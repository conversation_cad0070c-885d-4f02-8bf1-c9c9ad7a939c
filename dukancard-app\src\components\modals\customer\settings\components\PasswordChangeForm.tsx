import React, { useState } from "react";
import { View } from "react-native";
import { useF<PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as Yup from "yup";
import { Input } from "@/src/components/ui/Input";
import { Button } from "@/src/components/ui/Button";
import { useTheme } from "@/src/hooks/useTheme";
import { createSettingsModalStyles } from "@/styles/modals/customer/settings-modal";
import { supabase } from "@/lib/supabase";
import { useToast } from "@/src/components/ui/Toast";

const PasswordSchema = Yup.object().shape({
  currentPassword: Yup.string().required("Current password is required"),
  newPassword: Yup.string()
    .min(8, "Password must be at least 8 characters")
    .required("New password is required"),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref("newPassword")], "Passwords must match")
    .required("Confirm password is required"),
});

type PasswordFormData = Yup.InferType<typeof PasswordSchema>;

interface PasswordChangeFormProps {
  onPasswordChanged: () => void;
}

const PasswordChangeForm: React.FC<PasswordChangeFormProps> = ({
  onPasswordChanged,
}) => {
  const theme = useTheme();
  const styles = createSettingsModalStyles(theme);
  const [isPending, setIsPending] = useState(false);
  const toast = useToast();

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<PasswordFormData>({
    resolver: yupResolver(PasswordSchema),
  });

  const onPasswordSubmit = async (data: PasswordFormData) => {
    setIsPending(true);
    const { error } = await supabase.auth.updateUser({
      password: data.newPassword,
    });
    setIsPending(false);

    if (error) {
      toast.error("Error", error.message);
    } else {
      toast.success("Success", "Password updated successfully!");
      onPasswordChanged();
    }
  };

  return (
    <View style={styles.formContainer}>
      <Controller
        control={control}
        name="currentPassword"
        render={({ field: { onChange, onBlur, value } }) => (
          <Input
            label="Current Password"
            onBlur={onBlur}
            onChangeText={onChange}
            value={value}
            secureTextEntry
            error={errors.currentPassword?.message}
            editable={!isPending}
            style={styles.input}
          />
        )}
      />
      <Controller
        control={control}
        name="newPassword"
        render={({ field: { onChange, onBlur, value } }) => (
          <Input
            label="New Password"
            onBlur={onBlur}
            onChangeText={onChange}
            value={value}
            secureTextEntry
            error={errors.newPassword?.message}
            editable={!isPending}
            style={styles.input}
          />
        )}
      />
      <Controller
        control={control}
        name="confirmPassword"
        render={({ field: { onChange, onBlur, value } }) => (
          <Input
            label="Confirm New Password"
            onBlur={onBlur}
            onChangeText={onChange}
            value={value}
            secureTextEntry
            error={errors.confirmPassword?.message}
            editable={!isPending}
            style={styles.input}
          />
        )}
      />
      <Button
        title="Change Password"
        onPress={handleSubmit(onPasswordSubmit)}
        loading={isPending}
        style={styles.button}
      />
    </View>
  );
};

export default PasswordChangeForm;
