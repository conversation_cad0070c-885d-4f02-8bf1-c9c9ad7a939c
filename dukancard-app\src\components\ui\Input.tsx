import { useTheme } from '@/src/hooks/useTheme';
import { Eye, EyeOff } from 'lucide-react-native';
import React, { useState } from 'react';
import {
    Text,
    TextInput,
    TextInputProps,
    TouchableOpacity,
    View,
    ViewStyle,
} from 'react-native';

export interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  containerStyle?: ViewStyle;
  type?: 'text' | 'email' | 'password' | 'phone' | 'numeric';
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  isRequired?: boolean;
  helperText?: string;
  onPressRightIcon?: () => void;
}

export function Input({
  label,
  error,
  containerStyle,
  type = 'text',
  leftIcon,
  rightIcon,
  isRequired = false,
  helperText,
  onPressRightIcon,
  ...textInputProps
}: InputProps) {
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const theme = useTheme();

  const getKeyboardType = () => {
    switch (type) {
      case 'email':
        return 'email-address';
      case 'phone':
        return 'phone-pad';
      case 'numeric':
        return 'numeric';
      default:
        return 'default';
    }
  };

  const getSecureTextEntry = () => {
    if (type === 'password') {
      return !isPasswordVisible;
    }
    return false;
  };

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  const iconColor = theme.isDark ? '#A6A6A6' : '#808080';
  const placeholderColor = theme.isDark ? '#A6A6A6' : '#808080';

  return (
    <View style={[{ marginBottom: theme.spacing.sm }, containerStyle]}>
      {label && (
        <Text
          style={{
            color: theme.colors.textPrimary,
            fontSize: theme.typography.fontSize.sm,
            fontWeight: '600',
            marginBottom: theme.spacing.xs,
            letterSpacing: 0.5,
            paddingHorizontal: theme.spacing.xs, // Minimal horizontal padding
          }}
        >
          {label}
          {isRequired && (
            <Text style={{ color: theme.colors.error }}> *</Text>
          )}
        </Text>
      )}
      <View style={{ position: 'relative' }}>
        {leftIcon && (
          <View style={{
            position: 'absolute',
            left: theme.spacing.sm,
            top: 14, // Adjusted for reduced input height
            zIndex: 1,
          }}>
            {leftIcon}
          </View>
        )}
        {rightIcon && (
          <TouchableOpacity
            style={{
              position: 'absolute',
              right: theme.spacing.sm,
              top: 14,
              zIndex: 1,
            }}
            onPress={onPressRightIcon}
            disabled={!onPressRightIcon} // Disable if no handler is provided
          >
            {rightIcon}
          </TouchableOpacity>
        )}
        <TextInput
          style={[
            {
              borderRadius: 12,
              paddingLeft: leftIcon ? 40 : theme.spacing.sm,
              paddingRight: rightIcon || type === 'password' ? 42 : theme.spacing.sm,
              paddingVertical: 0,
              fontSize: theme.typography.fontSize.base,
              fontWeight: 'normal',
              letterSpacing: 0.5,
              height: 48, // Reduced height for more compact look
              textAlignVertical: 'center',
              backgroundColor: 'transparent',
              color: textInputProps.editable === false
                ? (theme.isDark ? '#a6a6a6' : '#666666')
                : theme.colors.textPrimary,
              borderWidth: error ? 2 : 1,
              borderColor: error
                ? theme.colors.error // Changed from theme.colors.danger
                : isFocused
                  ? theme.brandColors.gold
                  : theme.isDark
                    ? theme.colors.border
                    : theme.colors.border
            },
            textInputProps.style
          ]}
          placeholderTextColor={textInputProps.placeholderTextColor || placeholderColor}
          keyboardType={getKeyboardType()}
          secureTextEntry={getSecureTextEntry()}
          autoCapitalize={type === 'email' ? 'none' : 'sentences'}
          autoCorrect={type !== 'email' && type !== 'password'}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          {...textInputProps}
        />
        {type === 'password' && (
          <TouchableOpacity
            style={{
              position: 'absolute',
              right: 16,
              top: 0,
              bottom: 0,
              width: 32,
              justifyContent: 'center',
              alignItems: 'center',
            }}
            onPress={togglePasswordVisibility}
          >
            {isPasswordVisible ? (
              <EyeOff size={20} color={iconColor} testID="eyeoff-icon" />
            ) : (
              <Eye size={20} color={iconColor} testID="eye-icon" />
            )}
          </TouchableOpacity>
        )}
      </View>
      {error && (
        <Text
          style={{
            color: theme.colors.error, // Changed from theme.colors.danger
            fontSize: 14,
            fontWeight: '500',
            marginTop: 6
          }}
        >
          {error}
        </Text>
      )}
      {!error && helperText && (
        <Text
          style={{
            color: theme.colors.textSecondary,
            fontSize: 12,
            marginTop: 4,
            paddingHorizontal: theme.spacing.xs,
          }}
        >
          {helperText}
        </Text>
      )}
    </View>
  );
}
