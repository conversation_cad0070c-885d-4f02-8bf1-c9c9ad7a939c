import React from 'react';
import { render } from '@testing-library/react-native';
import { GoogleIcon } from '../../../../src/components/ui/GoogleIcon';

describe('<GoogleIcon />', () => {
  it('renders correctly with default props', () => {
    const { toJSON } = render(<GoogleIcon />);
    expect(toJSON()).toMatchSnapshot();
  });

  it('applies different size prop values', () => {
    const { toJSON: small } = render(<GoogleIcon size={16} />);
    expect(small()).toMatchSnapshot();

    const { toJSON: medium } = render(<GoogleIcon size={24} />);
    expect(medium()).toMatchSnapshot();

    const { toJSON: large } = render(<GoogleIcon size={32} />);
    expect(large()).toMatchSnapshot();
  });

  it('applies custom style prop', () => {
    const customStyle = { backgroundColor: 'blue' };
    const { toJSON } = render(<GoogleIcon style={customStyle} />);
    expect(toJSON()).toMatchSnapshot();
  });
});
