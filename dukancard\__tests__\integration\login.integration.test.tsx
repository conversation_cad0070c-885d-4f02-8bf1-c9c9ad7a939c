import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { useRouter, useSearchParams } from 'next/navigation';
import { LoginForm } from '@/app/(main)/login/LoginForm';
import { sendOTP, verifyOTP, loginWithMobilePassword } from '@/app/(main)/login/actions';
import { createClient } from '@/utils/supabase/client';
import { getPostLoginRedirectPath } from '@/lib/actions/redirectAfterLogin';
import { toast } from 'sonner';

// Mock child components
jest.mock('@/app/(main)/login/components/EmailOTPForm', () => ({
  EmailOTPForm: jest.fn(({ onEmailSubmit, onOTPSubmit, step }) => (
    <div>
      {step === 'email' ? (
        <button onClick={() => onEmailSubmit({ email: '<EMAIL>' })}>Submit Email</button>
      ) : (
        <button onClick={() => onOTPSubmit({ email: '<EMAIL>', otp: '123456' })}>Submit OTP</button>
      )}
    </div>
  )),
}));

jest.mock('@/app/(main)/login/components/MobilePasswordForm', () => ({
  MobilePasswordForm: jest.fn(({ onSubmit }) => (
    <button onClick={() => onSubmit({ mobile: '1234567890', password: 'password123' })}>Submit Mobile/Password</button>
  )),
}));

// Mock Next.js navigation hooks
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(() => ({
    get: jest.fn((param) => {
      if (param === 'redirect') return null;
      if (param === 'message') return null;
      return null;
    }),
    entries: jest.fn(() => []), // Mock entries for Object.fromEntries
  })),
}));

// Mock Supabase client
const mockSignInWithOAuth = jest.fn();
jest.mock('@/utils/supabase/client', () => ({
  createClient: jest.fn(() => ({
    auth: {
      getUser: jest.fn(() => Promise.resolve({ data: { user: { id: 'test-user-id' } }, error: null })),
      signInWithOAuth: mockSignInWithOAuth,
    },
  })),
}));

// Mock server actions
jest.mock('@/app/(main)/login/actions', () => ({
  sendOTP: jest.fn(),
  verifyOTP: jest.fn(),
  loginWithMobilePassword: jest.fn(),
}));

// Mock redirectAfterLogin action
jest.mock('@/lib/actions/redirectAfterLogin', () => ({
  getPostLoginRedirectPath: jest.fn(() => Promise.resolve('/dashboard/customer')),
}));

// Mock sonner toast
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
  },
}));

describe('Login Page Integration Tests', () => {
  const mockPush = jest.fn();
  const mockWindowOpen = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    (useRouter as jest.Mock).mockReturnValue({ push: mockPush });
    Object.defineProperty(window, 'open', { value: mockWindowOpen, writable: true });
    jest.useFakeTimers();
  });

  afterEach(() => {
    act(() => {
      jest.runOnlyPendingTimers();
    });
    jest.useRealTimers();
  });

  it('should allow email OTP login and redirect to dashboard', async () => {
    // Mock successful OTP send and verification
    (sendOTP as jest.Mock).mockResolvedValue({ success: true, message: 'OTP sent!' });
    (verifyOTP as jest.Mock).mockResolvedValue({ success: true });
    (getPostLoginRedirectPath as jest.Mock).mockResolvedValue('/dashboard/customer');

    await act(async () => {
      render(<LoginForm />);
    });

    // Simulate email submission by clicking the mocked button
    await act(async () => {
      fireEvent.click(screen.getByRole('button', { name: 'Submit Email' }));
    });

    await waitFor(() => {
      expect(sendOTP).toHaveBeenCalledWith({ email: '<EMAIL>' });
      expect(screen.getByText('Enter Verification Code')).toBeInTheDocument();
    });

    // Simulate OTP submission by clicking the mocked button
    await act(async () => {
      fireEvent.click(screen.getByRole('button', { name: 'Submit OTP' }));
    });

    await waitFor(() => {
      expect(verifyOTP).toHaveBeenCalledWith({ email: '<EMAIL>', otp: '123456' });
      expect(toast.success).toHaveBeenCalledWith('Sign in successful!', { description: 'Redirecting to your dashboard...' });
      expect(mockPush).toHaveBeenCalledWith('/dashboard/customer');
    });
  });

  it('should allow mobile/password login and redirect to dashboard', async () => {
    // Mock successful mobile/password login
    (loginWithMobilePassword as jest.Mock).mockResolvedValue({ success: true });
    (getPostLoginRedirectPath as jest.Mock).mockResolvedValue('/dashboard/business');

    await act(async () => {
      render(<LoginForm />);
    });

    // Switch to mobile/password method
    await act(async () => {
      fireEvent.click(screen.getByRole('button', { name: 'Mobile + Password' }));
    });

    // Simulate mobile/password submission
    await act(async () => {
      fireEvent.click(screen.getByRole('button', { name: 'Submit Mobile/Password' }));
    });

    await waitFor(() => {
      expect(loginWithMobilePassword).toHaveBeenCalledWith({ mobile: '1234567890', password: 'password123' });
      expect(toast.success).toHaveBeenCalledWith('Sign in successful!', { description: 'Redirecting to your dashboard...' });
      expect(mockPush).toHaveBeenCalledWith('/dashboard/business');
    });
  });

  it('should handle social login and redirect to choose-role for new users', async () => {
    // Mock Supabase social login to return a URL
    mockSignInWithOAuth.mockResolvedValue({ data: { url: 'https://mock-auth-url.com' }, error: null });
    (getPostLoginRedirectPath as jest.Mock).mockResolvedValue('/choose-role'); // Simulate new user

    await act(async () => {
      render(<LoginForm />);
    });

    await act(async () => {
      fireEvent.click(screen.getByRole('button', { name: /Login with Google/i }));
    });

    await waitFor(() => {
      expect(mockSignInWithOAuth).toHaveBeenCalled();
      expect(mockWindowOpen).toHaveBeenCalledWith('https://mock-auth-url.com', '_blank');
    });
  });

  it('should display error toast on failed login attempt', async () => {
    // Mock failed login attempt
    (sendOTP as jest.Mock).mockResolvedValue({ success: false, error: 'Invalid credentials' });

    await act(async () => {
      render(<LoginForm />);
    });

    await act(async () => {
      fireEvent.click(screen.getByRole('button', { name: 'Submit Email' }));
    });

    await waitFor(() => {
      expect(toast.error).toHaveBeenCalledWith('Failed to send OTP', { description: 'Invalid credentials' });
      expect(mockPush).not.toHaveBeenCalled();
    });
  });

  it('should handle redirect slug from URL after successful login', async () => {
    // Mock successful OTP send and verification
    (sendOTP as jest.Mock).mockResolvedValue({ success: true, message: 'OTP sent!' });
    (verifyOTP as jest.Mock).mockResolvedValue({ success: true });

    // Mock useSearchParams to return a redirect slug
    (useRouter as jest.Mock).mockReturnValue({ push: mockPush });
    (require('next/navigation').useSearchParams as jest.Mock).mockReturnValue({
      get: jest.fn((param) => {
        if (param === 'redirect') return 'my-custom-page';
        if (param === 'message') return 'WelcomeBack';
        return null;
      }),
      entries: jest.fn(() => []), // Mock entries for Object.fromEntries
    });

    await act(async () => {
      render(<LoginForm />);
    });

    // Simulate email submission
    await act(async () => {
      fireEvent.click(screen.getByRole('button', { name: 'Submit Email' }));
    });

    await waitFor(() => {
      expect(sendOTP).toHaveBeenCalledWith({ email: '<EMAIL>' });
    });

    // Simulate OTP submission
    await act(async () => {
      fireEvent.click(screen.getByRole('button', { name: 'Submit OTP' }));
    });

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/my-custom-page?message=WelcomeBack');
    });
  });
});