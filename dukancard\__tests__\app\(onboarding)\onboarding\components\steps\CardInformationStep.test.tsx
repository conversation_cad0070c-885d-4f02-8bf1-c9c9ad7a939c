import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { CardInformationStep } from '@/app/(onboarding)/onboarding/components/steps/CardInformationStep';

// Mock lucide-react icons
jest.mock('lucide-react', () => ({
  User: ({ className }: { className: string }) => <svg data-testid="user-icon" className={className} />,
  Briefcase: ({ className }: { className: string }) => <svg data-testid="briefcase-icon" className={className} />,
  Phone: ({ className }: { className: string }) => <svg data-testid="phone-icon" className={className} />,
  Link: ({ className }: { className: string }) => <svg data-testid="link-icon" className={className} />,
  Loader2: ({ className }: { className: string }) => <svg data-testid="loader-icon" className={className} />,
  AlertCircle: ({ className }: { className: string }) => <svg data-testid="alert-circle-icon" className={className} />,
  CheckCircle2: ({ className }: { className: string }) => <svg data-testid="check-circle-icon" className={className} />,
  Check: ({ className }: { className: string }) => <svg data-testid="check-icon" className={className} />,
}));

// Explicitly mock react-hook-form
jest.mock('react-hook-form', () => ({
  useForm: () => ({
    control: {},
    handleSubmit: jest.fn(),
    register: jest.fn(),
    setValue: jest.fn(),
    formState: { errors: {} },
    trigger: jest.fn(),
    setError: jest.fn(),
    clearErrors: jest.fn(),
    getValues: jest.fn(),
  }),
  FormProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>, // Simple passthrough
}));

// Mock UI components
jest.mock('@/components/ui/form', () => ({
  FormControl: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  FormItem: ({ children }: { children: React.ReactNode }) => <div data-testid="form-item">{children}</div>,
  FormLabel: ({ children, htmlFor, formLabelHtmlFor }: { children: React.ReactNode, htmlFor?: string, formLabelHtmlFor?: string }) => <label data-testid="form-label" htmlFor={formLabelHtmlFor || htmlFor}>{children}</label>,
  FormMessage: ({ children }: { children: React.ReactNode }) => <div data-testid="form-message">{children}</div>,
  FormField: ({ render, name }: { render: ({ field }: any) => React.ReactNode, name: string }) => {
    const mockField = {
      name: name,
      value: '',
      onChange: jest.fn(),
      onBlur: jest.fn(),
    };
    return <div data-testid="form-field">{render({ field: mockField, formLabelHtmlFor: name })}</div>;
  },
}));

jest.mock('@/components/ui/input', () => ({
  Input: ({ className, placeholder, type, pattern, inputMode, onChange, onKeyDown, disabled, name, ...props }: any) => (
    <input
      className={className}
      placeholder={placeholder}
      type={type}
      pattern={pattern}
      inputMode={inputMode}
      onChange={onChange}
      onKeyDown={onKeyDown}
      disabled={disabled}
      name={name}
      id={name}
      {...props}
    />
  ),
}));

jest.mock('@/components/ui/category-combobox', () => ({
  CategoryCombobox: ({ value, onChange, placeholder, disabled, className }: any) => (
    <input
      data-testid="category-combobox-mock"
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
      disabled={disabled}
      className={className}
    />
  ),
}));

describe('CardInformationStep', () => {
  const mockSetSlugToCheck = jest.fn();
  const mockSetSlugAvailable = jest.fn();

  const mockForm = {
    control: {},
    handleSubmit: jest.fn(),
    register: jest.fn(),
    setValue: jest.fn(),
    formState: { errors: {} },
    trigger: jest.fn(),
    setError: jest.fn(),
    clearErrors: jest.fn(),
    getValues: jest.fn(),
  };

  const defaultProps = {
    form: mockForm,
    isSubmitting: false,
    slugAvailable: null,
    isCheckingSlug: false,
    setSlugToCheck: mockSetSlugToCheck,
    setSlugAvailable: mockSetSlugAvailable,
    user: null,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all form fields correctly', () => {
    render(<CardInformationStep {...defaultProps} />);

    expect(screen.getByLabelText(/Your Name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Your Title\/Designation/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Primary Phone Number/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Business Category/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Desired Card URL/i)).toBeInTheDocument();
  });
});