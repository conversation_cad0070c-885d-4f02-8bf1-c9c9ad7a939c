import React from 'react';
import { render, screen } from '@testing-library/react';
import LoginPage, { generateMetadata } from '@/app/(main)/login/page';

// Mock the LoginForm component
jest.mock('@/app/(main)/login/LoginForm', () => ({
  LoginForm: () => <div data-testid="mock-login-form">Mock LoginForm</div>,
}));

// Mock the AuthPageBackground component
jest.mock('@/app/(main)/components/auth/AuthPageBackground', () => ({
  __esModule: true,
  default: () => <div data-testid="mock-auth-page-background">Mock AuthPageBackground</div>,
}));

describe('LoginPage', () => {
  it('renders the LoginForm component', async () => {
    render(<LoginPage />);
    expect(screen.getByTestId('mock-login-form')).toBeInTheDocument();
  });

  it('renders the AuthPageBackground component', () => {
    render(<LoginPage />);
    expect(screen.getByTestId('mock-auth-page-background')).toBeInTheDocument();
  });

  it('renders the Suspense fallback initially', () => {
    // We can't directly test the Suspense fallback in a simple render
    // without more advanced testing utilities that simulate Suspense boundaries.
    // However, we can ensure the main content is eventually rendered.
    render(<LoginPage />);
    expect(screen.getByTestId('mock-login-form')).toBeInTheDocument();
  });

  describe('generateMetadata', () => {
    it('should return correct metadata', async () => {
      const metadata = await generateMetadata();
      expect(metadata.title).toBe('Sign In');
      expect(metadata.description).toBe('Sign in to your Dukancard account or create a new account with just your email address.');
      expect(metadata.robots).toBe('noindex, follow');
    });
  });
});
