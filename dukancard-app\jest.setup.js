/* eslint-env jest */
import React from "react";
import "@testing-library/jest-native/extend-expect";

// Mock react-native-url-polyfill
jest.mock("react-native-url-polyfill/auto", () => {});

// Mock Expo modules
jest.mock("expo-constants", () => ({
  expoConfig: {
    extra: {
      supabaseUrl: "mock-supabase-url",
      supabaseAnonKey: "mock-supabase-anon-key",
    },
  },
}));

jest.mock('expo-modules-core', () => ({
  NativeModulesProxy: {},
  requireNativeViewManager: jest.fn(),
}));

jest.mock('expo-blur', () => ({
  BlurView: 'View',
}));

jest.mock("expo-router", () => ({
  router: {
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    canGoBack: jest.fn(() => true),
  },
  useRouter: jest.fn(() => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
    canGoBack: jest.fn(() => true),
  })),
  useLocalSearchParams: jest.fn(() => ({})),
  useSegments: jest.fn(() => []),
  usePathname: jest.fn(() => "/"),
}));

jest.mock("expo-status-bar", () => ({
  StatusBar: "StatusBar",
}));

jest.mock("expo-web-browser", () => ({
  openBrowserAsync: jest.fn(() => Promise.resolve({ type: 'cancel' })),
  dismissBrowser: jest.fn(() => Promise.resolve()),
  mayInitWithUrlAsync: jest.fn(() => Promise.resolve()),
  warmUpAsync: jest.fn(() => Promise.resolve()),
  coolDownAsync: jest.fn(() => Promise.resolve()),
}));

// Mock Expo Secure Store
let mockSecureStore = {};

jest.mock("expo-secure-store", () => ({
  getItemAsync: jest.fn((key) => Promise.resolve(mockSecureStore[key] || null)),
  setItemAsync: jest.fn((key, value) => {
    mockSecureStore[key] = value;
    return Promise.resolve();
  }),
  deleteItemAsync: jest.fn((key) => {
    delete mockSecureStore[key];
    return Promise.resolve();
  }),
}));

jest.mock("expo-location", () => ({
  PermissionStatus: {
    GRANTED: "granted",
    UNDETERMINED: "undetermined",
    DENIED: "denied",
  },
  requestForegroundPermissionsAsync: jest.fn(() =>
    Promise.resolve({
      status: "granted",
      canAskAgain: true,
      granted: true,
      expires: "never",
    })
  ),
  getForegroundPermissionsAsync: jest.fn(() =>
    Promise.resolve({
      status: "granted",
      canAskAgain: true,
      granted: true,
      expires: "never",
    })
  ),
  getCurrentPositionAsync: jest.fn(() =>
    Promise.resolve({
      coords: {
        latitude: 28.6139,
        longitude: 77.209,
      },
    })
  ),
  reverseGeocodeAsync: jest.fn(() =>
    Promise.resolve([
      {
        postalCode: "110001",
        city: "New Delhi",
        region: "Delhi",
      },
    ])
  ),
  hasServicesEnabledAsync: jest.fn(() => Promise.resolve(true)),
  Accuracy: {
    Balanced: 3,
  },
}));

jest.mock("@react-native-google-signin/google-signin", () => ({
  GoogleSignin: {
    configure: jest.fn(),
    hasPlayServices: jest.fn(() => Promise.resolve(true)),
    signIn: jest.fn(() =>
      Promise.resolve({
        user: {
          id: "mock-user-id",
          name: "Mock User",
          email: "<EMAIL>",
        },
        idToken: "mock-id-token",
      })
    ),
    signOut: jest.fn(() => Promise.resolve()),
    isSignedIn: jest.fn(() => Promise.resolve(false)),
    getCurrentUser: jest.fn(() => Promise.resolve(null)),
  },
}));

// Mock Supabase client
const mockSupabase = {
  auth: {
    signInWithIdToken: jest.fn().mockResolvedValue({ data: null, error: null }),
    signOut: jest.fn().mockResolvedValue({ error: null }),
    getSession: jest
      .fn()
      .mockResolvedValue({ data: { session: null }, error: null }),
    onAuthStateChange: jest.fn(() => ({
      data: { subscription: { unsubscribe: jest.fn() } },
    })),
    getUser: jest.fn().mockResolvedValue({ data: { user: null }, error: null }),
  },
  from: jest.fn(() => mockSupabase.from),
  select: jest.fn(() => mockSupabase.from),
  insert: jest.fn(() => mockSupabase.from),
  update: jest.fn(() => mockSupabase.from),
  delete: jest.fn(() => mockSupabase.from),
  eq: jest.fn(() => mockSupabase.from),
  ilike: jest.fn(() => mockSupabase.from),
  in: jest.fn(() => mockSupabase.from),
  order: jest.fn(() => mockSupabase.from),
  range: jest.fn(() => mockSupabase.from),
  single: jest.fn().mockResolvedValue({ data: null, error: null }),
  limit: jest.fn(() => mockSupabase.from),
  count: jest.fn().mockResolvedValue({ count: 0, error: null }),
  rpc: jest.fn().mockResolvedValue({ data: null, error: null }),
};

jest.mock("@supabase/supabase-js", () => ({
  createClient: jest.fn(() => mockSupabase),
}));

// Mock React Native modules
jest.mock("react-native", () => {
  const React = require('react');
  const { View, Text, ScrollView } = jest.requireActual('react-native');
  // Ensure StyleSheet is properly mocked
  const mockStyleSheet = {
    create: jest.fn((styles) => styles),
    flatten: jest.fn((style) => style),
    compose: jest.fn((style1, style2) => [style1, style2]),
    hairlineWidth: 1,
    absoluteFill: {
      position: "absolute",
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
    },
    absoluteFillObject: {
      position: "absolute",
      left: 0,
      right: 0,
      top: 0,
      bottom: 0,
    },
  };

  return {
    StyleSheet: mockStyleSheet,
    Alert: {
      alert: jest.fn(),
    },
    Platform: {
      OS: "android",
      select: jest.fn((obj) => obj.android || obj.default),
    },
    Dimensions: {
      get: jest.fn(() => ({ width: 375, height: 812 })),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    },
    AppState: {
      currentState: "active",
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
    },
    Text: "Text",
    View: "View",
    SafeAreaView: "SafeAreaView",
    KeyboardAvoidingView: "KeyboardAvoidingView",
    ScrollView: "ScrollView",
    TouchableOpacity: "TouchableOpacity",
    TouchableHighlight: "TouchableHighlight",
    TouchableWithoutFeedback: "TouchableWithoutFeedback",
    Pressable: "Pressable",
    Image: "Image",
    TextInput: "TextInput",
    FlatList: "FlatList",
    SectionList: "SectionList",
    ActivityIndicator: "ActivityIndicator",
    Modal: "Modal",
    useColorScheme: jest.fn(() => 'light'),
    Animated: {
      View: jest.fn(({ children, ...props }) => React.createElement(View, props, children)),
      Text: jest.fn(({ children, ...props }) => React.createElement(Text, props, children)),
      ScrollView: jest.fn(({ children, ...props }) => React.createElement(ScrollView, props, children)),
      Value: jest.fn(() => ({
        setValue: jest.fn(),
        addListener: jest.fn(),
        removeListener: jest.fn(),
        removeAllListeners: jest.fn(),
        interpolate: jest.fn(),
      })),
      timing: jest.fn(() => ({
        start: jest.fn(),
        stop: jest.fn(),
        reset: jest.fn(),
      })),
      spring: jest.fn(() => ({
        start: jest.fn(),
        stop: jest.fn(),
        reset: jest.fn(),
      })),
      sequence: jest.fn(),
      parallel: jest.fn(),
      stagger: jest.fn(),
      loop: jest.fn(),
      createAnimatedComponent: jest.fn((Component) => Component),
    },
    Easing: {
      linear: jest.fn(),
      ease: jest.fn(),
      quad: jest.fn(),
      cubic: jest.fn(),
      poly: jest.fn(),
      sin: jest.fn(),
      circle: jest.fn(),
      exp: jest.fn(),
      elastic: jest.fn(),
      back: jest.fn(),
      bounce: jest.fn(),
      bezier: jest.fn(),
      in: jest.fn(),
      out: jest.fn(),
      inOut: jest.fn(),
    },
    Keyboard: {
      dismiss: jest.fn(),
      addListener: jest.fn(),
      removeListener: jest.fn(),
      removeAllListeners: jest.fn(),
    },
  };
});

jest.mock("@/lib/theme/colors", () => ({
  SCREEN_WIDTH: 375,
  BASE_WIDTH: 375,
}));

jest.mock("@expo/vector-icons", () => ({
  Ionicons: "Ionicons",
  MaterialCommunityIcons: "MaterialCommunityIcons",
  FontAwesome: "FontAwesome",
  // Add other icon sets as needed
}));

// Mock safe area context
jest.mock("react-native-safe-area-context", () => ({
  SafeAreaProvider: ({ children }) => children,
  SafeAreaView: ({ children }) => children,
  useSafeAreaInsets: () => ({ top: 0, bottom: 0, left: 0, right: 0 }),
}));

// Mock Async Storage
let mockAsyncStorage = {};

jest.mock("@react-native-async-storage/async-storage", () => ({
  getItem: jest.fn((key) => Promise.resolve(mockAsyncStorage[key] || null)),
  setItem: jest.fn((key, value) => {
    mockAsyncStorage[key] = value;
    return Promise.resolve();
  }),
  removeItem: jest.fn((key) => {
    delete mockAsyncStorage[key];
    return Promise.resolve();
  }),
  clear: jest.fn(() => {
    mockAsyncStorage = {};
    return Promise.resolve();
  }),
}));

// Mock NetInfo
jest.mock("@/src/utils/networkStatus", () => ({
  isOnline: jest.fn(() => Promise.resolve(true)),
  isInternetReachable: jest.fn(() => Promise.resolve(true)),
  useNetworkStatus: jest.fn(() => ({
    isConnected: true,
    isInternetReachable: true,
    type: 'wifi',
  })),
}));







// react-native-svg is mocked via __mocks__/react-native-svg.js

// Mock lucide-react-native
jest.mock("lucide-react-native", () => {
  const mockReact = require("react");
  const { View } = require('react-native');

  const createMockIcon = (name) => {
    return mockReact.forwardRef(({ size, color, testID, ...props }, ref) => (
      mockReact.createElement(View, { testID: testID || `${name.toLowerCase()}-icon`, style: { width: size, height: size, backgroundColor: color }, ...props, ref }, name)
    ));
  };

  return {
    Mail: createMockIcon('Mail'),
    Lock: createMockIcon('Lock'),
    Eye: createMockIcon('Eye'),
    EyeOff: createMockIcon('EyeOff'),
    Phone: createMockIcon('Phone'),
    X: createMockIcon('X'),
    ArrowLeft: createMockIcon('ArrowLeft'),
    Heart: createMockIcon('Heart'),
    Share2: createMockIcon('Share2'),
    MessageCircle: createMockIcon('MessageCircle'),
    User: createMockIcon('User'),
    Briefcase: createMockIcon('Briefcase'),
    ChevronRight: createMockIcon('ChevronRight'),
    ShoppingBag: createMockIcon('ShoppingBag'),
    Star: createMockIcon('Star'),
    MapPin: createMockIcon('MapPin'),
    Package: createMockIcon('Package'),
    MoreVertical: createMockIcon('MoreVertical'),
  };
});

// Mock Discovery Service
jest.mock("@/services/discovery/DiscoveryService", () => ({
  DiscoveryService: {
    getBusinessProfiles: jest.fn(),
    getBusinessProfile: jest.fn(),
    getProducts: jest.fn(),
    // Add other methods as needed
  },
}));

jest.mock("@/src/hooks/useTheme", () => ({
  useTheme: () => ({
    colors: {
      primary: "#D4AF37",
      background: "#FFFFFF",
      cardBackground: "#F0F0F0",
      border: "#E5E5E5",
      textPrimary: "#000000",
      textSecondary: "#666666",
      destructive: "#FF0000",
      muted: "#F5F5F5",
      error: "#FF0000",
    },
    isDark: false,
    isLight: true,
    colorScheme: 'light',
    spacing: {
      xs: 4,
      sm: 8,
      md: 16,
      lg: 24,
      xl: 32,
      xxl: 48,
      xxxl: 64,
    },
    typography: {
      fontSize: {
        xs: 12,
        sm: 14,
        base: 16,
        lg: 18,
        xl: 20,
        xxl: 24,
      },
      fontWeight: {
        normal: '400',
        medium: '500',
        semibold: '600',
        bold: '700',
      },
      lineHeight: {
        normal: 1.4,
        relaxed: 1.6,
      },
    },
    borderRadius: {
      sm: 4,
      md: 8,
      lg: 12,
      xl: 16,
    },
    shadows: {
      sm: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
        elevation: 1,
      },
      md: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 4,
        elevation: 2,
      },
    },
    animations: {},
    breakpoints: {},
    brandColors: {},
    getColor: jest.fn(),
    getSpacing: jest.fn(),
    getShadow: jest.fn(),
    getBorderRadius: jest.fn(),
    getFontSize: jest.fn(),
    getFontWeight: jest.fn(),
    getLineHeight: jest.fn(),
  }),
}));

// Global test environment setup
global.__DEV__ = true;

// Reset all mocks before each test
beforeEach(() => {
  // Clear storage mocks
  mockAsyncStorage = {};
  mockSecureStore = {};

  // Reset Supabase mock
  Object.keys(mockSupabase).forEach((key) => {
    if (
      typeof mockSupabase[key] === "function" &&
      mockSupabase[key].mockClear
    ) {
      mockSupabase[key].mockClear();
    }
  });
  Object.keys(mockSupabase.auth).forEach((key) => {
    if (
      typeof mockSupabase.auth[key] === "function" &&
      mockSupabase.auth[key].mockClear
    ) {
      mockSupabase.auth[key].mockClear();
    }
  });

  // Restore default mock implementations
  mockSupabase.from.mockImplementation(() => mockSupabase.from);
  mockSupabase.auth.getSession.mockResolvedValue({
    data: { session: null },
    error: null,
  });
  mockSupabase.auth.getUser.mockResolvedValue({
    data: { user: null },
    error: null,
  });
  mockSupabase.auth.onAuthStateChange.mockReturnValue({
    data: { subscription: { unsubscribe: jest.fn() } },
  });
  mockSupabase.single.mockResolvedValue({ data: null, error: null });
  mockSupabase.count.mockResolvedValue({ count: 0, error: null });
  mockSupabase.rpc.mockResolvedValue({ data: null, error: null });
});

// Silence console warnings during tests
const originalWarn = console.warn;
console.warn = (...args) => {
  if (
    typeof args[0] === "string" &&
    args[0].includes("Warning: ReactDOM.render is no longer supported")
  ) {
    return;
  }
  originalWarn.call(console, ...args);
};

// Note: Fake timers removed as they interfere with async operations in tests
// Individual tests can use jest.useFakeTimers() if needed
