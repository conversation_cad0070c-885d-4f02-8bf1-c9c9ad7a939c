import { StyleSheet } from "react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { responsiveFontSize } from "@/lib/theme/colors";

export const createLikesModalStyles = (theme: ReturnType<typeof useTheme>) => {
  const { isDark } = theme;
  return StyleSheet.create({
    modalContainer: {
      flex: 1,
      backgroundColor: isDark ? "#000" : "#FFF",
    },
    safeArea: {
      flex: 1,
    },
    header: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: "600",
      color: isDark ? "#FFF" : "#000",
    },
    closeButton: {
      padding: theme.spacing.xs,
    },
    contentContainer: {
      flex: 1,
      padding: theme.spacing.md,
    },
    searchContainer: {
      marginBottom: theme.spacing.md,
    },
    listContainer: {
      flex: 1,
    },
    emptyContainer: {
      flex: 1,
      alignItems: "center",
      justifyContent: "center",
      padding: theme.spacing.xl,
    },
    emptyText: {
      fontSize: theme.typography.fontSize.base,
      color: theme.colors.textSecondary,
      textAlign: "center",
      marginTop: theme.spacing.md,
    },
    loadingContainer: {
      flex: 1,
      alignItems: "center",
      justifyContent: "center",
    },
    footerLoadingContainer: {
      paddingVertical: theme.spacing.lg,
    },
  });
};
