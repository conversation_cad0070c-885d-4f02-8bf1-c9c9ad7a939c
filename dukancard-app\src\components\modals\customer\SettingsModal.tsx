import React, { useState, useEffect } from "react";
import {
  Modal,
  View,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Text,
  TouchableOpacity,
  Linking,
} from "react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { createSettingsModalStyles } from "@/styles/modals/customer/settings-modal";
import { X, Mail, Phone, Lock, Trash2 } from "lucide-react-native";
import EmailLinkingModal from "./settings/EmailLinkingModal";
import PhoneLinkingModal from "./settings/PhoneLinkingModal";
import PasswordChangeModal from "./settings/PasswordChangeModal";
import DeleteAccountModal from "./DeleteAccountModal";
import { useAuth } from "@/src/contexts/AuthContext";
import SettingsModalSkeleton from "../../skeletons/modals/SettingsModalSkeleton";

interface SettingsModalProps {
  isVisible: boolean;
  onClose: () => void;
}

const SettingsModal: React.FC<SettingsModalProps> = ({
  isVisible,
  onClose,
}) => {
  const theme = useTheme();
  const styles = createSettingsModalStyles(theme);
  const { user, loading } = useAuth();

  const [emailModalVisible, setEmailModalVisible] = useState(false);
  const [phoneModalVisible, setPhoneModalVisible] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [deleteAccountModalVisible, setDeleteAccountModalVisible] = useState(false);

  const registrationType = user?.app_metadata?.provider;

  const handleDeleteAccount = () => {
    setDeleteAccountModalVisible(true);
  };

  if (loading) {
    return <SettingsModalSkeleton />;
  }

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={onClose}
    >
      <SafeAreaView
        style={{ flex: 1, backgroundColor: theme.colors.background }}
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={{ flex: 1 }}
        >
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
              padding: theme.spacing.md,
              borderBottomWidth: 1,
              borderBottomColor: theme.colors.border,
            }}
          >
            <View style={{ width: 40 }} />
            <Text
              style={{
                fontSize: theme.typography.fontSize.lg,
                fontWeight: "600",
                color: theme.colors.textPrimary,
              }}
            >
              Settings
            </Text>
            <TouchableOpacity onPress={onClose}>
              <X size={24} color={theme.colors.textPrimary} />
            </TouchableOpacity>
          </View>

          <View style={styles.container}>
            <TouchableOpacity
              style={styles.settingItem}
              onPress={() => setEmailModalVisible(true)}
            >
              <View style={styles.settingLeft}>
                <Mail
                  size={24}
                  color={theme.colors.textPrimary}
                  style={styles.settingIcon}
                />
                <View style={styles.settingContent}>
                  <Text style={styles.settingTitle}>Link Email</Text>
                  <Text style={styles.settingSubtitle}>{user?.email}</Text>
                </View>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.settingItem}
              onPress={() => setPhoneModalVisible(true)}
            >
              <View style={styles.settingLeft}>
                <Phone
                  size={24}
                  color={theme.colors.textPrimary}
                  style={styles.settingIcon}
                />
                <View style={styles.settingContent}>
                  <Text style={styles.settingTitle}>Link Phone</Text>
                  <Text style={styles.settingSubtitle}>
                    {user?.phone || "Not provided"}
                  </Text>
                </View>
              </View>
            </TouchableOpacity>

            {registrationType !== "google" && (
              <TouchableOpacity
                style={styles.settingItem}
                onPress={() => setPasswordModalVisible(true)}
              >
                <View style={styles.settingLeft}>
                  <Lock
                    size={24}
                    color={theme.colors.textPrimary}
                    style={styles.settingIcon}
                  />
                  <View style={styles.settingContent}>
                    <Text style={styles.settingTitle}>Change Password</Text>
                  </View>
                </View>
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={styles.settingItem}
              onPress={handleDeleteAccount}
            >
              <View style={styles.settingLeft}>
                <Trash2
                  size={24}
                  color={theme.colors.destructive}
                  style={styles.settingIcon}
                />
                <View style={styles.settingContent}>
                  <Text
                    style={[
                      styles.settingTitle,
                      { color: theme.colors.destructive },
                    ]}
                  >
                    Delete Account
                  </Text>
                </View>
              </View>
            </TouchableOpacity>
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>

      <EmailLinkingModal
        isVisible={emailModalVisible}
        onClose={() => setEmailModalVisible(false)}
        onEmailLinked={() => setEmailModalVisible(false)}
        currentEmail={user?.email}
      />

      <PhoneLinkingModal
        isVisible={phoneModalVisible}
        onClose={() => setPhoneModalVisible(false)}
        currentPhone={user?.phone}
      />

      <PasswordChangeModal
        isVisible={passwordModalVisible}
        onClose={() => setPasswordModalVisible(false)}
        onPasswordChanged={() => setPasswordModalVisible(false)}
      />

      <DeleteAccountModal
        visible={deleteAccountModalVisible}
        onClose={() => setDeleteAccountModalVisible(false)}
      />
    </Modal>
  );
};

export default SettingsModal;
