import React, { useState, useRef } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Image,
} from "react-native";
import { Controller, useFormContext } from "react-hook-form";
import { UploadCloud } from "lucide-react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { FormField } from "@/src/components/ui/FormField";
import { useToast } from "@/src/components/ui/Toast";
import ImagePickerBottomSheet, {
  ImagePickerBottomSheetRef,
} from "@/src/components/pickers/ImagePickerBottomSheet";
import {
  uploadAvatarImage,
  openCameraForAvatar,
  openGalleryForAvatar,
} from "@/backend/supabase/services/storage/avatarUploadService";
import { useAuth } from "@/src/contexts/AuthContext";
import { updateBasicInfo } from "@/backend/supabase/services/business/businessCardService";
import { BasicInfoData, basicInfoSchema } from "@/backend/supabase/services/business/schemas";
import { createBasicInfoSectionStyles } from "@/styles/modals/business/sections/basic-info-section";

interface BasicInfoSectionProps {
  onBack: () => void;
}

export default function BasicInfoSection({ onBack }: BasicInfoSectionProps) {
  const theme = useTheme();
  const styles = createBasicInfoSectionStyles(theme);
  const { user } = useAuth();
  const toast = useToast();
  const imagePickerRef = useRef<ImagePickerBottomSheetRef>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isImageUploading, setIsImageUploading] = useState(false);

  const { control, watch, setValue, handleSubmit } = useFormContext();
  const logoUrl = watch("logo_url");

  const handleImageSelect = async (imageUri: string) => {
    if (!user) return;
    setIsImageUploading(true);
    const result = await uploadAvatarImage(imageUri, user.id);
    if (result.success && result.url) {
      setValue("logo_url", result.url);
      toast.success("Success", "Logo uploaded successfully.");
    } else {
      toast.error("Upload Failed", "Could not upload the selected image.");
    }
    setIsImageUploading(false);
  };

  const handleCameraSelection = async () => {
    imagePickerRef.current?.dismiss();
    const result = await openCameraForAvatar();
    if (result && !result.canceled && result.assets?.[0]) {
      handleImageSelect(result.assets[0].uri);
    }
  };

  const handleGallerySelection = async () => {
    imagePickerRef.current?.dismiss();
    const result = await openGalleryForAvatar();
    if (result && !result.canceled && result.assets?.[0]) {
      handleImageSelect(result.assets[0].uri);
    }
  };

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    
    // Extract only basic info fields
    const basicInfoData: BasicInfoData = {
      business_name: data.business_name,
      member_name: data.member_name,
      title: data.title,
      logo_url: data.logo_url,
      business_category: data.business_category,
      established_year: data.established_year,
      about_bio: data.about_bio,
    };

    // Validate the data
    const validation = basicInfoSchema.safeParse(basicInfoData);
    if (!validation.success) {
      toast.error("Validation Error", "Please check all required fields.");
      setIsLoading(false);
      return;
    }

    // Convert null values to undefined for API compatibility
    const apiData = {
      ...basicInfoData,
      logo_url: basicInfoData.logo_url || undefined,
      business_category: basicInfoData.business_category || undefined,
      established_year: basicInfoData.established_year || undefined,
    };

    const { success, error } = await updateBasicInfo(apiData);
    if (success) {
      toast.success("Success", "Basic information updated successfully.");
      onBack();
    } else {
      toast.error("Error", error || "Failed to update basic information.");
    }
    setIsLoading(false);
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {/* Logo Upload Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Business Logo</Text>
          <View style={styles.logoContainer}>
            <TouchableOpacity
              onPress={() => imagePickerRef.current?.present()}
              style={styles.logoUploadArea}
              disabled={isImageUploading}
            >
              {logoUrl ? (
                <Image source={{ uri: logoUrl }} style={styles.logoImage} />
              ) : (
                <View style={styles.logoPlaceholder}>
                  {isImageUploading ? (
                    <ActivityIndicator size="large" color={theme.colors.primary} />
                  ) : (
                    <>
                      <UploadCloud size={40} color={theme.colors.textSecondary} />
                      <Text style={styles.logoPlaceholderText}>Upload Logo</Text>
                    </>
                  )}
                </View>
              )}
            </TouchableOpacity>
          </View>
        </View>

        {/* Business Information Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Business Information</Text>
          
          <Controller
            control={control}
            name="business_name"
            render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
              <FormField
                label="Business Name *"
                value={value || ""}
                onChangeText={onChange}
                onBlur={onBlur}
                error={error?.message}
                placeholder="Enter your business name"
              />
            )}
          />

          <Controller
            control={control}
            name="business_category"
            render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
              <FormField
                label="Business Category"
                value={value || ""}
                onChangeText={onChange}
                onBlur={onBlur}
                error={error?.message}
                placeholder="e.g., Restaurant, Retail, Services"
              />
            )}
          />

          <Controller
            control={control}
            name="established_year"
            render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
              <FormField
                label="Established Year"
                value={value ? value.toString() : ""}
                onChangeText={(text) => onChange(text ? parseInt(text) : undefined)}
                onBlur={onBlur}
                error={error?.message}
                type="number"
                placeholder="e.g., 2020"
              />
            )}
          />
        </View>

        {/* Personal Information Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Personal Information</Text>
          
          <Controller
            control={control}
            name="member_name"
            render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
              <FormField
                label="Your Name *"
                value={value || ""}
                onChangeText={onChange}
                onBlur={onBlur}
                error={error?.message}
                placeholder="Enter your full name"
              />
            )}
          />

          <Controller
            control={control}
            name="title"
            render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
              <FormField
                label="Title / Designation *"
                value={value || ""}
                onChangeText={onChange}
                onBlur={onBlur}
                error={error?.message}
                placeholder="e.g., Owner, Manager, CEO"
              />
            )}
          />
        </View>

        {/* About Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>About Your Business</Text>
          
          <Controller
            control={control}
            name="about_bio"
            render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
              <FormField
                label="About / Bio"
                value={value || ""}
                onChangeText={onChange}
                onBlur={onBlur}
                error={error?.message}
                multiline
                numberOfLines={4}
                placeholder="Tell customers about your business..."
                maxLength={250}
              />
            )}
          />
        </View>
      </ScrollView>

      {/* Save Button */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.saveButton,
            { backgroundColor: theme.colors.primary },
            isLoading && styles.saveButtonDisabled,
          ]}
          onPress={handleSubmit(onSubmit)}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="white" />
          ) : (
            <Text style={styles.saveButtonText}>Save Basic Information</Text>
          )}
        </TouchableOpacity>
      </View>

      {/* Image Picker Modal */}
      <ImagePickerBottomSheet
        ref={imagePickerRef}
        onCameraPress={handleCameraSelection}
        onGalleryPress={handleGallerySelection}
      />
    </View>
  );
}
