import { getUserAndProfile } from "@/lib/actions/user/getUserAndProfile";
import { describe, it, expect, jest, beforeEach } from "@jest/globals";
import { getUserAndProfile as originalGetUserAndProfile } from "@/lib/actions/subscription/utils";

// Mock the originalGetUserAndProfile from subscription utils module
jest.mock("@/lib/actions/subscription/utils", () => ({
  getUserAndProfile: jest.fn(),
}));

const mockOriginalGetUserAndProfile = originalGetUserAndProfile as jest.MockedFunction<typeof originalGetUserAndProfile>;

describe("getUserAndProfile", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should return an error if user is not authenticated", async () => {
    mockOriginalGetUserAndProfile.mockResolvedValue({
      error: "User not authenticated",
      user: null,
      profile: null,
      subscription: null,
    });

    const result = await getUserAndProfile();
    expect(result.error).toBe("User not authenticated");
    expect(result.user).toBeNull();
    expect(result.profile).toBeNull();
    expect(result.subscription).toBeNull();
  });

  it("should return an error if business profile is not found", async () => {
    const user = {
      id: "test-user-id",
      name: null,
      email: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      avatar_url: null,
      address: null,
      pincode: null,
      state: null,
      city: null,
      locality: null,
      city_slug: null,
      state_slug: null,
      locality_slug: null,
      phone: null,
      latitude: null,
      longitude: null,
    };
    mockOriginalGetUserAndProfile.mockResolvedValue({
      error: "Business profile not found. Please complete onboarding first.",
      user,
      profile: null,
      subscription: null,
    });

    const result = await getUserAndProfile();
    expect(result.error).toBe(
      "Business profile not found. Please complete onboarding first."
    );
    expect(result.user).toEqual(user);
    expect(result.profile).toBeNull();
    expect(result.subscription).toBeNull();
  });

  it("should return user, profile, and subscription on success", async () => {
    const user = {
      id: "test-user-id",
      name: null,
      email: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      avatar_url: null,
      address: null,
      pincode: null,
      state: null,
      city: null,
      locality: null,
      city_slug: null,
      state_slug: null,
      locality_slug: null,
      phone: null,
      latitude: null,
      longitude: null,
    };
    const profile = {
      id: "test-user-id",
      business_name: "Test Business",
      contact_email: null,
      has_active_subscription: false,
      trial_end_date: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      logo_url: null,
      member_name: null,
      phone: null,
      instagram_url: null,
      whatsapp_number: null,
      status: "active",
      title: null,
      address_line: null,
      city: null,
      state: null,
      pincode: null,
      locality: null,
      about_bio: null,
      facebook_url: null,
      average_rating: null,
      total_likes: 0,
      total_subscriptions: 0,
      theme_color: null,
      business_hours: null,
      delivery_info: null,
      total_visits: 0,
      today_visits: 0,
      yesterday_visits: 0,
      visits_7_days: 0,
      visits_30_days: 0,
      business_category: null,
      business_slug: null,
      gallery: null,
      city_slug: null,
      state_slug: null,
      locality_slug: null,
      custom_branding: null,
      custom_ads: null,
      established_year: null,
      latitude: null,
      longitude: null,
    };
    const subscription = { id: "sub-id", plan: "premium" };

    mockOriginalGetUserAndProfile.mockResolvedValue({
      error: null,
      user,
      profile,
      subscription,
    });

    const result = await getUserAndProfile();

    expect(result.error).toBeNull();
    expect(result.user).toEqual(user);
    expect(result.profile).toEqual(profile);
    expect(result.subscription).toEqual(subscription);
  });

  it("should handle the case where there is no subscription", async () => {
    const user = {
      id: "test-user-id",
      name: null,
      email: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      avatar_url: null,
      address: null,
      pincode: null,
      state: null,
      city: null,
      locality: null,
      city_slug: null,
      state_slug: null,
      locality_slug: null,
      phone: null,
      latitude: null,
      longitude: null,
    };
    const profile = {
      id: "test-user-id",
      business_name: "Test Business",
      contact_email: null,
      has_active_subscription: false,
      trial_end_date: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      logo_url: null,
      member_name: null,
      phone: null,
      instagram_url: null,
      whatsapp_number: null,
      status: "active",
      title: null,
      address_line: null,
      city: null,
      state: null,
      pincode: null,
      locality: null,
      about_bio: null,
      facebook_url: null,
      average_rating: null,
      total_likes: 0,
      total_subscriptions: 0,
      theme_color: null,
      business_hours: null,
      delivery_info: null,
      total_visits: 0,
      today_visits: 0,
      yesterday_visits: 0,
      visits_7_days: 0,
      visits_30_days: 0,
      business_category: null,
      business_slug: null,
      gallery: null,
      city_slug: null,
      state_slug: null,
      locality_slug: null,
      custom_branding: null,
      custom_ads: null,
      established_year: null,
      latitude: null,
      longitude: null,
    };

    mockOriginalGetUserAndProfile.mockResolvedValue({
      error: null,
      user,
      profile,
      subscription: null,
    });

    const result = await getUserAndProfile();

    expect(result.error).toBeNull();
    expect(result.user).toEqual(user);
    expect(result.profile).toEqual(profile);
    expect(result.subscription).toBeNull();
  });
});