import { StyleSheet } from "react-native";
import { useTheme } from "@/src/hooks/useTheme";

export const createReviewsModalStyles = (
  theme: ReturnType<typeof useTheme>
) => {
  const { isDark } = theme;
  return StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: "600",
      color: theme.colors.foreground,
    },
    closeButton: {
      padding: theme.spacing.xs,
    },
    contentContainer: {
      flex: 1,
      padding: theme.spacing.md,
    },
    sortContainer: {
      flexDirection: "row",
      justifyContent: "flex-end",
      marginBottom: theme.spacing.md,
    },
    sortButton: {
      flexDirection: "row",
      alignItems: "center",
      paddingVertical: theme.spacing.sm,
      paddingHorizontal: theme.spacing.md,
      borderRadius: theme.borderRadius.md,
      backgroundColor: theme.colors.card,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    sortButtonText: {
      color: theme.colors.foreground,
      marginRight: theme.spacing.sm,
    },
    emptyContainer: {
      flex: 1,
      alignItems: "center",
      justifyContent: "center",
      padding: theme.spacing.xl,
    },
    emptyText: {
      fontSize: theme.typography.fontSize.base,
      color: theme.colors.textSecondary,
      textAlign: "center",
      marginTop: theme.spacing.md,
    },
    footerLoadingContainer: {
      paddingVertical: theme.spacing.lg,
    },
    // Reviews header section styles
    reviewsHeaderSection: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    reviewCountText: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
      fontWeight: "500",
    },
    // Sort bottom sheet styles
    sortBottomSheetContainer: {
      flex: 1,
      paddingHorizontal: theme.spacing.md,
    },
    sortBottomSheetHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    sortBottomSheetTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: "600",
      color: theme.colors.foreground,
    },
    sortBottomSheetCloseButton: {
      padding: theme.spacing.xs,
    },
    sortBottomSheetListContent: {
      paddingVertical: theme.spacing.sm,
      paddingBottom: theme.spacing.xl, // Add extra bottom padding for proper spacing
    },
    sortGroupTitle: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: "600",
      color: theme.colors.textSecondary,
      marginTop: theme.spacing.md,
      marginBottom: theme.spacing.sm,
      paddingHorizontal: theme.spacing.sm,
    },
    sortOption: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
      marginVertical: theme.spacing.xs,
    },
    sortSelectedOption: {
      backgroundColor: theme.colors.card,
    },
    sortOptionText: {
      fontSize: theme.typography.fontSize.base,
      color: theme.colors.foreground,
    },
    sortSelectedOptionText: {
      color: "#C29D5B",
      fontWeight: "600",
    },
    // Search section styles
    searchContainer: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    searchInputContainer: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: theme.isDark ? theme.colors.input : theme.colors.card,
      borderRadius: theme.borderRadius.md,
      paddingHorizontal: theme.spacing.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    searchInput: {
      flex: 1,
      height: 48,
      color: theme.colors.foreground,
      fontSize: theme.typography.fontSize.base,
    },
  });
};
