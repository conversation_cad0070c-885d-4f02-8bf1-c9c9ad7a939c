/**
 * Error handling components for Discovery Screen
 * Based on dukancard/app/(main)/discover/components/ErrorSection.tsx
 */

import React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/src/hooks/useTheme";

interface ErrorSectionProps {
  error: string | null;
  onRetry?: () => void;
  isVisible?: boolean;
}

export const ErrorSection: React.FC<ErrorSectionProps> = ({
  error,
  onRetry,
  isVisible = true,
}) => {
  const { colors } = useTheme();

  if (!error || !isVisible) {
    return null;
  }

  const styles = createStyles(colors);

  return (
    <View style={styles.container}>
      <View style={styles.errorCard}>
        <View style={styles.errorHeader}>
          <Ionicons
            name="alert-circle"
            size={24}
            color={colors.error}
            style={styles.errorIcon}
          />
          <Text style={styles.errorTitle}>Error Occurred</Text>
        </View>

        <Text style={styles.errorMessage}>{error}</Text>

        {onRetry && (
          <TouchableOpacity
            style={styles.retryButton}
            onPress={onRetry}
            activeOpacity={0.7}
          >
            <Ionicons
              name="refresh"
              size={16}
              color={colors.primaryForeground}
            />
            <Text style={styles.retryButtonText}>Try Again</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

interface EmptyStateProps {
  title: string;
  description: string;
  icon?: string;
  actionText?: string;
  onAction?: () => void;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  title,
  description,
  icon = "search",
  actionText,
  onAction,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  return (
    <View style={styles.emptyContainer}>
      <View style={styles.emptyContent}>
        <View style={styles.emptyIconContainer}>
          <Ionicons name={icon as any} size={48} color={colors.textSecondary} />
        </View>

        <Text style={styles.emptyTitle}>{title}</Text>
        <Text style={styles.emptyDescription}>{description}</Text>

        {actionText && onAction && (
          <TouchableOpacity
            style={styles.emptyActionButton}
            onPress={onAction}
            activeOpacity={0.7}
          >
            <Text style={styles.emptyActionText}>{actionText}</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

interface NetworkErrorProps {
  onRetry: () => void;
  isVisible?: boolean;
}

export const NetworkError: React.FC<NetworkErrorProps> = ({
  onRetry,
  isVisible = true,
}) => {
  const { colors } = useTheme();

  if (!isVisible) {
    return null;
  }

  const styles = createStyles(colors);

  return (
    <View style={styles.container}>
      <View style={styles.networkErrorCard}>
        <View style={styles.networkErrorHeader}>
          <Ionicons
            name="wifi"
            size={32}
            color={colors.textSecondary}
            style={styles.networkErrorIcon}
          />
          <Text style={styles.networkErrorTitle}>Connection Problem</Text>
        </View>

        <Text style={styles.networkErrorMessage}>
          Please check your internet connection and try again.
        </Text>

        <TouchableOpacity
          style={styles.networkRetryButton}
          onPress={onRetry}
          activeOpacity={0.7}
        >
          <Ionicons name="refresh" size={16} color={colors.primaryForeground} />
          <Text style={styles.networkRetryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

interface LocationErrorProps {
  error: string;
  onRetry?: () => void;
  onManualLocation?: () => void;
  isVisible?: boolean;
}

export const LocationError: React.FC<LocationErrorProps> = ({
  error,
  onRetry,
  onManualLocation,
  isVisible = true,
}) => {
  const { colors } = useTheme();

  if (!isVisible) {
    return null;
  }

  const styles = createStyles(colors);

  return (
    <View style={styles.container}>
      <View style={styles.locationErrorCard}>
        <View style={styles.locationErrorHeader}>
          <Ionicons
            name="location"
            size={24}
            color={colors.warning}
            style={styles.locationErrorIcon}
          />
          <Text style={styles.locationErrorTitle}>Location Issue</Text>
        </View>

        <Text style={styles.locationErrorMessage}>{error}</Text>

        <View style={styles.locationErrorActions}>
          {onRetry && (
            <TouchableOpacity
              style={[styles.locationActionButton, styles.retryLocationButton]}
              onPress={onRetry}
              activeOpacity={0.7}
            >
              <Ionicons name="refresh" size={16} color={colors.primary} />
              <Text style={styles.retryLocationButtonText}>Try Again</Text>
            </TouchableOpacity>
          )}

          {onManualLocation && (
            <TouchableOpacity
              style={[styles.locationActionButton, styles.manualLocationButton]}
              onPress={onManualLocation}
              activeOpacity={0.7}
            >
              <Ionicons
                name="create"
                size={16}
                color={colors.primaryForeground}
              />
              <Text style={styles.manualLocationButtonText}>
                Enter Manually
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </View>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      paddingHorizontal: 20,
      paddingVertical: 16,
    },
    errorCard: {
      backgroundColor: colors.errorBackground || colors.cardBackground,
      borderRadius: 12,
      padding: 16,
      borderWidth: 1,
      borderColor: colors.error + "30",
    },
    errorHeader: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 8,
    },
    errorIcon: {
      marginRight: 8,
    },
    errorTitle: {
      fontSize: 16,
      fontWeight: "600",
      color: colors.error,
    },
    errorMessage: {
      fontSize: 14,
      color: colors.textSecondary,
      lineHeight: 20,
      marginBottom: 12,
    },
    retryButton: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: colors.primary,
      borderRadius: 8,
      paddingVertical: 10,
      paddingHorizontal: 16,
    },
    retryButtonText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.primaryForeground,
      marginLeft: 6,
    },
    emptyContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingHorizontal: 40,
      paddingVertical: 60,
    },
    emptyContent: {
      alignItems: "center",
    },
    emptyIconContainer: {
      width: 80,
      height: 80,
      borderRadius: 40,
      backgroundColor: colors.border + "30",
      justifyContent: "center",
      alignItems: "center",
      marginBottom: 16,
    },
    emptyTitle: {
      fontSize: 18,
      fontWeight: "600",
      color: colors.textPrimary,
      textAlign: "center",
      marginBottom: 8,
    },
    emptyDescription: {
      fontSize: 14,
      color: colors.textSecondary,
      textAlign: "center",
      lineHeight: 20,
      marginBottom: 20,
    },
    emptyActionButton: {
      backgroundColor: colors.primary,
      borderRadius: 8,
      paddingVertical: 12,
      paddingHorizontal: 24,
    },
    emptyActionText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.primaryForeground,
    },
    networkErrorCard: {
      backgroundColor: colors.cardBackground,
      borderRadius: 12,
      padding: 20,
      alignItems: "center",
      borderWidth: 1,
      borderColor: colors.border,
    },
    networkErrorHeader: {
      alignItems: "center",
      marginBottom: 12,
    },
    networkErrorIcon: {
      marginBottom: 8,
    },
    networkErrorTitle: {
      fontSize: 16,
      fontWeight: "600",
      color: colors.textPrimary,
    },
    networkErrorMessage: {
      fontSize: 14,
      color: colors.textSecondary,
      textAlign: "center",
      lineHeight: 20,
      marginBottom: 16,
    },
    networkRetryButton: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: colors.primary,
      borderRadius: 8,
      paddingVertical: 10,
      paddingHorizontal: 20,
    },
    networkRetryButtonText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.primaryForeground,
      marginLeft: 6,
    },
    locationErrorCard: {
      backgroundColor: colors.warningBackground || colors.cardBackground,
      borderRadius: 12,
      padding: 16,
      borderWidth: 1,
      borderColor: colors.warning + "30",
    },
    locationErrorHeader: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 8,
    },
    locationErrorIcon: {
      marginRight: 8,
    },
    locationErrorTitle: {
      fontSize: 16,
      fontWeight: "600",
      color: colors.warning,
    },
    locationErrorMessage: {
      fontSize: 14,
      color: colors.textSecondary,
      lineHeight: 20,
      marginBottom: 16,
    },
    locationErrorActions: {
      flexDirection: "row",
      gap: 12,
    },
    locationActionButton: {
      flex: 1,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      borderRadius: 8,
      paddingVertical: 10,
      paddingHorizontal: 12,
    },
    retryLocationButton: {
      backgroundColor: "transparent",
      borderWidth: 1,
      borderColor: colors.primary,
    },
    retryLocationButtonText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.primary,
      marginLeft: 6,
    },
    manualLocationButton: {
      backgroundColor: colors.primary,
    },
    manualLocationButtonText: {
      fontSize: 14,
      fontWeight: "500",
      color: colors.primaryForeground,
      marginLeft: 6,
    },
  });
