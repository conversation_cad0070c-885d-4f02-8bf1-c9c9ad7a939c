/**
 * Skeleton loaders for Discovery Screen components
 * Based on dukancard-app/src/components/ui/ProductSkeleton.tsx
 */

import React from "react";
import { View, StyleSheet } from "react-native";
import { SkeletonLoader } from "@/src/components/ui/SkeletonLoader";
import { useTheme } from "@/src/hooks/useTheme";

// Search Section Skeleton
export const SearchSectionSkeleton: React.FC = () => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  return (
    <View style={styles.container}>
      {/* Business Search Skeleton */}
      <View style={styles.searchGroup}>
        <SkeletonLoader width={120} height={14} style={styles.labelSkeleton} />
        <View style={styles.searchContainer}>
          <SkeletonLoader
            width={20}
            height={20}
            borderRadius={10}
            style={styles.iconSkeleton}
          />
          <SkeletonLoader
            width="70%"
            height={16}
            style={styles.inputSkeleton}
          />
        </View>
      </View>

      {/* Product Search Skeleton */}
      <View style={styles.searchGroup}>
        <SkeletonLoader width={160} height={14} style={styles.labelSkeleton} />
        <View style={styles.searchContainer}>
          <SkeletonLoader
            width={20}
            height={20}
            borderRadius={10}
            style={styles.iconSkeleton}
          />
          <SkeletonLoader
            width="80%"
            height={16}
            style={styles.inputSkeleton}
          />
        </View>
      </View>

      {/* Hint Skeleton */}
      <View style={styles.hintContainer}>
        <SkeletonLoader width={16} height={16} borderRadius={8} />
        <SkeletonLoader width="90%" height={12} style={styles.hintSkeleton} />
      </View>
    </View>
  );
};

// Category Selector Skeleton
export const CategorySelectorSkeleton: React.FC = () => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  return (
    <View style={styles.container}>
      <SkeletonLoader width={140} height={14} style={styles.labelSkeleton} />
      <View style={styles.selectorContainer}>
        <SkeletonLoader
          width={20}
          height={20}
          borderRadius={10}
          style={styles.iconSkeleton}
        />
        <SkeletonLoader width="60%" height={16} style={styles.inputSkeleton} />
        <SkeletonLoader width={20} height={20} borderRadius={10} />
      </View>
      <View style={styles.hintContainer}>
        <SkeletonLoader width={16} height={16} borderRadius={8} />
        <SkeletonLoader width="85%" height={12} style={styles.hintSkeleton} />
      </View>
    </View>
  );
};

// View Toggle Skeleton
export const ViewToggleSkeleton: React.FC = () => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  return (
    <View style={styles.toggleContainer}>
      <View style={styles.toggleButtonsContainer}>
        <View style={styles.toggleButton}>
          <SkeletonLoader
            width={18}
            height={18}
            borderRadius={9}
            style={styles.iconSkeleton}
          />
          <SkeletonLoader
            width={60}
            height={14}
            style={styles.buttonTextSkeleton}
          />
        </View>
        <View style={styles.toggleButton}>
          <SkeletonLoader
            width={18}
            height={18}
            borderRadius={9}
            style={styles.iconSkeleton}
          />
          <SkeletonLoader
            width={80}
            height={14}
            style={styles.buttonTextSkeleton}
          />
        </View>
      </View>
      <SkeletonLoader
        width="70%"
        height={12}
        style={styles.descriptionSkeleton}
      />
    </View>
  );
};

// Location Selector Skeleton
export const LocationSelectorSkeleton: React.FC = () => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  return (
    <View style={styles.container}>
      <SkeletonLoader width={100} height={14} style={styles.labelSkeleton} />
      <View style={styles.locationContainer}>
        <SkeletonLoader
          width={20}
          height={20}
          borderRadius={10}
          style={styles.iconSkeleton}
        />
        <SkeletonLoader width="50%" height={16} style={styles.inputSkeleton} />
        <SkeletonLoader width={80} height={32} borderRadius={16} />
      </View>
      <View style={styles.locationRow}>
        <View style={styles.locationField}>
          <SkeletonLoader
            width={60}
            height={12}
            style={styles.smallLabelSkeleton}
          />
          <SkeletonLoader
            width="100%"
            height={16}
            style={styles.smallInputSkeleton}
          />
        </View>
        <View style={styles.locationField}>
          <SkeletonLoader
            width={40}
            height={12}
            style={styles.smallLabelSkeleton}
          />
          <SkeletonLoader
            width="100%"
            height={16}
            style={styles.smallInputSkeleton}
          />
        </View>
      </View>
    </View>
  );
};

// Business Card Skeleton (for results)
export const BusinessCardSkeleton: React.FC = () => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  return (
    <View style={styles.businessCard}>
      {/* Distance Badge Skeleton - Top Right */}
      <SkeletonLoader
        width={45}
        height={20}
        borderRadius={10}
        style={styles.distanceBadgeSkeleton}
      />

      {/* Header with logo and basic info */}
      <View style={styles.businessHeader}>
        <SkeletonLoader width={44} height={44} borderRadius={22} />
        <View style={styles.businessInfo}>
          <SkeletonLoader width="75%" height={15} style={styles.businessName} />
          <SkeletonLoader
            width="55%"
            height={11}
            style={styles.businessCategory}
          />
          <SkeletonLoader
            width="65%"
            height={11}
            style={styles.businessLocation}
          />
        </View>
      </View>

      {/* Description */}
      <SkeletonLoader
        width="85%"
        height={13}
        style={styles.businessDescription}
      />
      <SkeletonLoader
        width="60%"
        height={13}
        style={styles.businessDescription}
      />

      {/* Stats Section Skeleton */}
      <View style={styles.statsContainer}>
        {/* Likes */}
        <View style={styles.statItem}>
          <SkeletonLoader width={14} height={14} borderRadius={7} />
          <SkeletonLoader width={25} height={12} style={styles.statText} />
        </View>

        {/* Followers */}
        <View style={styles.statItem}>
          <SkeletonLoader width={14} height={14} borderRadius={7} />
          <SkeletonLoader width={25} height={12} style={styles.statText} />
        </View>

        {/* Rating */}
        <View style={styles.statItem}>
          <SkeletonLoader width={14} height={14} borderRadius={7} />
          <SkeletonLoader width={25} height={12} style={styles.statText} />
        </View>
      </View>
    </View>
  );
};

// Results List Skeleton
export const ResultsListSkeleton: React.FC<{ itemCount?: number }> = ({
  itemCount = 5,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  return (
    <View style={styles.resultsContainer}>
      {Array.from({ length: itemCount }).map((_, index) => (
        <BusinessCardSkeleton key={index} />
      ))}
    </View>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      paddingHorizontal: 20,
      paddingVertical: 16,
      backgroundColor: colors.background,
    },
    searchGroup: {
      marginBottom: 16,
    },
    labelSkeleton: {
      marginBottom: 8,
    },
    searchContainer: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: colors.cardBackground,
      borderRadius: 12,
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderWidth: 1,
      borderColor: colors.border,
    },
    selectorContainer: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: colors.cardBackground,
      borderRadius: 12,
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderWidth: 1,
      borderColor: colors.border,
    },
    iconSkeleton: {
      marginRight: 12,
    },
    inputSkeleton: {
      flex: 1,
    },
    hintContainer: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: 4,
      marginTop: 8,
    },
    hintSkeleton: {
      marginLeft: 6,
    },
    toggleContainer: {
      paddingHorizontal: 20,
      paddingVertical: 16,
      backgroundColor: colors.background,
      alignItems: "center",
    },
    toggleButtonsContainer: {
      flexDirection: "row",
      backgroundColor: colors.cardBackground,
      borderRadius: 12,
      padding: 4,
      borderWidth: 1,
      borderColor: colors.border,
    },
    toggleButton: {
      flex: 1,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: 12,
      paddingHorizontal: 16,
      borderRadius: 8,
    },
    buttonTextSkeleton: {
      marginLeft: 6,
    },
    descriptionSkeleton: {
      marginTop: 8,
    },
    locationContainer: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: colors.cardBackground,
      borderRadius: 12,
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderWidth: 1,
      borderColor: colors.border,
      marginBottom: 12,
    },
    locationRow: {
      flexDirection: "row",
      gap: 12,
    },
    locationField: {
      flex: 1,
    },
    smallLabelSkeleton: {
      marginBottom: 4,
    },
    smallInputSkeleton: {
      marginTop: 4,
    },
    businessCard: {
      backgroundColor: colors.cardBackground,
      borderRadius: 12,
      padding: 12, // Reduced from 16
      marginHorizontal: 0, // Removed horizontal margin
      marginVertical: 6, // Reduced from 8
      borderWidth: 1,
      borderColor: colors.border,
      shadowColor: colors.shadow,
      shadowOffset: {
        width: 0,
        height: 1,
      },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 2,
      position: "relative", // Add position relative for absolute positioning of badge
    },
    distanceBadgeSkeleton: {
      position: "absolute",
      top: 8,
      right: 8,
      zIndex: 1,
    },
    businessHeader: {
      flexDirection: "row",
      marginBottom: 8, // Reduced from 12
    },
    businessInfo: {
      flex: 1,
      marginLeft: 10, // Reduced from 12
    },
    businessName: {
      marginBottom: 3, // Reduced from 4
    },
    businessCategory: {
      marginBottom: 4,
    },
    businessLocation: {
      marginBottom: 4,
    },
    businessDescription: {
      marginBottom: 4,
      marginTop: 4, // Added top margin to match real component
    },
    statsContainer: {
      flexDirection: "row",
      justifyContent: "space-around",
      alignItems: "center",
      marginTop: 12,
      paddingTop: 12,
      borderTopWidth: 1,
      borderTopColor: colors.border + "40", // Faint border
    },
    statItem: {
      flexDirection: "row",
      alignItems: "center",
      flex: 1,
      justifyContent: "center",
    },
    statText: {
      marginLeft: 4,
    },
    resultsContainer: {
      paddingVertical: 8,
    },
  });
