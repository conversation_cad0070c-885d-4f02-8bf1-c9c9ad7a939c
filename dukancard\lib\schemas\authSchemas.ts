import { z } from "zod";

export const IndianMobileSchema = z
  .string()
  .trim()
  .min(10, { message: "Mobile number must be 10 digits" })
  .max(10, { message: "Mobile number must be 10 digits" })
  .regex(/^[6-9]\d{9}$/, { message: "Please enter a valid Indian mobile number" });

export const EmailOTPSchema = z.object({
  email: z
    .string()
    .trim()
    .min(1, { message: "Email is required" })
    .email({ message: "Please enter a valid email address" }),
});

export const VerifyOTPSchema = z.object({
  email: z
    .string()
    .trim()
    .min(1, { message: "Email is required" })
    .email({ message: "Please enter a valid email address" }),
  otp: z
    .string()
    .trim()
    .min(6, { message: "OTP must be 6 digits" })
    .max(6, { message: "OTP must be 6 digits" })
    .regex(/^\d{6}$/, { message: "OTP must be 6 digits" }),
});

export const PasswordComplexitySchema = z
  .string()
  .min(8, "Password must be at least 8 characters long")
  .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
  .regex(/[a-z]/, "Password must contain at least one lowercase letter")
  .regex(/\d/, "Password must contain at least one number")
  .regex(/[^a-zA-Z0-9]/, "Password must contain at least one special character");

export const MobilePasswordLoginSchema = z.object({
  mobile: IndianMobileSchema,
  password: z.string().trim().min(1, { message: "Password is required" }),
});