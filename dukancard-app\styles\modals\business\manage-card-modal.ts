import { StyleSheet } from "react-native";
import { useTheme } from "@/src/hooks/useTheme";

export const createManageCardModalStyles = (
  theme: ReturnType<typeof useTheme>
) => {
  const { isDark } = theme;
  return StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: isDark ? "#000" : "#FFF",
    },
    keyboardAvoidingView: {
      flex: 1,
    },
    header: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: "600",
      color: isDark ? "#FFF" : "#000",
    },
    closeButton: {
      padding: theme.spacing.xs,
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      padding: theme.spacing.md,
      paddingBottom: theme.spacing.xl,
    },
    form: {
      flex: 1,
    },
    section: {
      marginBottom: theme.spacing.lg,
    },
    sectionTitle: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
      color: isDark ? "#FFF" : "#000",
      marginBottom: theme.spacing.sm,
    },
    logoContainer: {
      width: 120,
      height: 120,
      borderRadius: 60,
      backgroundColor: theme.colors.card,
      justifyContent: "center",
      alignItems: "center",
      marginBottom: 20,
      borderWidth: 2,
      borderColor: theme.colors.border,
    },
    logo: {
      width: "100%",
      height: "100%",
      borderRadius: 60,
    },
    logoPlaceholder: {
      justifyContent: "center",
      alignItems: "center",
    },
    footer: {
      padding: theme.spacing.md,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      backgroundColor: isDark ? "#000" : "#FFF",
    },
    submitButton: {
      borderRadius: theme.borderRadius.lg,
      paddingVertical: theme.spacing.md,
      alignItems: "center",
      justifyContent: "center",
      minHeight: 50,
    },
    submitButtonDisabled: {
      opacity: 0.6,
    },
    submitButtonText: {
      color: "white",
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
    },

    // Menu styles
    menuContainer: {
      flex: 1,
    },
    menuTitle: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: "700",
      color: isDark ? "#FFF" : "#000",
      marginBottom: theme.spacing.xs,
    },
    menuSubtitle: {
      fontSize: theme.typography.fontSize.sm,
      color: isDark ? "#999" : "#666",
      marginBottom: theme.spacing.lg,
    },
    menuItem: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
      backgroundColor: isDark ? "#000" : "#FFF",
    },
    menuItemLeft: {
      flexDirection: "row",
      alignItems: "center",
      flex: 1,
    },
    menuItemIcon: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: isDark ? "rgba(212, 175, 55, 0.1)" : "rgba(212, 175, 55, 0.1)",
      alignItems: "center",
      justifyContent: "center",
      marginRight: theme.spacing.sm,
    },
    menuItemContent: {
      flex: 1,
    },
    menuItemTitle: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
      color: isDark ? "#FFF" : "#000",
      marginBottom: 2,
    },
    menuItemDescription: {
      fontSize: theme.typography.fontSize.xs,
      color: isDark ? "#999" : "#666",
    },
    menuItemRight: {
      flexDirection: "row",
      alignItems: "center",
    },

    // Section view styles
    sectionContainer: {
      flex: 1,
      padding: theme.spacing.md,
      alignItems: "center",
      justifyContent: "center",
    },
    backButton: {
      padding: theme.spacing.xs,
    },
  });
};
