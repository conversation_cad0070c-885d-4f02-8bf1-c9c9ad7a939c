import {
  cleanPhoneFromAuth,
  maskPhoneNumber,
  maskEmail,
  formatIndianNumberShort,
  formatAddress,
  formatDate,
  formatCurrency,
  toTitleCase,
} from "@/lib/utils";
import { BusinessCardData } from "@/app/(dashboard)/dashboard/business/card/schema";

describe("Utility Functions", () => {
  describe("cleanPhoneFromAuth", () => {
    it("should clean phone numbers with +91 prefix", () => {
      expect(cleanPhoneFromAuth("+************")).toBe("8458060663");
    });

    it("should clean phone numbers with 91 prefix", () => {
      expect(cleanPhoneFromAuth("************")).toBe("8458060663");
    });

    it("should return 10-digit numbers as is", () => {
      expect(cleanPhoneFromAuth("8458060663")).toBe("8458060663");
    });

    it("should return null for invalid numbers", () => {
      expect(cleanPhoneFromAuth("12345")).toBeNull();
      expect(cleanPhoneFromAuth("abcdefghij")).toBeNull();
    });

    it("should return null for null or undefined input", () => {
      expect(cleanPhoneFromAuth(null)).toBeNull();
      expect(cleanPhoneFromAuth(undefined)).toBeNull();
    });
  });

  describe("maskPhoneNumber", () => {
    it("should mask a valid phone number", () => {
      expect(maskPhoneNumber("9123456789")).toBe("91******89");
    });

    it("should return 'Invalid Phone' for short numbers", () => {
      expect(maskPhoneNumber("123")).toBe("Invalid Phone");
    });

    it("should return 'Invalid Phone' for null or undefined", () => {
      expect(maskPhoneNumber(null)).toBe("Invalid Phone");
      expect(maskPhoneNumber(undefined)).toBe("Invalid Phone");
    });
  });

  describe("maskEmail", () => {
    it("should mask a valid email", () => {
      expect(maskEmail("<EMAIL>")).toBe("ex*****@do****.com");
    });

    it("should handle short usernames and domains", () => {
      expect(maskEmail("<EMAIL>")).toBe("Email Hidden");
    });

    it("should return 'Invalid Email' for invalid emails", () => {
      expect(maskEmail("invalid-email")).toBe("Invalid Email");
    });
  });

  describe("formatIndianNumberShort", () => {
    it("should format thousands", () => {
      expect(formatIndianNumberShort(1200)).toBe("1.2K");
    });

    it("should format lakhs", () => {
      expect(formatIndianNumberShort(120000)).toBe("1.2L");
    });

    it("should format crores", () => {
      expect(formatIndianNumberShort(12000000)).toBe("1.2Cr");
    });
  });

  describe("formatAddress", () => {
    it("should format a full address", () => {
      const data = {
        address_line: "123 Main St",
        locality: "Koramangala",
        city: "Bengaluru",
        state: "Karnataka",
        pincode: "560034",
      } as BusinessCardData;
      expect(formatAddress(data)).toBe(
        "123 Main St, Koramangala, Bengaluru, Karnataka, 560034"
      );
    });

    it("should handle missing address parts", () => {
      const data = {
        city: "Bengaluru",
        state: "Karnataka",
      } as BusinessCardData;
      expect(formatAddress(data)).toBe("Bengaluru, Karnataka");
    });
  });

  describe("formatDate", () => {
    it("should format a date without time", () => {
      const date = new Date("2023-10-27T10:00:00Z");
      // Note: The output depends on the test environment's timezone.
      // This test assumes a consistent environment.
      expect(formatDate(date)).toBe("27 October 2023");
    });

    it("should format a date with time", () => {
      const date = new Date("2023-10-27T10:00:00Z");
      const formattedDate = formatDate(date, true);
      expect(formattedDate).toContain("27 October 2023");
      expect(formattedDate).toMatch(/0?3:30(:00)?\s?pm/i);
    });
  });

  describe("formatCurrency", () => {
    it("should format INR currency", () => {
      expect(formatCurrency(12345)).toBe("₹12,345");
    });

    it("should format USD currency", () => {
      // The exact format can vary based on the Intl implementation.
      // Checking for the dollar sign and the number is more robust.
      const formatted = formatCurrency(123.45, "USD");
      expect(formatted).toContain("$");
      expect(formatted).toContain("123.45");
    });
  });

  describe("toTitleCase", () => {
    it("should convert a string to title case", () => {
      expect(toTitleCase("hello world")).toBe("Hello World");
    });

    it("should handle an empty string", () => {
      expect(toTitleCase("")).toBe("");
    });
  });
});