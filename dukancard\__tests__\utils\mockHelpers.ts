// Test utilities for configuring global mocks
import { mockHelpers } from '@/__mocks__/supabase/mockFactory';

// Re-export the mock helpers for easy access in tests
export { mockHelpers as supabaseMockHelpers };

// Additional test utilities
export const testHelpers = {
  // Create a mock user for testing
  createMockUser: (overrides = {}) => ({
    id: 'test-user-id',
    email: '<EMAIL>',
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z',
    ...overrides,
  }),

  // Create a mock business profile
  createMockBusinessProfile: (overrides = {}) => ({
    id: 'test-user-id',
    business_name: 'Test Business',
    business_type: 'retail',
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z',
    ...overrides,
  }),

  // Create a mock subscription
  createMockSubscription: (overrides = {}) => ({
    id: 'sub-123',
    plan: 'premium',
    status: 'active',
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z',
    ...overrides,
  }),

  // Create a mock product
  createMockProduct: (overrides = {}) => ({
    id: 'prod-123',
    name: 'Test Product',
    product_type: 'product',
    description: 'Test product description',
    base_price: 100,
    discounted_price: 90,
    is_available: true,
    image_url: null,
    images: [],
    featured_image_index: 0,
    slug: 'test-product',
    created_at: '2023-01-01T00:00:00.000Z',
    updated_at: '2023-01-01T00:00:00.000Z',
    product_variants: [],
    ...overrides,
  }),

  // Create a mock error
  createMockError: (message = 'Test error', code = 'TEST_ERROR') => ({
    message,
    code,
    details: null,
    hint: null,
  }),
};

// Common test scenarios
export const testScenarios = {
  // Setup authenticated user scenario
  authenticatedUser: (user = testHelpers.createMockUser()) => {
    mockHelpers.mockAuthenticatedUser(user);
    return user;
  },

  // Setup unauthenticated scenario
  unauthenticatedUser: () => {
    mockHelpers.mockUnauthenticatedUser();
  },

  // Setup successful data fetch
  successfulQuery: (data: any, count?: number) => {
    return mockHelpers.mockSuccessfulQuery(data, count);
  },

  // Setup query error
  queryError: (error: any) => {
    return mockHelpers.mockQueryError(error);
  },

  // Reset all mocks
  resetAll: () => {
    mockHelpers.resetMocks();
    jest.clearAllMocks();
  },
};

// Basic test to ensure the file is valid
describe('Mock Helpers', () => {
  it('should export test helpers', () => {
    expect(testHelpers).toBeDefined();
    expect(testScenarios).toBeDefined();
    expect(typeof testHelpers.createMockUser).toBe('function');
    expect(typeof testScenarios.authenticatedUser).toBe('function');
  });
});
