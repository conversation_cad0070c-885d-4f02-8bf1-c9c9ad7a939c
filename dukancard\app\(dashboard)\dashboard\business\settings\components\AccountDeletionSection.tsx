"use client";

import { useState, useTransition, useEffect } from "react";
import { toast } from "sonner";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { Loader2, Trash2, AlertTriangle, XCircle, Mail, Smartphone } from "lucide-react";
import {
  deleteAccount,
  checkDeleteAccountVerificationOptions,
  sendDeleteAccountOTP,
  verifyDeleteAccountOTP,
  verifyDeleteAccountPassword
} from "../actions";

type VerificationStep = 'initial' | 'choose-method' | 'email-otp' | 'password' | 'final-confirm';

export default function AccountDeletionSection() {
  const router = useRouter();
  const [_isPending, startTransition] = useTransition();
  const [deleteConfirmText, setDeleteConfirmText] = useState("");
  const [isDeleting, setIsDeleting] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Enhanced security state
  const [verificationStep, setVerificationStep] = useState<VerificationStep>('initial');
  const [hasEmail, setHasEmail] = useState(false);
  const [hasPhone, setHasPhone] = useState(false);
  const [userEmail, setUserEmail] = useState('');
  const [_selectedMethod, setSelectedMethod] = useState<'email' | 'password' | null>(null);
  const [otp, setOtp] = useState('');
  const [password, setPassword] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [isSendingOTP, setIsSendingOTP] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [isCheckingOptions, setIsCheckingOptions] = useState(false);

  // Check verification options when dialog opens
  useEffect(() => {
    if (isDialogOpen && verificationStep === 'initial') {
      checkVerificationOptions();
    }
  }, [isDialogOpen, verificationStep]);

  const checkVerificationOptions = async () => {
    setIsCheckingOptions(true);
    try {
      const result = await checkDeleteAccountVerificationOptions();
      if (result.success) {
        setHasEmail(result.hasEmail);
        setHasPhone(result.hasPhone);

        // Get user email for OTP if available
        if (result.hasEmail) {
          // We'll get the email from the server action response
          setUserEmail(''); // Will be set when sending OTP
        }

        // Determine next step based on available options
        if (result.hasEmail && result.hasPhone) {
          setVerificationStep('choose-method');
        } else if (result.hasEmail) {
          setSelectedMethod('email');
          setVerificationStep('email-otp');
        } else if (result.hasPhone) {
          setSelectedMethod('password');
          setVerificationStep('password');
        } else {
          // No email or phone - proceed with just DELETE confirmation
          setVerificationStep('final-confirm');
        }
      } else {
        toast.error(result.message || 'Failed to check verification options');
      }
    } catch (_error) {
      toast.error('An error occurred while checking verification options');
    } finally {
      setIsCheckingOptions(false);
    }
  };

  const handleMethodSelection = (method: 'email' | 'password') => {
    setSelectedMethod(method);
    if (method === 'email') {
      setVerificationStep('email-otp');
    } else {
      setVerificationStep('password');
    }
  };

  const handleSendOTP = async () => {
    setIsSendingOTP(true);
    try {
      const result = await sendDeleteAccountOTP();
      if (result.success) {
        toast.success(result.message);
        if (result.email) {
          setUserEmail(result.email);
        }
      } else {
        toast.error(result.message);
        if (result.isConfigurationError) {
          // Don't proceed if configuration error
          return;
        }
      }
    } catch (_error) {
      toast.error('Failed to send verification code');
    } finally {
      setIsSendingOTP(false);
    }
  };

  const handleVerifyOTP = async () => {
    if (otp.length !== 6) {
      toast.error('Please enter a valid 6-digit code');
      return;
    }

    setIsVerifying(true);
    try {
      const result = await verifyDeleteAccountOTP(userEmail, otp);
      if (result.success) {
        toast.success(result.message);
        setIsVerified(true);
        setVerificationStep('final-confirm');
      } else {
        toast.error(result.message);
      }
    } catch (_error) {
      toast.error('Failed to verify code');
    } finally {
      setIsVerifying(false);
    }
  };

  const handleVerifyPassword = async () => {
    if (!password.trim()) {
      toast.error('Please enter your password');
      return;
    }

    setIsVerifying(true);
    try {
      const result = await verifyDeleteAccountPassword(password);
      if (result.success) {
        toast.success(result.message);
        setIsVerified(true);
        setVerificationStep('final-confirm');
      } else {
        toast.error(result.message);
      }
    } catch (_error) {
      toast.error('Failed to verify password');
    } finally {
      setIsVerifying(false);
    }
  };

  // Handle final account deletion
  const handleDeleteAccount = (e: React.MouseEvent) => {
    // Prevent the default action which would close the dialog
    e.preventDefault();

    if (deleteConfirmText !== "DELETE") {
      toast.error('Please type "DELETE" to confirm');
      return;
    }

    // Check if verification is required and completed
    if ((hasEmail || hasPhone) && !isVerified) {
      toast.error('Please complete verification first');
      return;
    }

    setIsDeleting(true);
    startTransition(async () => {
      try {
        // Show a toast before making the request
        toast.success("Processing account deletion...");

        // Wrap the deletion in a try-catch to handle potential errors
        try {
          const _result = await deleteAccount();

          // If we get here, the account was successfully deleted
          // The server action already signs out the user, so we just need to redirect
          resetDialogState();
          router.push("/");
        } catch (error: unknown) {
          // Check if the error is because the response is undefined (which happens after successful deletion)
          if (error instanceof Error && error.message.includes("Cannot read properties of undefined")) {
            // This likely means the account was deleted successfully but we lost the connection
            // because the user was signed out
            resetDialogState();
            router.push("/");
          } else {
            // This is a real error
            console.error("Error in account deletion:", error);
            toast.error("Failed to delete account");
            setIsDeleting(false);
          }
        }
      } catch (outerError) {
        // Handle any other errors that might occur
        console.error("Unexpected error during account deletion:", outerError);
        toast.error("An unexpected error occurred");
        setIsDeleting(false);
      }
    });
  };

  const resetDialogState = () => {
    setDeleteConfirmText("");
    setIsDialogOpen(false);
    setVerificationStep('initial');
    setSelectedMethod(null);
    setOtp('');
    setPassword('');
    setIsVerified(false);
    setUserEmail('');
    setIsCheckingOptions(false);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: 0.3 }}
      className="space-y-6"
    >
      {/* Section Header */}
      <div className="pb-6 border-b border-red-200/60 dark:border-red-700/60">
        <div className="flex items-center gap-3 mb-3">
          <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-br from-red-500/10 to-red-500/5 border border-red-500/20">
            <Trash2 className="w-4 h-4 text-red-500" />
          </div>
          <h2 className="text-xl font-semibold text-red-600 dark:text-red-400">
            Delete Account
          </h2>
        </div>
        <p className="text-sm text-red-600/80 dark:text-red-400/80 leading-relaxed">
          Permanently delete your account and all associated data.
        </p>
      </div>

      {/* Section Content */}
      <div className="space-y-6">

      <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-100 dark:border-red-800/30 mb-4">
        <div className="flex items-start gap-2">
          <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5" />
          <div>
            <p className="text-sm font-medium text-red-700 dark:text-red-400">
              Warning: This action cannot be undone
            </p>
            <p className="text-xs text-red-600/80 dark:text-red-400/80 mt-1">
              Deleting your account will permanently remove all your data, including your business card,
              products, and analytics. Any active subscriptions will be canceled.
            </p>
          </div>
        </div>
      </div>

        <div className="flex justify-end">
          <AlertDialog open={isDialogOpen} onOpenChange={(open) => {
            // Only allow closing the dialog if we're not in the deleting state
            if (!isDeleting) {
              setIsDialogOpen(open);
            }
          }}>
            <AlertDialogTrigger asChild>
              <Button
                variant="outline"
                className="px-4 py-2 h-auto font-medium border-red-500 dark:border-red-500 text-red-500 dark:text-red-400 hover:bg-red-500/10 shadow-sm hover:shadow-md transition-all duration-200"
                type="button"
                onClick={() => setIsDialogOpen(true)}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Account
              </Button>
            </AlertDialogTrigger>
        <AlertDialogContent className="bg-white dark:bg-black border-red-200 dark:border-red-800/30 p-0 overflow-hidden max-w-md">
          {/* Header with gradient background */}
          <div className="bg-gradient-to-r from-red-500/10 to-red-600/10 dark:from-red-800/20 dark:to-red-900/20 p-6 border-b border-red-200 dark:border-red-800/30">
            <AlertDialogHeader>
              <AlertDialogTitle className="text-red-600 dark:text-red-400 flex items-center gap-2 text-xl">
                <AlertTriangle className="h-6 w-6" />
                {verificationStep === 'choose-method' ? 'Verify Your Identity' :
                 verificationStep === 'email-otp' ? 'Email Verification' :
                 verificationStep === 'password' ? 'Password Verification' :
                 'Delete Account Confirmation'}
              </AlertDialogTitle>
              <AlertDialogDescription className="text-neutral-700 dark:text-neutral-300 mt-2">
                {verificationStep === 'choose-method' ? 'Choose how you want to verify your identity before deleting your account.' :
                 verificationStep === 'email-otp' ? 'We\'ve sent a verification code to your email address.' :
                 verificationStep === 'password' ? 'Please enter your current password to verify your identity.' :
                 'This action cannot be undone. This will permanently delete your account and remove all your data from our servers.'}
              </AlertDialogDescription>
            </AlertDialogHeader>
          </div>

          {/* Content */}
          <div className="p-6">
            {/* Loading state while checking verification options */}
            {isCheckingOptions && (
              <div className="flex flex-col items-center justify-center py-8 space-y-4">
                <Loader2 className="h-8 w-8 animate-spin text-neutral-500" />
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Checking verification options...
                </p>
              </div>
            )}

            {/* Choose verification method */}
            {!isCheckingOptions && verificationStep === 'choose-method' && (
              <div className="space-y-4">
                <p className="text-sm text-neutral-600 dark:text-neutral-400 mb-4">
                  For security, please verify your identity before proceeding:
                </p>

                <div className="space-y-3">
                  <Button
                    onClick={() => handleMethodSelection('email')}
                    variant="outline"
                    className="w-full justify-start gap-3 p-4 h-auto border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-800"
                  >
                    <Mail className="h-5 w-5 text-blue-500" />
                    <div className="text-left">
                      <div className="font-medium">Email Verification</div>
                      <div className="text-xs text-neutral-500">Send OTP to your email</div>
                    </div>
                  </Button>

                  <Button
                    onClick={() => handleMethodSelection('password')}
                    variant="outline"
                    className="w-full justify-start gap-3 p-4 h-auto border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-800"
                  >
                    <Smartphone className="h-5 w-5 text-green-500" />
                    <div className="text-left">
                      <div className="font-medium">Password Verification</div>
                      <div className="text-xs text-neutral-500">Enter your current password</div>
                    </div>
                  </Button>
                </div>
              </div>
            )}

            {/* Email OTP verification */}
            {!isCheckingOptions && verificationStep === 'email-otp' && (
              <div className="space-y-4">
                <div className="text-center">
                  <Button
                    onClick={handleSendOTP}
                    disabled={isSendingOTP}
                    variant="outline"
                    className="mb-4"
                  >
                    {isSendingOTP ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        Sending...
                      </>
                    ) : (
                      'Send Verification Code'
                    )}
                  </Button>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                    Enter 6-digit verification code:
                  </label>
                  <div className="flex justify-center">
                    <InputOTP
                      maxLength={6}
                      value={otp}
                      onChange={setOtp}
                      className="gap-2"
                    >
                      <InputOTPGroup>
                        <InputOTPSlot index={0} className="w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500" />
                        <InputOTPSlot index={1} className="w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500" />
                        <InputOTPSlot index={2} className="w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500" />
                        <InputOTPSlot index={3} className="w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500" />
                        <InputOTPSlot index={4} className="w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500" />
                        <InputOTPSlot index={5} className="w-12 h-12 text-lg font-semibold border-2 border-border focus:border-red-500" />
                      </InputOTPGroup>
                    </InputOTP>
                  </div>
                </div>

                <Button
                  onClick={handleVerifyOTP}
                  disabled={otp.length !== 6 || isVerifying}
                  className="w-full"
                >
                  {isVerifying ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Verifying...
                    </>
                  ) : (
                    'Verify Code'
                  )}
                </Button>
              </div>
            )}

            {/* Password verification */}
            {!isCheckingOptions && verificationStep === 'password' && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                    Enter your current password:
                  </label>
                  <Input
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Current password"
                    className="bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700"
                  />
                </div>

                <Button
                  onClick={handleVerifyPassword}
                  disabled={!password.trim() || isVerifying}
                  className="w-full"
                >
                  {isVerifying ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Verifying...
                    </>
                  ) : (
                    'Verify Password'
                  )}
                </Button>
              </div>
            )}

            {/* Final confirmation */}
            {!isCheckingOptions && verificationStep === 'final-confirm' && (
              <>
                <div className="bg-red-50 dark:bg-red-900/10 rounded-lg p-4 mb-6 border border-red-100 dark:border-red-800/20">
                  <div className="flex items-start gap-3">
                    <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-sm font-medium text-red-700 dark:text-red-400">
                        Warning: All data will be permanently lost
                      </p>
                      <ul className="text-xs text-red-600/80 dark:text-red-400/80 mt-2 space-y-1 list-disc pl-4">
                        <li>Your business profile and digital card</li>
                        <li>All products and services</li>
                        <li>Analytics and customer data</li>
                        <li>Subscription information</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <p className="text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
                  Type <span className="font-bold text-red-500 dark:text-red-400">DELETE</span> to confirm:
                </p>
                <Input
                  value={deleteConfirmText}
                  onChange={(e) => setDeleteConfirmText(e.target.value)}
                  placeholder="DELETE"
                  className="bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700 focus-visible:ring-red-500/30"
                />
              </>
            )}
          </div>

          {/* Footer */}
          <AlertDialogFooter className="p-6 pt-0 flex flex-col sm:flex-row gap-3">
            <AlertDialogCancel asChild>
              <Button
                className="w-full sm:w-auto border-neutral-200 dark:border-neutral-700 text-neutral-700 dark:text-neutral-300"
                disabled={isDeleting || isVerifying || isSendingOTP || isCheckingOptions}
                onClick={() => {
                  if (!isDeleting && !isVerifying && !isSendingOTP && !isCheckingOptions) {
                    resetDialogState();
                  }
                }}
              >
                <XCircle className="mr-2 h-4 w-4" />
                Cancel
              </Button>
            </AlertDialogCancel>

            {/* Only show delete button on final confirmation step */}
            {!isCheckingOptions && verificationStep === 'final-confirm' && (
              <div className="relative group inline-flex max-w-full w-auto">
                {/* Red glow effect for the dialog action button */}
                <div
                  className="absolute -inset-0.5 rounded-md bg-gradient-to-r from-red-500/60 to-red-600/80 blur-md max-w-full w-auto"
                  style={{ opacity: 0.7 }}
                />

                <AlertDialogAction
                  onClick={handleDeleteAccount}
                  disabled={deleteConfirmText !== "DELETE" || isDeleting}
                  className="relative z-10 w-full sm:w-auto bg-red-600 hover:bg-red-700 text-white flex items-center justify-center gap-2"
                  asChild
                >
                  <button
                    type="button"
                    disabled={deleteConfirmText !== "DELETE" || isDeleting}
                    className="relative z-10 w-full sm:w-auto bg-red-600 hover:bg-red-700 text-white flex items-center justify-center gap-2 px-4 py-2 rounded-md"
                    onClick={handleDeleteAccount}
                  >
                  {isDeleting ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Trash2 className="h-4 w-4" />
                  )}
                  <span>{isDeleting ? "Deleting..." : "Delete Account"}</span>
                  </button>
                </AlertDialogAction>
              </div>
            )}
          </AlertDialogFooter>
        </AlertDialogContent>
        </AlertDialog>
        </div>
      </div>
    </motion.div>
  );
}
