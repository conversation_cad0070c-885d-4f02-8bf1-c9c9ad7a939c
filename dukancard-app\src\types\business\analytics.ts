// From dukancard-app/BUSINESS_DASHBOARD_PRP.md
export interface BusinessMetrics {
  total_likes: number;
  total_subscriptions: number;
  average_rating: number;
  total_products: number;
  total_views: number;
  monthly_visits: number;
}

// From dukancard/app/(dashboard)/dashboard/business/analytics/actions.ts
export interface VisitAnalyticsData {
  totalUniqueVisits: number;
  todayUniqueVisits: number;
  yesterdayUniqueVisits: number;
  visits7Days: number;
  visits30Days: number;
  currentMonthUniqueVisits: number;
  previousMonthUniqueVisits: number;
  currentYear: number;
  currentMonth: number;
  dailyTrend7Days: { date: string; visits: number }[];
  dailyTrend30Days: { date: string; visits: number }[];
  hourlyTrendToday: { hour: number; visits: number }[];
  monthlyTrend: { year: number; month: number; visits: number }[];
  availableYears: number[];
}
