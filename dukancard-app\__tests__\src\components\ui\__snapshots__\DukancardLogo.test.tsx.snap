// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<DukancardLogo /> applies custom style prop 1`] = `
<View
  style={
    {
      "alignItems": "center",
    }
  }
>
  <View
    style={
      {
        "alignItems": "center",
      }
    }
  >
    <View
      style={
        {
          "alignItems": "center",
          "flexDirection": "row",
          "marginBottom": 6,
        }
      }
    >
      <Text
        style={
          {
            "color": "#C29D5B",
            "fontSize": 24,
            "fontWeight": "900",
            "letterSpacing": 1,
          }
        }
      >
        Dukan
      </Text>
      <Text
        style={
          {
            "color": "#000000",
            "fontSize": 24,
            "fontWeight": "900",
            "letterSpacing": 1,
          }
        }
      >
        card
      </Text>
    </View>
    <Text
      style={
        {
          "color": "#6b7280",
          "fontSize": 12,
          "fontWeight": "600",
          "letterSpacing": 2,
          "marginBottom": 16,
          "textTransform": "uppercase",
        }
      }
    >
      Connect • Discover • Grow
    </Text>
  </View>
</View>
`;

exports[`<DukancardLogo /> applies different size prop values 1`] = `
<View
  style={
    {
      "alignItems": "center",
    }
  }
>
  <View
    style={
      {
        "alignItems": "center",
      }
    }
  >
    <View
      style={
        {
          "alignItems": "center",
          "flexDirection": "row",
          "marginBottom": 6,
        }
      }
    >
      <Text
        style={
          {
            "color": "#C29D5B",
            "fontSize": 18,
            "fontWeight": "900",
            "letterSpacing": 0.5,
          }
        }
      >
        Dukan
      </Text>
      <Text
        style={
          {
            "color": "#000000",
            "fontSize": 18,
            "fontWeight": "900",
            "letterSpacing": 0.5,
          }
        }
      >
        card
      </Text>
    </View>
    <Text
      style={
        {
          "color": "#6b7280",
          "fontSize": 10,
          "fontWeight": "600",
          "letterSpacing": 2,
          "marginBottom": 8,
          "textTransform": "uppercase",
        }
      }
    >
      Connect • Discover • Grow
    </Text>
  </View>
</View>
`;

exports[`<DukancardLogo /> applies different size prop values 2`] = `
<View
  style={
    {
      "alignItems": "center",
    }
  }
>
  <View
    style={
      {
        "alignItems": "center",
      }
    }
  >
    <View
      style={
        {
          "alignItems": "center",
          "flexDirection": "row",
          "marginBottom": 6,
        }
      }
    >
      <Text
        style={
          {
            "color": "#C29D5B",
            "fontSize": 24,
            "fontWeight": "900",
            "letterSpacing": 1,
          }
        }
      >
        Dukan
      </Text>
      <Text
        style={
          {
            "color": "#000000",
            "fontSize": 24,
            "fontWeight": "900",
            "letterSpacing": 1,
          }
        }
      >
        card
      </Text>
    </View>
    <Text
      style={
        {
          "color": "#6b7280",
          "fontSize": 12,
          "fontWeight": "600",
          "letterSpacing": 2,
          "marginBottom": 16,
          "textTransform": "uppercase",
        }
      }
    >
      Connect • Discover • Grow
    </Text>
  </View>
</View>
`;

exports[`<DukancardLogo /> applies different size prop values 3`] = `
<View
  style={
    {
      "alignItems": "center",
    }
  }
>
  <View
    style={
      {
        "alignItems": "center",
      }
    }
  >
    <View
      style={
        {
          "alignItems": "center",
          "flexDirection": "row",
          "marginBottom": 6,
        }
      }
    >
      <Text
        style={
          {
            "color": "#C29D5B",
            "fontSize": 32,
            "fontWeight": "900",
            "letterSpacing": 1.2,
          }
        }
      >
        Dukan
      </Text>
      <Text
        style={
          {
            "color": "#000000",
            "fontSize": 32,
            "fontWeight": "900",
            "letterSpacing": 1.2,
          }
        }
      >
        card
      </Text>
    </View>
    <Text
      style={
        {
          "color": "#6b7280",
          "fontSize": 14,
          "fontWeight": "600",
          "letterSpacing": 2,
          "marginBottom": 20,
          "textTransform": "uppercase",
        }
      }
    >
      Connect • Discover • Grow
    </Text>
  </View>
</View>
`;

exports[`<DukancardLogo /> applies different size prop values 4`] = `
<View
  style={
    {
      "alignItems": "center",
    }
  }
>
  <View
    style={
      {
        "alignItems": "center",
      }
    }
  >
    <View
      style={
        {
          "alignItems": "center",
          "flexDirection": "row",
          "marginBottom": 6,
        }
      }
    >
      <Text
        style={
          {
            "color": "#C29D5B",
            "fontSize": 36,
            "fontWeight": "900",
            "letterSpacing": 1.5,
          }
        }
      >
        Dukan
      </Text>
      <Text
        style={
          {
            "color": "#000000",
            "fontSize": 36,
            "fontWeight": "900",
            "letterSpacing": 1.5,
          }
        }
      >
        card
      </Text>
    </View>
    <Text
      style={
        {
          "color": "#6b7280",
          "fontSize": 14,
          "fontWeight": "600",
          "letterSpacing": 2,
          "marginBottom": 24,
          "textTransform": "uppercase",
        }
      }
    >
      Connect • Discover • Grow
    </Text>
  </View>
</View>
`;

exports[`<DukancardLogo /> renders correctly with default props 1`] = `
<View
  style={
    {
      "alignItems": "center",
    }
  }
>
  <View
    style={
      {
        "alignItems": "center",
      }
    }
  >
    <View
      style={
        {
          "alignItems": "center",
          "flexDirection": "row",
          "marginBottom": 6,
        }
      }
    >
      <Text
        style={
          {
            "color": "#C29D5B",
            "fontSize": 24,
            "fontWeight": "900",
            "letterSpacing": 1,
          }
        }
      >
        Dukan
      </Text>
      <Text
        style={
          {
            "color": "#000000",
            "fontSize": 24,
            "fontWeight": "900",
            "letterSpacing": 1,
          }
        }
      >
        card
      </Text>
    </View>
    <Text
      style={
        {
          "color": "#6b7280",
          "fontSize": 12,
          "fontWeight": "600",
          "letterSpacing": 2,
          "marginBottom": 16,
          "textTransform": "uppercase",
        }
      }
    >
      Connect • Discover • Grow
    </Text>
  </View>
</View>
`;
