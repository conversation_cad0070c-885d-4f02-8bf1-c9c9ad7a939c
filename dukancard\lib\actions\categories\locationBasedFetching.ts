"use server";

import { createClient } from "@/utils/supabase/server";
import { BusinessProfilePublicData, BusinessSortBy } from "../businessProfiles/types";
import { NearbyProduct } from "@/app/(main)/discover/actions/types";

// Type for location parameters
export type LocationParams = {
  categoryName: string;
  state?: string | null;
  city?: string | null;
  pincode?: string | null;
  locality?: string | null;
  stateSlug?: string | null;
  citySlug?: string | null;
  localitySlug?: string | null;
  page?: number;
  limit?: number;
  sortBy?: BusinessSortBy;
  businessName?: string | null;
};

// Type for product location parameters
export type ProductLocationParams = LocationParams & {
  productType?: "physical" | "service" | null;
  productName?: string | null;
  productSortBy?: string;
};

/**
 * Unified function to fetch businesses based on location parameters
 */
export async function fetchBusinessesByLocation(params: LocationParams): Promise<{
  data?: BusinessProfilePublicData[];
  count?: number;
  error?: string;
}> {
  const {
    categoryName,
    state,
    city,
    pincode,
    locality,
    stateSlug,
    citySlug,
    localitySlug,
    page = 1,
    limit = 20,
    sortBy = "created_desc",
    businessName
  } = params;

  console.log(`fetchBusinessesByLocation called with pincode: ${pincode}, city: ${city}, state: ${state}, locality: ${locality}, stateSlug: ${stateSlug}, citySlug: ${citySlug}, localitySlug: ${localitySlug}`);

  try {
    // Use regular client - business_profiles has public read access
    const supabase = await createClient();
    const offset = (page - 1) * limit;

    // Build the query
    let query = supabase
      .from("business_profiles")
      .select(
        `
        id, business_name, logo_url, member_name, title,
        address_line, city, state, pincode, locality, phone, instagram_url,
        facebook_url, whatsapp_number, about_bio, status, business_slug,
        theme_color, delivery_info, business_hours, business_category, total_likes, total_subscriptions,
        average_rating, created_at, updated_at, contact_email
      `,
        { count: "exact" }
      )
      .eq("status", "online")
      .eq("business_category", categoryName);

    // Add location filters if provided
    if (pincode) {
      query = query.eq("pincode", pincode);
    }

    // Prefer slug-based filtering when available
    if (stateSlug) {
      query = query.eq("state_slug", stateSlug);
    } else if (state) {
      // Fall back to name-based filtering if slug not provided
      query = query.eq("state", state);
    }

    if (citySlug) {
      query = query.eq("city_slug", citySlug);
    } else if (city) {
      // Fall back to name-based filtering if slug not provided
      query = query.eq("city", city);
    }

    if (localitySlug) {
      query = query.eq("locality_slug", localitySlug);
    } else if (locality) {
      // Fall back to name-based filtering if slug not provided
      console.log(`Filtering businesses by exact locality: "${locality}"`);
      query = query.eq("locality", locality);
    }

    // Add business name search if provided
    if (businessName) {
      query = query.ilike("business_name", `%${businessName}%`);
    }

    // Add sorting
    const [column, direction] = sortBy.split('_');
    const isAscending = direction === 'asc';

    // Map column names to actual database column names
    let orderColumn = column;
    if (column === 'created') {
      orderColumn = 'created_at';
    } else if (column === 'updated') {
      orderColumn = 'updated_at';
    }

    query = query.order(orderColumn, { ascending: isAscending });

    // Execute the query with pagination
    const { data, count, error } = await query
      .range(offset, offset + limit - 1);

    if (error) {
      console.error("Error fetching businesses by location:", error);
      return { error: "Database error fetching businesses." };
    }

    return {
      data: data as BusinessProfilePublicData[],
      count: count || 0
    };
  } catch (error) {
    console.error("Unexpected error in fetchBusinessesByLocation:", error);
    return { error: "An unexpected error occurred." };
  }
}

/**
 * Unified function to fetch products based on location parameters
 */
export async function fetchProductsByLocation(params: ProductLocationParams): Promise<{
  data?: NearbyProduct[];
  count?: number;
  error?: string;
}> {
  const {
    categoryName,
    state,
    city,
    pincode,
    locality,
    stateSlug,
    citySlug,
    localitySlug,
    page = 1,
    limit = 20,
    productType,
    productName,
    productSortBy = "created_desc"
  } = params;

  console.log(`fetchProductsByLocation called with pincode: ${pincode}, city: ${city}, state: ${state}, stateSlug: ${stateSlug}, citySlug: ${citySlug}, localitySlug: ${localitySlug}`);

  try {
    // Use regular client - business_profiles has public read access
    const supabase = await createClient();
    const offset = (page - 1) * limit;

    // First, get business IDs that match the category and location
    let businessQuery = supabase
      .from("business_profiles")
      .select("id")
      .eq("status", "online")
      .eq("business_category", categoryName);

    // Add location filters if provided
    if (pincode) {
      businessQuery = businessQuery.eq("pincode", pincode);
    }

    // Prefer slug-based filtering when available
    if (stateSlug) {
      businessQuery = businessQuery.eq("state_slug", stateSlug);
    } else if (state) {
      // Fall back to name-based filtering if slug not provided
      businessQuery = businessQuery.eq("state", state);
    }

    if (citySlug) {
      businessQuery = businessQuery.eq("city_slug", citySlug);
    } else if (city) {
      // Fall back to name-based filtering if slug not provided
      businessQuery = businessQuery.eq("city", city);
    }

    if (localitySlug) {
      businessQuery = businessQuery.eq("locality_slug", localitySlug);
    } else if (locality) {
      // Fall back to name-based filtering if slug not provided
      console.log(`Filtering products by exact locality: "${locality}"`);
      businessQuery = businessQuery.eq("locality", locality);
    }

    const { data: businesses, error: businessError } = await businessQuery;

    if (businessError) {
      console.error("Error fetching businesses for products:", businessError);
      return { error: "Failed to fetch businesses for products." };
    }

    // If no businesses found, return empty result
    if (!businesses || businesses.length === 0) {
      return {
        data: [],
        count: 0
      };
    }

    // Get business IDs
    const businessIds = businesses.map((b: { id: string }) => b.id);

    // Now fetch products from these businesses
    let productQuery = supabase
      .from("products_services")
      .select(
        `
        *,
        business_profiles:business_id (
          business_slug
        )
      `,
        { count: "exact" }
      )
      .in("business_id", businessIds)
      .eq("is_available", true);

    // Add product type filter if provided
    if (productType) {
      productQuery = productQuery.eq("product_type", productType);
    }

    // Add product name search if provided
    if (productName) {
      productQuery = productQuery.ilike("name", `%${productName}%`);
    }

    // Add sorting
    const [column, direction] = productSortBy.split('_');
    const isAscending = direction === 'asc';

    // Map column names to actual database column names
    let orderColumn = column;
    if (column === 'created') {
      orderColumn = 'created_at';
    } else if (column === 'updated') {
      orderColumn = 'updated_at';
    } else if (column === 'price') {
      // Special handling for price sorting to use discounted_price when available, otherwise base_price
      if (isAscending) {
        productQuery = productQuery.order('discounted_price', { ascending: true, nullsFirst: false })
                          .order('base_price', { ascending: true, nullsFirst: false });
      } else {
        productQuery = productQuery.order('discounted_price', { ascending: false, nullsFirst: false })
                          .order('base_price', { ascending: false, nullsFirst: false });
      }
    } else if (column === 'newest') {
      // Handle 'newest' as a special case - sort by created_at descending
      productQuery = productQuery.order('created_at', { ascending: false });
      // Skip the generic ordering below since we've already applied a special case
    } else {
      // Only apply generic ordering if we haven't applied a special case above
      productQuery = productQuery.order(orderColumn, { ascending: isAscending });
    }

    // Execute the query with pagination
    const { data: products, count: productCount, error: productError } = await productQuery
      .range(offset, offset + limit - 1);

    if (productError) {
      console.error("Error fetching products:", productError);
      return { error: "Failed to fetch products." };
    }

    // Transform the products to include business_slug
    const transformedProducts = products.map((product: { business_profiles?: { business_slug?: string }; [key: string]: unknown }) => {
      // Extract business_slug from the nested business_profiles object
      const businessSlug = product.business_profiles?.business_slug;

      // Create a new object without the nested business_profiles property
      // to avoid issues with serialization and type conflicts
      const { ...productData } = product;

      return {
        ...productData,
        business_slug: businessSlug || null
      } as NearbyProduct;
    });

    return {
      data: transformedProducts,
      count: productCount || 0
    };
  } catch (error) {
    console.error("Unexpected error in fetchProductsByLocation:", error);
    return { error: "An unexpected error occurred." };
  }
}
