import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Switch,
} from "react-native";
import { Controller, useFormContext } from "react-hook-form";
import { Clock, Truck } from "lucide-react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { FormField } from "@/src/components/ui/FormField";
import { useToast } from "@/src/components/ui/Toast";
import { updateBusinessDetails } from "@/backend/supabase/services/business/businessCardService";
import { BusinessDetailsData, businessDetailsSchema } from "@/backend/supabase/services/business/schemas";
import { createBusinessDetailsSectionStyles } from "@/styles/modals/business/sections/business-details-section";

// Business hours types
export interface DayHours {
  isOpen: boolean;
  openTime: string;
  closeTime: string;
}

export interface BusinessHours {
  monday: DayHours;
  tuesday: DayHours;
  wednesday: DayHours;
  thursday: DayHours;
  friday: DayHours;
  saturday: DayHours;
  sunday: DayHours;
}

const EMPTY_HOURS: DayHours = {
  isOpen: false,
  openTime: "09:00",
  closeTime: "18:00",
};

const EMPTY_BUSINESS_HOURS: BusinessHours = {
  monday: { ...EMPTY_HOURS },
  tuesday: { ...EMPTY_HOURS },
  wednesday: { ...EMPTY_HOURS },
  thursday: { ...EMPTY_HOURS },
  friday: { ...EMPTY_HOURS },
  saturday: { ...EMPTY_HOURS },
  sunday: { ...EMPTY_HOURS },
};

interface BusinessDetailsSectionProps {
  onBack: () => void;
}

export default function BusinessDetailsSection({ onBack }: BusinessDetailsSectionProps) {
  const theme = useTheme();
  const styles = createBusinessDetailsSectionStyles(theme);
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const { control, handleSubmit, watch, setValue } = useFormContext();
  const currentBusinessHours = watch("business_hours") || EMPTY_BUSINESS_HOURS;

  // Day groups for easier management
  const dayGroups = {
    weekdays: {
      label: "Mon-Fri",
      days: ["monday", "tuesday", "wednesday", "thursday", "friday"] as (keyof BusinessHours)[],
    },
    weekend: {
      label: "Sat-Sun", 
      days: ["saturday", "sunday"] as (keyof BusinessHours)[],
    },
  };

  const formatTime = (time: string) => {
    const [hour, minute] = time.split(':');
    const hourNum = parseInt(hour);
    const period = hourNum >= 12 ? 'PM' : 'AM';
    const displayHour = hourNum > 12 ? hourNum - 12 : hourNum === 0 ? 12 : hourNum;
    return `${displayHour}:${minute} ${period}`;
  };

  const updateGroupHours = (
    groupKey: 'weekdays' | 'weekend',
    isOpen: boolean,
    openTime?: string,
    closeTime?: string
  ) => {
    const group = dayGroups[groupKey];
    const newHours = { ...currentBusinessHours };
    
    group.days.forEach((day) => {
      newHours[day] = {
        isOpen,
        openTime: openTime || newHours[day].openTime,
        closeTime: closeTime || newHours[day].closeTime,
      };
    });

    setValue("business_hours", newHours);
  };

  const getGroupStatus = (groupKey: 'weekdays' | 'weekend') => {
    const group = dayGroups[groupKey];
    const openDays = group.days.filter(day => currentBusinessHours[day]?.isOpen);
    return {
      isOpen: openDays.length > 0,
      allOpen: openDays.length === group.days.length,
      someOpen: openDays.length > 0 && openDays.length < group.days.length,
      openTime: openDays.length > 0 ? currentBusinessHours[openDays[0]].openTime : "09:00",
      closeTime: openDays.length > 0 ? currentBusinessHours[openDays[0]].closeTime : "18:00",
    };
  };

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    
    // Extract only business details fields
    const businessDetailsData: BusinessDetailsData = {
      business_hours: data.business_hours,
      delivery_info: data.delivery_info,
    };

    // Validate the data
    const validation = businessDetailsSchema.safeParse(businessDetailsData);
    if (!validation.success) {
      toast.error("Validation Error", "Please check all fields.");
      setIsLoading(false);
      return;
    }

    // Convert null values to undefined for API compatibility
    const apiData = {
      ...businessDetailsData,
      delivery_info: businessDetailsData.delivery_info || undefined,
    };

    const { success, error } = await updateBusinessDetails(apiData);
    if (success) {
      toast.success("Success", "Business details updated successfully.");
      onBack();
    } else {
      toast.error("Error", error || "Failed to update business details.");
    }
    setIsLoading(false);
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {/* Business Hours Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Business Hours</Text>
          
          {/* Weekdays */}
          <View style={styles.hoursGroup}>
            <View style={styles.hoursGroupHeader}>
              <View style={styles.hoursGroupInfo}>
                <Clock size={20} color={theme.colors.primary} />
                <Text style={styles.hoursGroupTitle}>{dayGroups.weekdays.label}</Text>
              </View>
              <Switch
                value={getGroupStatus('weekdays').isOpen}
                onValueChange={(isOpen) => updateGroupHours('weekdays', isOpen)}
                trackColor={{ false: "#767577", true: theme.colors.primary }}
                thumbColor={getGroupStatus('weekdays').isOpen ? "#FFF" : "#f4f3f4"}
              />
            </View>
            
            {getGroupStatus('weekdays').isOpen && (
              <View style={styles.hoursGroupContent}>
                <View style={styles.timeRow}>
                  <Text style={styles.timeLabel}>Hours:</Text>
                  <View style={styles.timeInputs}>
                    <TouchableOpacity style={styles.timeButton}>
                      <Text style={styles.timeButtonText}>
                        {formatTime(getGroupStatus('weekdays').openTime)}
                      </Text>
                    </TouchableOpacity>
                    <Text style={styles.timeSeparator}>to</Text>
                    <TouchableOpacity style={styles.timeButton}>
                      <Text style={styles.timeButtonText}>
                        {formatTime(getGroupStatus('weekdays').closeTime)}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            )}
          </View>

          {/* Weekend */}
          <View style={styles.hoursGroup}>
            <View style={styles.hoursGroupHeader}>
              <View style={styles.hoursGroupInfo}>
                <Clock size={20} color={theme.colors.primary} />
                <Text style={styles.hoursGroupTitle}>{dayGroups.weekend.label}</Text>
              </View>
              <Switch
                value={getGroupStatus('weekend').isOpen}
                onValueChange={(isOpen) => updateGroupHours('weekend', isOpen)}
                trackColor={{ false: "#767577", true: theme.colors.primary }}
                thumbColor={getGroupStatus('weekend').isOpen ? "#FFF" : "#f4f3f4"}
              />
            </View>
            
            {getGroupStatus('weekend').isOpen && (
              <View style={styles.hoursGroupContent}>
                <View style={styles.timeRow}>
                  <Text style={styles.timeLabel}>Hours:</Text>
                  <View style={styles.timeInputs}>
                    <TouchableOpacity style={styles.timeButton}>
                      <Text style={styles.timeButtonText}>
                        {formatTime(getGroupStatus('weekend').openTime)}
                      </Text>
                    </TouchableOpacity>
                    <Text style={styles.timeSeparator}>to</Text>
                    <TouchableOpacity style={styles.timeButton}>
                      <Text style={styles.timeButtonText}>
                        {formatTime(getGroupStatus('weekend').closeTime)}
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            )}
          </View>
        </View>

        {/* Delivery Information Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Delivery Information</Text>
          
          <Controller
            control={control}
            name="delivery_info"
            render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
              <FormField
                label="Delivery Details"
                value={value || ""}
                onChangeText={onChange}
                onBlur={onBlur}
                error={error?.message}
                multiline
                numberOfLines={4}
                placeholder="Describe your delivery options, areas covered, charges, etc."

              />
            )}
          />
        </View>

        <View style={styles.infoBox}>
          <Text style={styles.infoText}>
            💡 Tip: Clear business hours and delivery information help customers plan their visits and orders better.
          </Text>
        </View>
      </ScrollView>

      {/* Save Button */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.saveButton,
            { backgroundColor: theme.colors.primary },
            isLoading && styles.saveButtonDisabled,
          ]}
          onPress={handleSubmit(onSubmit)}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="white" />
          ) : (
            <Text style={styles.saveButtonText}>Save Business Details</Text>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
}
