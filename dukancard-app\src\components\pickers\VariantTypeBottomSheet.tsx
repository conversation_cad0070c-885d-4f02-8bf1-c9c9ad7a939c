import React, { useState, useEffect, forwardRef, useImperativeHandle } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  Modal,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { Search, X, Check } from "lucide-react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { createVariantTypeBottomSheetStyles } from "@/styles/pickers/variant-type-bottom-sheet";
import { getAllVariantTypes } from "@/src/constants/predefinedVariants";

interface VariantType {
  id: string;
  name: string;
  display_name: string;
  description?: string;
  is_predefined: boolean;
  sort_order: number;
}

interface VariantTypeBottomSheetProps {
  selectedType?: string;
  onSelect: (type: VariantType) => void;
  onClose: () => void;
}

export interface VariantTypeBottomSheetRef {
  open: () => void;
  close: () => void;
}

const VariantTypeBottomSheet = forwardRef<VariantTypeBottomSheetRef, VariantTypeBottomSheetProps>(
  ({ selectedType, onSelect, onClose }, ref) => {
    const theme = useTheme();
    const styles = createVariantTypeBottomSheetStyles(theme);
    
    const [visible, setVisible] = useState(false);
    const [searchQuery, setSearchQuery] = useState("");
    const [variantTypes, setVariantTypes] = useState<VariantType[]>([]);
    const [filteredTypes, setFilteredTypes] = useState<VariantType[]>([]);

    useImperativeHandle(ref, () => ({
      open: () => setVisible(true),
      close: () => setVisible(false),
    }));

    useEffect(() => {
      const types = getAllVariantTypes();
      setVariantTypes(types);
      setFilteredTypes(types);
    }, []);

    useEffect(() => {
      if (!searchQuery.trim()) {
        setFilteredTypes(variantTypes);
      } else {
        const filtered = variantTypes.filter(
          (type) =>
            type.display_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            type.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            (type.description && type.description.toLowerCase().includes(searchQuery.toLowerCase()))
        );
        setFilteredTypes(filtered);
      }
    }, [searchQuery, variantTypes]);

    const handleClose = () => {
      setVisible(false);
      setSearchQuery("");
      onClose();
    };

    const handleSelect = (type: VariantType) => {
      onSelect(type);
      handleClose();
    };

    const renderVariantType = ({ item }: { item: VariantType }) => {
      const isSelected = selectedType === item.name;

      return (
        <TouchableOpacity
          style={[styles.typeItem, isSelected && styles.typeItemSelected]}
          onPress={() => handleSelect(item)}
          activeOpacity={0.7}
        >
          <View style={styles.typeInfo}>
            <Text style={[styles.typeName, isSelected && styles.typeNameSelected]}>
              {item.display_name}
            </Text>
            {item.description && (
              <Text style={[styles.typeDescription, isSelected && styles.typeDescriptionSelected]}>
                {item.description}
              </Text>
            )}
          </View>
          {isSelected && (
            <Check size={20} color={theme.colors.primary} />
          )}
        </TouchableOpacity>
      );
    };

    return (
      <Modal
        visible={visible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={handleClose}
      >
        <SafeAreaView style={styles.safeArea}>
          <KeyboardAvoidingView
            style={styles.container}
            behavior={Platform.OS === "ios" ? "padding" : "height"}
          >
            {/* Header */}
            <View style={styles.header}>
              <Text style={styles.headerTitle}>Select Variant Type</Text>
              <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
                <X size={24} color={theme.colors.foreground} />
              </TouchableOpacity>
            </View>

            {/* Search */}
            <View style={styles.searchContainer}>
              <View style={styles.searchInputContainer}>
                <Search size={20} color={theme.colors.mutedForeground} />
                <TextInput
                  style={styles.searchInput}
                  placeholder="Search variant types..."
                  placeholderTextColor={theme.colors.mutedForeground}
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                  autoCapitalize="none"
                />
                {searchQuery.length > 0 && (
                  <TouchableOpacity
                    style={styles.clearButton}
                    onPress={() => setSearchQuery("")}
                  >
                    <X size={16} color={theme.colors.mutedForeground} />
                  </TouchableOpacity>
                )}
              </View>
            </View>

            {/* Types List */}
            <FlatList
              data={filteredTypes}
              renderItem={renderVariantType}
              keyExtractor={(item) => item.id}
              style={styles.list}
              contentContainerStyle={styles.listContent}
              showsVerticalScrollIndicator={false}
              ItemSeparatorComponent={() => <View style={styles.separator} />}
            />

            {filteredTypes.length === 0 && searchQuery.length > 0 && (
              <View style={styles.emptyState}>
                <Text style={styles.emptyStateText}>
                  No variant types found for &quot;{searchQuery}&quot;
                </Text>
              </View>
            )}
          </KeyboardAvoidingView>
        </SafeAreaView>
      </Modal>
    );
  }
);

VariantTypeBottomSheet.displayName = "VariantTypeBottomSheet";

export default VariantTypeBottomSheet;
