"use client";

import { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { createClient } from "@/utils/supabase/client";
import { Loader2 } from "lucide-react";

export default function AuthCallbackClient() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const supabase = createClient();

  useEffect(() => {
    const handleAuth = async () => {
      // Check if this window should close itself after auth (from popup flow)
      const shouldCloseWindow = searchParams.get('closeWindow') === 'true';

      // Get user session
      const { data: authData, error: authError } = await supabase.auth.getUser();

      // Handle auth errors or no user
      if (authError || !authData?.user) {
        if (shouldCloseWindow) {
          // If this is a popup window and auth failed, close it and let the opener handle it
          window.close();
          return;
        }

        router.push("/login"); // Redirect to login on auth error
        return;
      }

      const userId = authData.user.id;
      // Check if we have redirect and message parameters
      const redirectSlug = searchParams.get('redirect');
      const messageParam = searchParams.get('message');
      const nextParam = searchParams.get('next');



      let redirectPath;

      try {
        // Check if this is a new user or existing user
        const { getPostLoginRedirectPath } = await import("@/lib/actions/redirectAfterLogin");
        const defaultRedirectPath = await getPostLoginRedirectPath(supabase, userId);

        // Determine user type - needed for both redirect and non-redirect cases
        const isNewUser = defaultRedirectPath === "/choose-role" || defaultRedirectPath === "/onboarding";
        const isBusinessUser = userId.startsWith("business_") || defaultRedirectPath.includes("/business");



        if (redirectSlug) {
          // If we have a redirect slug, we need to handle based on user type

          if (isNewUser) {
            // For new users, we need to determine if they're a business user or customer
            // and handle the redirect accordingly

            // Get the redirect slug from either the redirect parameter or the next parameter
            const effectiveRedirectSlug = redirectSlug || (nextParam && nextParam !== '/onboarding' && nextParam !== '/choose-role' ? nextParam.replace(/^\//, '') : null);



            if (isBusinessUser) {
              // New business users need to complete onboarding first
              // Store the redirect slug and message in localStorage to use after onboarding
              if (typeof window !== 'undefined' && effectiveRedirectSlug) {
                localStorage.setItem('postOnboardingRedirect', effectiveRedirectSlug);
                if (messageParam) {
                  localStorage.setItem('postOnboardingMessage', messageParam);
                }

              }

              redirectPath = defaultRedirectPath;
            } else {
              // New customer users need to choose their role first
              // Append the redirect and message parameters to the choose-role URL
              let chooseRolePath = defaultRedirectPath;

              // Always store the redirect in localStorage as a backup
              if (typeof window !== 'undefined' && effectiveRedirectSlug) {
                localStorage.setItem('chooseRoleRedirect', effectiveRedirectSlug);
                if (messageParam) {
                  localStorage.setItem('chooseRoleMessage', messageParam);
                }

              }

              // Also append to URL
              if (effectiveRedirectSlug) {
                chooseRolePath += `?redirect=${encodeURIComponent(effectiveRedirectSlug)}`;
                if (messageParam) {
                  chooseRolePath += `&message=${encodeURIComponent(messageParam)}`;
                }

              }


              redirectPath = chooseRolePath;
            }
          } else {
            // Existing users or new customer users can go directly to the card
            // Include message parameter if available
            if (messageParam) {
              redirectPath = `/${redirectSlug}?message=${encodeURIComponent(messageParam)}`;
            } else {
              redirectPath = `/${redirectSlug}`;
            }
          }
        } else {
          // No redirect slug, check if we have a next parameter that's not onboarding or choose-role
          const effectiveNextParam = nextParam && nextParam !== '/onboarding' && nextParam !== '/choose-role' ? nextParam : null;

          if (effectiveNextParam && isNewUser) {
            // If we have a next parameter and this is a new user, handle it like a redirect
            const nextParamWithoutSlash = effectiveNextParam.replace(/^\//, '');

            if (isBusinessUser) {
              // New business users need to complete onboarding first
              if (typeof window !== 'undefined') {
                localStorage.setItem('postOnboardingRedirect', nextParamWithoutSlash);
                if (messageParam) {
                  localStorage.setItem('postOnboardingMessage', messageParam);
                }

              }
              redirectPath = defaultRedirectPath;
            } else {
              // New customer users need to choose their role first
              // Store the redirect in localStorage as a backup
              if (typeof window !== 'undefined') {
                localStorage.setItem('chooseRoleRedirect', nextParamWithoutSlash);
                if (messageParam) {
                  localStorage.setItem('chooseRoleMessage', messageParam);
                }
              }

              let chooseRolePath = defaultRedirectPath;
              chooseRolePath += `?redirect=${encodeURIComponent(nextParamWithoutSlash)}`;
              if (messageParam) {
                chooseRolePath += `&message=${encodeURIComponent(messageParam)}`;
              }
              redirectPath = chooseRolePath;
            }
          } else {
            // No redirect or next parameter, use the default redirect logic
            redirectPath = defaultRedirectPath;

            if (redirectPath === "/") {
              console.warn(
                "Auth callback: couldn't determine dashboard. Redirecting to home."
              );
            }
          }


        }
      } catch (error) {
        console.error("Unexpected error in shared redirect logic:", error);
        redirectPath = "/?view=home"; // Fallback redirect
      }

      // Handle window closing or redirection based on the closeWindow parameter
      if (shouldCloseWindow && window.opener) {
        // If this is a popup window, close it and redirect the opener
        try {
          // Try to redirect the opener window if possible
          if (window.opener.location.origin === window.location.origin) {
            // Only redirect if same origin for security
            window.opener.location.href = redirectPath;
          }
        } catch (e) {
          // If we can't access the opener due to cross-origin restrictions,
          // just close this window and let the opener handle it
          console.warn("Could not redirect opener window:", e);
        }

        // Close this window after a short delay to ensure the message is visible
        setTimeout(() => {
          window.close();
        }, 500);
      } else {
        // Normal flow - redirect in the current window
        router.push(redirectPath || '/?view=home');
      }
    };

    handleAuth();
    // Include searchParams in the dependencies array since we're using it now
  }, [router, supabase, searchParams]);

  // Check if this is a popup window that will close itself
  const isPopupWindow = searchParams.get('closeWindow') === 'true';

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <Loader2 className="mx-auto h-8 w-8 text-[var(--brand-gold)] animate-spin" />
        <p className="mt-4 text-lg font-medium">Authenticating...</p>
        {isPopupWindow && (
          <p className="mt-2 text-sm text-muted-foreground">
            This window will close automatically after sign-in is complete.
          </p>
        )}
      </div>
    </div>
  );
}
