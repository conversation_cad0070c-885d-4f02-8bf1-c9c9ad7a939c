import { StyleSheet } from "react-native";

export const createDeleteAccountModalStyles = (theme: any) => {
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: "600",
      color: theme.colors.foreground,
    },
    closeButton: {
      padding: theme.spacing.xs,
    },
    content: {
      flex: 1,
      paddingHorizontal: theme.spacing.md,
    },
    warningSection: {
      alignItems: "center",
      paddingVertical: theme.spacing.xl,
      marginBottom: theme.spacing.lg,
    },
    warningTitle: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: "700",
      color: "#ff4444",
      marginTop: theme.spacing.md,
      marginBottom: theme.spacing.sm,
    },
    warningText: {
      fontSize: theme.typography.fontSize.base,
      color: theme.colors.textSecondary,
      textAlign: "center",
      lineHeight: 22,
    },
    instructionsSection: {
      marginBottom: theme.spacing.xl,
    },
    sectionTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: "600",
      color: theme.colors.foreground,
      marginBottom: theme.spacing.lg,
    },
    stepContainer: {
      flexDirection: "row",
      marginBottom: theme.spacing.lg,
    },
    stepNumber: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: "#007AFF",
      justifyContent: "center",
      alignItems: "center",
      marginRight: theme.spacing.md,
    },
    stepNumberText: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: "700",
      color: "#fff",
    },
    stepContent: {
      flex: 1,
    },
    stepTitle: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
      color: theme.colors.foreground,
      marginBottom: theme.spacing.xs,
    },
    stepDescription: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
      lineHeight: 20,
    },
    deletionInfoSection: {
      marginBottom: theme.spacing.xl,
      padding: theme.spacing.md,
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.lg,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    deletionList: {
      marginTop: theme.spacing.sm,
    },
    deletionItem: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing.xs,
      lineHeight: 20,
    },
    supportSection: {
      marginBottom: theme.spacing.xl,
      padding: theme.spacing.md,
      backgroundColor: theme.isDark ? "#1a1a1a" : "#f8f9fa",
      borderRadius: theme.borderRadius.lg,
    },
    supportText: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
      lineHeight: 20,
    },
    footer: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.md,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    websiteButton: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: "#007AFF",
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.lg,
      borderRadius: theme.borderRadius.lg,
      gap: theme.spacing.sm,
    },
    websiteButtonText: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
      color: "#fff",
    },
  });
};
