import { StyleSheet } from "react-native";
import { useTheme } from "@/src/hooks/useTheme";

export const createBasicInfoSectionStyles = (
  theme: ReturnType<typeof useTheme>
) => {
  const { isDark } = theme;
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: isDark ? "#000" : "#FFF",
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      padding: theme.spacing.md,
      paddingBottom: theme.spacing.xl,
    },
    section: {
      marginBottom: theme.spacing.lg,
    },
    sectionTitle: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
      color: isDark ? "#FFF" : "#000",
      marginBottom: theme.spacing.sm,
    },
    
    // Logo upload styles
    logoContainer: {
      alignItems: "center",
      marginBottom: theme.spacing.md,
    },
    logoUploadArea: {
      width: 120,
      height: 120,
      borderRadius: 60,
      borderWidth: 2,
      borderColor: theme.colors.border,
      borderStyle: "dashed",
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: isDark ? "rgba(255, 255, 255, 0.05)" : "rgba(0, 0, 0, 0.02)",
    },
    logoImage: {
      width: 116,
      height: 116,
      borderRadius: 58,
    },
    logoPlaceholder: {
      alignItems: "center",
      justifyContent: "center",
    },
    logoPlaceholderText: {
      fontSize: theme.typography.fontSize.xs,
      color: theme.colors.textSecondary,
      marginTop: theme.spacing.xs,
      textAlign: "center",
    },
    
    // Footer styles
    footer: {
      padding: theme.spacing.md,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      backgroundColor: isDark ? "#000" : "#FFF",
    },
    saveButton: {
      borderRadius: theme.borderRadius.lg,
      paddingVertical: theme.spacing.md,
      alignItems: "center",
      justifyContent: "center",
      minHeight: 50,
    },
    saveButtonDisabled: {
      opacity: 0.6,
    },
    saveButtonText: {
      color: "white",
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
    },
  });
};
