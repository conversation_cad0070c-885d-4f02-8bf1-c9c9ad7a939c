// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<Button /> renders correctly with default props 1`] = `
<TouchableOpacity
  activeOpacity={0.8}
  disabled={false}
  onPress={[MockFunction]}
  style={
    [
      {
        "alignItems": "center",
        "backgroundColor": "#000000",
        "borderRadius": 12,
        "flexDirection": "row",
        "gap": 8,
        "justifyContent": "center",
        "minHeight": 48,
        "paddingHorizontal": 20,
        "paddingVertical": 14,
      },
      undefined,
      false,
    ]
  }
>
  <Text
    style={
      {
        "color": "#FFFFFF",
        "fontSize": 16,
        "fontWeight": "600",
      }
    }
  >
    Test Button
  </Text>
</TouchableOpacity>
`;

exports[`<Button /> variants and sizes renders large size correctly 1`] = `
<TouchableOpacity
  activeOpacity={0.8}
  disabled={false}
  onPress={[MockFunction]}
  style={
    [
      {
        "alignItems": "center",
        "backgroundColor": "#000000",
        "borderRadius": 12,
        "flexDirection": "row",
        "gap": 8,
        "justifyContent": "center",
        "minHeight": 56,
        "paddingHorizontal": 24,
        "paddingVertical": 18,
      },
      undefined,
      false,
    ]
  }
>
  <Text
    style={
      {
        "color": "#FFFFFF",
        "fontSize": 18,
        "fontWeight": "600",
      }
    }
  >
    Test Button
  </Text>
</TouchableOpacity>
`;

exports[`<Button /> variants and sizes renders medium size correctly 1`] = `
<TouchableOpacity
  activeOpacity={0.8}
  disabled={false}
  onPress={[MockFunction]}
  style={
    [
      {
        "alignItems": "center",
        "backgroundColor": "#000000",
        "borderRadius": 12,
        "flexDirection": "row",
        "gap": 8,
        "justifyContent": "center",
        "minHeight": 48,
        "paddingHorizontal": 20,
        "paddingVertical": 14,
      },
      undefined,
      false,
    ]
  }
>
  <Text
    style={
      {
        "color": "#FFFFFF",
        "fontSize": 16,
        "fontWeight": "600",
      }
    }
  >
    Test Button
  </Text>
</TouchableOpacity>
`;

exports[`<Button /> variants and sizes renders outline variant correctly 1`] = `
<TouchableOpacity
  activeOpacity={0.8}
  disabled={false}
  onPress={[MockFunction]}
  style={
    [
      {
        "alignItems": "center",
        "backgroundColor": "transparent",
        "borderColor": "#CCCCCC",
        "borderRadius": 12,
        "borderWidth": 1,
        "flexDirection": "row",
        "gap": 8,
        "justifyContent": "center",
        "minHeight": 48,
        "paddingHorizontal": 20,
        "paddingVertical": 14,
      },
      undefined,
      false,
    ]
  }
>
  <Text
    style={
      {
        "color": "#C29D5B",
        "fontSize": 16,
        "fontWeight": "600",
      }
    }
  >
    Test Button
  </Text>
</TouchableOpacity>
`;

exports[`<Button /> variants and sizes renders primary variant correctly 1`] = `
<TouchableOpacity
  activeOpacity={0.8}
  disabled={false}
  onPress={[MockFunction]}
  style={
    [
      {
        "alignItems": "center",
        "backgroundColor": "#000000",
        "borderRadius": 12,
        "flexDirection": "row",
        "gap": 8,
        "justifyContent": "center",
        "minHeight": 48,
        "paddingHorizontal": 20,
        "paddingVertical": 14,
      },
      undefined,
      false,
    ]
  }
>
  <Text
    style={
      {
        "color": "#FFFFFF",
        "fontSize": 16,
        "fontWeight": "600",
      }
    }
  >
    Test Button
  </Text>
</TouchableOpacity>
`;

exports[`<Button /> variants and sizes renders secondary variant correctly 1`] = `
<TouchableOpacity
  activeOpacity={0.8}
  disabled={false}
  onPress={[MockFunction]}
  style={
    [
      {
        "alignItems": "center",
        "backgroundColor": "#f8f9fa",
        "borderColor": "#C29D5B",
        "borderRadius": 12,
        "borderWidth": 1,
        "flexDirection": "row",
        "gap": 8,
        "justifyContent": "center",
        "minHeight": 48,
        "paddingHorizontal": 20,
        "paddingVertical": 14,
      },
      undefined,
      false,
    ]
  }
>
  <Text
    style={
      {
        "color": "#C29D5B",
        "fontSize": 16,
        "fontWeight": "600",
      }
    }
  >
    Test Button
  </Text>
</TouchableOpacity>
`;

exports[`<Button /> variants and sizes renders small size correctly 1`] = `
<TouchableOpacity
  activeOpacity={0.8}
  disabled={false}
  onPress={[MockFunction]}
  style={
    [
      {
        "alignItems": "center",
        "backgroundColor": "#000000",
        "borderRadius": 12,
        "flexDirection": "row",
        "gap": 8,
        "justifyContent": "center",
        "minHeight": 40,
        "paddingHorizontal": 16,
        "paddingVertical": 10,
      },
      undefined,
      false,
    ]
  }
>
  <Text
    style={
      {
        "color": "#FFFFFF",
        "fontSize": 14,
        "fontWeight": "600",
      }
    }
  >
    Test Button
  </Text>
</TouchableOpacity>
`;
