import * as z from "zod";

// Base Zod object schema
const baseProductServiceSchemaObject = {
  id: z.string().uuid().optional(),
  business_id: z.string().uuid().optional(),
  product_type: z.enum(["physical", "service"]).default("physical"),
  name: z
    .string()
    .min(1, { message: "Product/Service name is required." })
    .max(100, { message: "Name cannot exceed 100 characters." }),
  description: z
    .string()
    .max(500, { message: "Description cannot exceed 500 characters." })
    .optional()
    .or(z.literal("")),
  base_price: z.coerce
    .number({
      required_error: "Base price is required.",
      invalid_type_error: "Base price must be a number.",
    })
    .positive({ message: "Base price must be positive." }),
  discounted_price: z.coerce
    .number({ invalid_type_error: "Discounted price must be a number." })
    .positive({ message: "Discounted price must be positive." })
    .optional()
    .nullable(),
  is_available: z.boolean().default(true),
  image_url: z
    .string()
    .url({ message: "Invalid image URL format." })
    .optional()
    .nullable(),
  images: z.array(z.string()).optional().nullable(),
  featured_image_index: z.number().int().min(0).optional().default(0),
  slug: z.string().optional(),
  created_at: z.date().optional(),
  updated_at: z.date().optional(),
};

// Base object schema without refinement
export const baseProductServiceObjectSchema = z.object(
  baseProductServiceSchemaObject
);

// Schema for ADD operations
export const addProductFormSchema = baseProductServiceObjectSchema
  .omit({
    id: true,
    business_id: true,
    created_at: true,
    updated_at: true,
    image_url: true,
  })
  .refine(
    (data) =>
      !data.base_price ||
      !data.discounted_price ||
      data.discounted_price < data.base_price,
    {
      message: "Discounted price must be less than base price.",
      path: ["discounted_price"],
    }
  );

// Schema for UPDATE operations
export const updateProductFormSchema = baseProductServiceObjectSchema
  .partial()
  .refine(
    (data) =>
      !data.base_price ||
      !data.discounted_price ||
      data.discounted_price < data.base_price,
    {
      message: "Discounted price must be less than base price.",
      path: ["discounted_price"],
    }
  );

// Final type for data returned by actions
export type ProductServiceData = z.infer<typeof baseProductServiceObjectSchema>;

// Business Card Schemas
export const businessCardSchema = z.object({
  business_name: z.string().min(1, "Business name is required"),
  member_name: z.string().min(1, "Member name is required"),
  title: z.string().min(1, "Title is required"),
  logo_url: z.string().url().optional().nullable(),
  address_line: z.string().min(1, "Address is required"),
  city: z.string().min(1, "City is required"),
  state: z.string().min(1, "State is required"),
  pincode: z.string().length(6, "Pincode must be 6 digits"),
  locality: z.string().min(1, "Locality is required"),
  phone: z.string().length(10, "Phone number must be 10 digits"),
  instagram_url: z.string().url().optional().nullable(),
  facebook_url: z.string().url().optional().nullable(),
  whatsapp_number: z
    .string()
    .length(10, "WhatsApp number must be 10 digits")
    .optional()
    .nullable(),
  about_bio: z.string().max(250, "About/bio cannot exceed 250 characters"),
  status: z.enum(["online", "offline"]),
  business_slug: z.string().min(3, "Slug must be at least 3 characters"),
  theme_color: z.string().regex(/^#[0-9a-fA-F]{6}$/, "Invalid theme color"),
  delivery_info: z.string().optional().nullable(),
  business_hours: z.any().optional().nullable(),
  contact_email: z.string().email("Invalid email address"),
  business_category: z.string().optional().nullable(),
  established_year: z
    .number()
    .int()
    .min(1900)
    .max(new Date().getFullYear())
    .optional()
    .nullable(),
  custom_branding: z.any().optional().nullable(),
  custom_ads: z.any().optional().nullable(),
});

export type BusinessCardData = z.infer<typeof businessCardSchema>;

// Section-specific schemas for individual updates
export const basicInfoSchema = z.object({
  business_name: z.string().min(1, "Business name is required"),
  member_name: z.string().min(1, "Member name is required"),
  title: z.string().min(1, "Title is required"),
  logo_url: z.string().url().optional().nullable(),
  business_category: z.string().optional().nullable(),
  established_year: z
    .number()
    .int()
    .min(1900)
    .max(new Date().getFullYear())
    .optional()
    .nullable(),
  about_bio: z.string().max(250, "About/bio cannot exceed 250 characters"),
});

export const contactLocationSchema = z.object({
  phone: z.string().length(10, "Phone number must be 10 digits"),
  contact_email: z.string().email("Invalid email address"),
  whatsapp_number: z
    .string()
    .length(10, "WhatsApp number must be 10 digits")
    .optional()
    .nullable(),
  address_line: z.string().min(1, "Address is required"),
  city: z.string().min(1, "City is required"),
  state: z.string().min(1, "State is required"),
  pincode: z.string().length(6, "Pincode must be 6 digits"),
  locality: z.string().min(1, "Locality is required"),
});

export const businessDetailsSchema = z.object({
  business_hours: z.any().optional().nullable(),
  delivery_info: z.string().optional().nullable(),
});

export const socialLinksSchema = z.object({
  instagram_url: z.string().url().optional().nullable(),
  facebook_url: z.string().url().optional().nullable(),
  whatsapp_number: z
    .string()
    .length(10, "WhatsApp number must be 10 digits")
    .optional()
    .nullable(),
});

export const appearanceSchema = z.object({
  theme_color: z.string().regex(/^#[0-9a-fA-F]{6}$/, "Invalid theme color"),
});

export const statusSettingsSchema = z.object({
  status: z.enum(["online", "offline"]),
  business_slug: z.string().min(3, "Slug must be at least 3 characters").optional(),
});

// Export types for each section
export type BasicInfoData = z.infer<typeof basicInfoSchema>;
export type ContactLocationData = z.infer<typeof contactLocationSchema>;
export type BusinessDetailsData = z.infer<typeof businessDetailsSchema>;
export type SocialLinksData = z.infer<typeof socialLinksSchema>;
export type AppearanceData = z.infer<typeof appearanceSchema>;
export type StatusSettingsData = z.infer<typeof statusSettingsSchema>;

export function validateBusinessCardData(data: unknown) {
  return businessCardSchema.safeParse(data);
}
