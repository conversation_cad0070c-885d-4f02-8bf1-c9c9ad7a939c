import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Modal,
  ScrollView,
  ActivityIndicator,
} from "react-native";
import {
  X,
  User,
  MapPin,
  Clock,
  Link,
  Palette,
  Settings,
  Crown,
  ChevronRight,
  ArrowLeft
} from "lucide-react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { createManageCardModalStyles } from "@/styles/modals/business/manage-card-modal";
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  BusinessCardData,
  businessCardSchema,
} from "@/backend/supabase/services/business/schemas";
import {
  getBusinessCardData,
  updateBusinessCard,
} from "@/backend/supabase/services/business/businessCardService";
import { useToast } from "@/src/components/ui/Toast";
import { useAuth } from "@/src/contexts/AuthContext";
import BasicInfoSection from "./sections/BasicInfoSection";
import ContactLocationSection from "./sections/ContactLocationSection";
import StatusSettingsSection from "./sections/StatusSettingsSection";
import BusinessDetailsSection from "./sections/BusinessDetailsSection";
import SocialLinksSection from "./sections/SocialLinksSection";
import AppearanceSection from "./sections/AppearanceSection";
import AdvancedFeaturesSection from "./sections/AdvancedFeaturesSection";

// Define the different sections available
type CardSection =
  | 'menu'
  | 'basic-info'
  | 'contact-location'
  | 'business-details'
  | 'social-links'
  | 'appearance'
  | 'status-settings'
  | 'advanced-features';

interface ManageCardModalProps {
  visible: boolean;
  onClose: () => void;
}

interface CardSectionItem {
  id: CardSection;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  isPremium?: boolean;
}

export default function ManageCardModal({
  visible,
  onClose,
}: ManageCardModalProps) {
  const theme = useTheme();
  const styles = createManageCardModalStyles(theme);
  const { user } = useAuth();
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [currentView, setCurrentView] = useState<CardSection>('menu');
  const [isFetching, setIsFetching] = useState(true);

  // Single form for all sections
  const formMethods = useForm<BusinessCardData>({
    resolver: zodResolver(businessCardSchema),
    defaultValues: {
      business_name: "",
      member_name: "",
      title: "",
      logo_url: "",
      phone: "",
      whatsapp_number: "",
      contact_email: "",
      address_line: "",
      city: "",
      state: "",
      pincode: "",
      locality: "",
      about_bio: "",
      business_slug: "",
      instagram_url: "",
      facebook_url: "",
      theme_color: "#000000",
      delivery_info: "",
      business_hours: {},
      business_category: "",
      established_year: undefined,
      status: "offline",
    },
  });

  const { reset, watch } = formMethods;

  // Define menu sections
  const menuSections: CardSectionItem[] = [
    {
      id: 'basic-info',
      title: 'Basic Information',
      description: 'Business name, logo, category, and description',
      icon: User,
    },
    {
      id: 'contact-location',
      title: 'Contact & Location',
      description: 'Phone, email, address, and location details',
      icon: MapPin,
    },
    {
      id: 'business-details',
      title: 'Business Details',
      description: 'Business hours, delivery info, and services',
      icon: Clock,
    },
    {
      id: 'social-links',
      title: 'Social Links',
      description: 'WhatsApp, Instagram, Facebook, and other links',
      icon: Link,
    },
    {
      id: 'appearance',
      title: 'Appearance',
      description: 'Theme colors and visual customization',
      icon: Palette,
    },
    {
      id: 'status-settings',
      title: 'Status & Settings',
      description: 'Online status, business slug, and visibility',
      icon: Settings,
    },
    {
      id: 'advanced-features',
      title: 'Advanced Features',
      description: 'Custom branding and premium features',
      icon: Crown,
      isPremium: true,
    },
  ];

  const fetchCardData = useCallback(async () => {
    setIsFetching(true);
    const { data, error } = await getBusinessCardData();
    if (error) {
      toast.error("Error", "Failed to fetch business card data.");
    } else if (data) {
      reset(data);
    }
    setIsFetching(false);
  }, [toast, reset]);

  // Fetch data when modal opens
  useEffect(() => {
    if (visible) {
      fetchCardData();
      setCurrentView('menu'); // Reset to menu when modal opens
    }
  }, [visible, fetchCardData]);

  // Navigation handlers
  const handleSectionSelect = (sectionId: CardSection) => {
    setCurrentView(sectionId);
  };

  const handleBackToMenu = () => {
    setCurrentView('menu');
  };

  // Save handler for individual sections
  const handleSectionSave = async (sectionData: Partial<BusinessCardData>) => {
    setIsLoading(true);
    const currentData = watch();
    const updatedData = { ...currentData, ...sectionData };

    const { success, error } = await updateBusinessCard(updatedData);
    if (success) {
      toast.success("Success", "Section updated successfully.");
      reset(updatedData); // Update form with saved data
    } else {
      toast.error("Error", error || "Failed to update section.");
    }
    setIsLoading(false);
  };

  // Render menu view
  const renderMenuView = () => (
    <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
      <View style={styles.menuContainer}>
        <Text style={styles.menuTitle}>Choose what to edit</Text>
        <Text style={styles.menuSubtitle}>
          Select a section to customize your business card
        </Text>

        {menuSections.map((section) => (
          <TouchableOpacity
            key={section.id}
            style={styles.menuItem}
            onPress={() => handleSectionSelect(section.id)}
          >
            <View style={styles.menuItemLeft}>
              <View style={styles.menuItemIcon}>
                <section.icon size={20} color={theme.colors.primary} />
              </View>
              <View style={styles.menuItemContent}>
                <Text style={styles.menuItemTitle}>{section.title}</Text>
                <Text style={styles.menuItemDescription}>{section.description}</Text>
              </View>
            </View>
            <View style={styles.menuItemRight}>
              {section.isPremium && (
                <Crown size={16} color="#D4AF37" style={{ marginRight: 8 }} />
              )}
              <ChevronRight size={20} color={theme.colors.textSecondary} />
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </ScrollView>
  );

  // Render section view
  const renderSectionView = () => {
    switch (currentView) {
      case 'basic-info':
        return <BasicInfoSection onBack={handleBackToMenu} />;
      case 'contact-location':
        return <ContactLocationSection onBack={handleBackToMenu} />;
      case 'status-settings':
        return <StatusSettingsSection onBack={handleBackToMenu} />;
      case 'business-details':
        return <BusinessDetailsSection onBack={handleBackToMenu} />;
      case 'social-links':
        return <SocialLinksSection onBack={handleBackToMenu} />;
      case 'appearance':
        return <AppearanceSection onBack={handleBackToMenu} />;
      case 'advanced-features':
        return <AdvancedFeaturesSection onBack={handleBackToMenu} />;
      default:
        return (
          <View style={styles.sectionContainer}>
            <Text style={styles.menuTitle}>Unknown Section</Text>
            <Text style={styles.menuSubtitle}>
              This section could not be found.
            </Text>
          </View>
        );
    }
  };

  return (
    <Modal
      visible={visible}
      onRequestClose={onClose}
      presentationStyle="fullScreen"
      animationType="slide"
    >
      <SafeAreaView style={styles.safeArea}>
        <KeyboardAvoidingView
          style={styles.keyboardAvoidingView}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
        >
          {/* Header */}
          <View style={styles.header}>
            {currentView !== 'menu' ? (
              <TouchableOpacity onPress={handleBackToMenu} style={styles.backButton}>
                <ArrowLeft size={24} color={theme.colors.foreground} />
              </TouchableOpacity>
            ) : (
              <View style={{ width: 40 }} />
            )}
            <Text style={styles.headerTitle}>
              {currentView === 'menu'
                ? 'Manage Business Card'
                : menuSections.find(s => s.id === currentView)?.title || 'Section'
              }
            </Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color={theme.colors.foreground} />
            </TouchableOpacity>
          </View>

          {/* Content */}
          {isFetching ? (
            <View style={styles.sectionContainer}>
              <ActivityIndicator size="large" color={theme.colors.primary} />
              <Text style={[styles.menuSubtitle, { marginTop: theme.spacing.sm }]}>
                Loading business card data...
              </Text>
            </View>
          ) : (
            <FormProvider {...formMethods}>
              {currentView === 'menu' ? renderMenuView() : renderSectionView()}
            </FormProvider>
          )}
        </KeyboardAvoidingView>
      </SafeAreaView>
    </Modal>
  );
}
