name: "Comprehensive Testing Plan for Dukancard React Native App"
description: |

## Purpose

Create a complete testing infrastructure and comprehensive test coverage for the dukancard-app React Native application to ensure production-ready quality, reliability, and maintainability across all components, services, and user flows.

## Core Principles

1. **Production-First Testing**: Every test must validate real-world scenarios and edge cases
2. **Comprehensive Coverage**: 90%+ code coverage across all modules and critical paths
3. **Multi-Layer Testing**: Unit, Integration, Component, and E2E testing strategies
4. **Performance & Security**: Include performance benchmarks and security validation
5. **CI/CD Integration**: Automated testing pipeline with quality gates

---

## Goal

Implement a comprehensive testing strategy covering all aspects of the dukancard-app React Native application, including:

- Complete unit test coverage for all components, hooks, services, and utilities
- Integration testing for API calls, database operations, and cross-component interactions
- Component testing with React Native Testing Library for UI validation
- End-to-end testing with Detox for critical user journeys
- Performance and security testing to ensure production readiness
- Automated CI/CD pipeline integration with quality gates

## Why

- **Business Value**: Prevents production bugs, reduces maintenance costs, ensures user satisfaction
- **Code Quality**: Maintains high code standards and enables confident refactoring
- **Developer Experience**: Provides fast feedback loops and documentation through tests
- **Production Reliability**: Validates all critical user flows and edge cases work correctly
- **Scalability**: Enables safe feature additions and modifications

## What

A complete testing ecosystem that validates:

- All React Native components render correctly and handle user interactions
- All custom hooks manage state and side effects properly
- All backend services perform database operations correctly
- All utility functions handle edge cases and errors gracefully
- All user authentication and authorization flows work securely
- All critical user journeys complete successfully end-to-end
- Application performance meets production standards
- Security vulnerabilities are identified and prevented

### Success Criteria

- [ ] 90%+ unit test coverage across all modules (components, hooks, services, utils)
- [ ] All 50+ React Native components have comprehensive test suites
- [ ] All 15+ custom hooks are thoroughly tested with edge cases
- [ ] All backend services have integration tests with mocked and real database operations
- [ ] All critical user flows covered by E2E tests (auth, onboarding, core features)
- [ ] Performance benchmarks established for key operations (rendering, navigation, API calls)
- [ ] Security tests validate authentication, authorization, and data validation
- [ ] CI/CD pipeline runs all tests automatically with quality gates
- [ ] Test documentation and best practices guide created
- [ ] Mock data factories and test utilities implemented for consistent testing

## All Needed Context

### Documentation & References

```yaml
# MUST READ - Include these in your context window
- file: dukancard-app/jest.config.js
  why: Current Jest configuration and coverage settings

- file: dukancard-app/jest.setup.js
  why: Existing mock setup and test environment configuration

- file: dukancard-app/e2e/jest.config.js
  why: Detox E2E testing configuration

- file: dukancard-app/src/components/index.ts
  why: Component structure and organization patterns

- file: dukancard-app/src/contexts/AuthContext.tsx
  why: Context testing patterns and authentication logic

- file: dukancard-app/backend/supabase/services/index.ts
  why: Backend service structure and testing requirements

- url: https://callstack.github.io/react-native-testing-library/
  why: React Native Testing Library documentation and best practices

- url: https://github.com/wix/Detox/blob/master/docs/README.md
  why: Detox E2E testing framework documentation

- url: https://jestjs.io/docs/testing-frameworks
  why: Jest testing framework advanced features and mocking

- file: dukancard-app/src/services/locationStorageService.test.ts
  why: Existing test pattern to follow for service testing
```

### Current Codebase Structure

```bash
dukancard-app/
├── src/
│   ├── components/          # 20+ component categories (ui, business, customer, etc.)
│   ├── contexts/           # 6 context providers (Auth, Location, Notification, etc.)
│   ├── hooks/              # 15+ custom hooks (useAuth*, useLoading*, etc.)
│   ├── services/           # Service layer (discovery, location, etc.)
│   ├── utils/              # 15+ utility modules (API, validation, etc.)
│   └── types/              # TypeScript type definitions
├── backend/
│   ├── supabase/services/  # 10+ service modules (auth, business, customer, etc.)
│   └── types/              # Backend type definitions
├── lib/
│   ├── actions/            # Server actions (business, customer, posts, etc.)
│   ├── auth/               # Authentication utilities
│   └── config/             # Configuration modules
├── app/                    # Expo Router screens and layouts
├── e2e/                    # Detox E2E tests
├── jest.config.js          # Jest configuration
├── jest.setup.js           # Test setup and mocks
└── package.json            # Dependencies and scripts
```

### Desired Testing Structure

```bash
dukancard-app/
├── __tests__/              # Test utilities and shared mocks
│   ├── __mocks__/          # Global mocks and factories
│   ├── utils/              # Test utilities and helpers
│   └── fixtures/           # Test data and fixtures
├── src/
│   ├── components/
│   │   ├── ui/
│   │   │   ├── Button.test.tsx
│   │   │   ├── Input.test.tsx
│   │   │   └── ...
│   │   ├── business/
│   │   │   ├── BusinessCard.test.tsx
│   │   │   └── ...
│   │   └── [each component category]/
│   ├── contexts/
│   │   ├── AuthContext.test.tsx
│   │   ├── LocationContext.test.tsx
│   │   └── ...
│   ├── hooks/
│   │   ├── useAuth.test.ts
│   │   ├── useLoadingState.test.ts
│   │   └── ...
│   ├── services/
│   │   ├── locationStorageService.test.ts  # Already exists
│   │   └── ...
│   └── utils/
│       ├── apiClient.test.ts
│       ├── validation.test.ts
│       └── ...
├── backend/
│   └── supabase/services/
│       ├── auth/
│       │   └── authService.test.ts
│       ├── business/
│       │   └── businessService.test.ts
│       └── ...
├── lib/
│   ├── actions/
│   │   └── [action].test.ts
│   └── auth/
│       └── [auth-module].test.ts
├── app/
│   ├── (auth)/
│   │   └── __tests__/
│   ├── (dashboard)/
│   │   └── __tests__/
│   └── ...
├── e2e/
│   ├── auth.e2e.js
│   ├── onboarding.e2e.js
│   ├── business-dashboard.e2e.js
│   ├── customer-flows.e2e.js
│   └── critical-paths.e2e.js
└── performance/
    ├── rendering.perf.test.js
    ├── navigation.perf.test.js
    └── api.perf.test.js
```

### Known Gotchas & Testing Challenges

```typescript
// CRITICAL: React Native Testing Library requires specific setup
// CRITICAL: Supabase mocking needs careful configuration to avoid real API calls
// CRITICAL: Expo Router navigation testing requires special handling
// CRITICAL: Image upload and camera features need platform-specific mocking
// CRITICAL: Location services require permission mocking
// CRITICAL: Real-time subscriptions need WebSocket mocking
// CRITICAL: AsyncStorage operations need proper cleanup between tests
// CRITICAL: Context providers must be wrapped correctly in test renders
// CRITICAL: Detox E2E tests require device/simulator setup and app builds
// CRITICAL: Performance tests need consistent baseline measurements
```

## Implementation Blueprint

### Testing Infrastructure Setup

Create the foundational testing utilities and configuration needed for comprehensive testing.

```typescript
// __tests__/utils/testUtils.tsx - Custom render function with providers
import { render } from "@testing-library/react-native";
import { AuthProvider } from "@/src/contexts/AuthContext";
import { ThemeProvider } from "@/src/contexts/ThemeContext";

export const renderWithProviders = (ui: React.ReactElement, options = {}) => {
  const AllProviders = ({ children }: { children: React.ReactNode }) => (
    <ThemeProvider>
      <AuthProvider>{children}</AuthProvider>
    </ThemeProvider>
  );

  return render(ui, { wrapper: AllProviders, ...options });
};

// __tests__/__mocks__/mockData.ts - Test data factories
export const createMockUser = (overrides = {}) => ({
  id: "test-user-id",
  email: "<EMAIL>",
  ...overrides,
});

export const createMockBusinessProfile = (overrides = {}) => ({
  id: "test-business-id",
  name: "Test Business",
  slug: "test-business",
  ...overrides,
});
```

### List of Tasks to be Completed

```yaml
Phase 1: Testing Infrastructure & Utilities
Task 1: Set up test utilities and mock factories
  CREATE __tests__/utils/testUtils.tsx:
    - Custom render functions with providers
    - Mock data factories for consistent test data
    - Test utilities for common operations

Task 2: Enhanced Jest configuration
  MODIFY jest.config.js:
    - Add performance testing configuration
    - Configure coverage thresholds per module
    - Add custom test environments for different scenarios

Task 3: Comprehensive mock setup
  MODIFY jest.setup.js:
    - Add missing service mocks
    - Configure Supabase client mocking
    - Set up AsyncStorage and SecureStore mocks

Phase 2: Component Testing (50+ Components)
Task 4: UI Component Tests
  CREATE src/components/ui/*.test.tsx:
    - Button, Input, OTPInput, Toast components
    - Test rendering, props, user interactions
    - Accessibility testing

Task 5: Feature Component Tests
  CREATE src/components/[feature]/*.test.tsx:
    - Business, customer, discovery, feed components
    - Test complex interactions and state changes
    - Integration with contexts and services

Task 6: Layout Component Tests
  CREATE src/components/layout/*.test.tsx:
    - Screen containers, auth containers
    - Navigation integration testing
    - Responsive behavior testing

Phase 3: Hook Testing (15+ Hooks)
Task 7: State Management Hook Tests
  CREATE src/hooks/*.test.ts:
    - useAuth*, useLoading*, useLocation* hooks
    - Test state changes, side effects, cleanup
    - Error handling and edge cases

Task 8: Business Logic Hook Tests
  CREATE src/hooks/business/*.test.ts:
    - useBusinessCardData, useBusinessInteractions
    - Test complex business logic and API integration
    - Performance and optimization testing

Phase 4: Service & Backend Testing
Task 9: Backend Service Tests
  CREATE backend/supabase/services/**/*.test.ts:
    - Auth, business, customer, posts services
    - Database operation testing with mocks
    - Error handling and validation testing

Task 10: Utility Function Tests
  CREATE src/utils/*.test.ts:
    - API client, validation, image compression
    - Pure function testing with edge cases
    - Performance testing for complex operations

Phase 5: Integration Testing
Task 11: Context Integration Tests
  CREATE src/contexts/*.test.tsx:
    - Provider behavior with real and mocked data
    - State synchronization across components
    - Error boundary and recovery testing

Task 12: Screen Integration Tests
  CREATE app/**/__tests__/*.test.tsx:
    - Full screen rendering with all dependencies
    - Navigation flow testing
    - Form submission and validation flows

Phase 6: E2E Testing
Task 13: Authentication Flow E2E Tests
  CREATE e2e/auth.e2e.js:
    - Sign up, sign in, role selection flows
    - OAuth integration testing
    - Profile completion workflows

Task 14: Core Feature E2E Tests
  CREATE e2e/core-features.e2e.js:
    - Business dashboard operations
    - Customer discovery and interactions
    - Post creation and management flows

Phase 7: Performance & Security Testing
Task 15: Performance Benchmarks
  CREATE performance/*.perf.test.js:
    - Component rendering performance
    - Navigation timing benchmarks
    - API response time validation

Task 16: Security Testing
  CREATE security/*.security.test.js:
    - Authentication security validation
    - Data sanitization testing
    - Permission and authorization testing

Phase 8: CI/CD Integration
Task 17: GitHub Actions Workflow
  CREATE .github/workflows/test.yml:
    - Automated test execution on PR/push
    - Coverage reporting and quality gates
    - E2E test execution with device matrix

Task 18: Test Documentation
  CREATE docs/TESTING.md:
    - Testing best practices guide
    - How to write and run tests
    - Troubleshooting common issues
```

### Detailed Testing Implementation Examples

```typescript
// Example: Component Test Pattern
// src/components/ui/Button.test.tsx
import { fireEvent } from "@testing-library/react-native";
import { renderWithProviders } from "@/__tests__/utils/testUtils";
import { Button } from "./Button";

describe("Button Component", () => {
  it("renders correctly with default props", () => {
    const { getByText } = renderWithProviders(
      <Button title="Test Button" onPress={() => {}} />
    );
    expect(getByText("Test Button")).toBeTruthy();
  });

  it("handles press events correctly", () => {
    const mockOnPress = jest.fn();
    const { getByText } = renderWithProviders(
      <Button title="Test Button" onPress={mockOnPress} />
    );

    fireEvent.press(getByText("Test Button"));
    expect(mockOnPress).toHaveBeenCalledTimes(1);
  });

  it("applies correct styling based on variant", () => {
    const { getByTestId } = renderWithProviders(
      <Button title="Primary" variant="primary" testID="primary-button" />
    );

    const button = getByTestId("primary-button");
    expect(button.props.style).toMatchObject({
      backgroundColor: expect.any(String),
    });
  });

  it("handles disabled state correctly", () => {
    const mockOnPress = jest.fn();
    const { getByText } = renderWithProviders(
      <Button title="Disabled" onPress={mockOnPress} disabled />
    );

    fireEvent.press(getByText("Disabled"));
    expect(mockOnPress).not.toHaveBeenCalled();
  });
});

// Example: Hook Test Pattern
// src/hooks/useLoadingState.test.ts
import { renderHook, act } from "@testing-library/react-native";
import { useLoadingState } from "./useLoadingState";

describe("useLoadingState Hook", () => {
  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it("initializes with correct default state", () => {
    const { result } = renderHook(() => useLoadingState());

    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it("handles loading state transitions correctly", async () => {
    const { result } = renderHook(() => useLoadingState({ minDuration: 100 }));

    act(() => {
      result.current.startLoading();
    });

    expect(result.current.isLoading).toBe(true);

    act(() => {
      result.current.stopLoading();
      jest.advanceTimersByTime(100);
    });

    expect(result.current.isLoading).toBe(false);
  });

  it("respects minimum loading duration", () => {
    const { result } = renderHook(() => useLoadingState({ minDuration: 500 }));

    act(() => {
      result.current.startLoading();
    });

    act(() => {
      result.current.stopLoading();
      jest.advanceTimersByTime(200); // Less than minDuration
    });

    expect(result.current.isLoading).toBe(true);

    act(() => {
      jest.advanceTimersByTime(300); // Complete minDuration
    });

    expect(result.current.isLoading).toBe(false);
  });
});

// Example: Service Test Pattern
// backend/supabase/services/auth/authService.test.ts
import { createClient } from "@supabase/supabase-js";
import { authService } from "./authService";

jest.mock("@supabase/supabase-js");
const mockSupabase = createClient as jest.MockedFunction<typeof createClient>;

describe("Auth Service", () => {
  let mockClient: any;

  beforeEach(() => {
    mockClient = {
      auth: {
        signUp: jest.fn(),
        signInWithPassword: jest.fn(),
        signOut: jest.fn(),
        getSession: jest.fn(),
      },
      from: jest.fn(() => ({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            single: jest.fn(),
          })),
        })),
      })),
    };
    mockSupabase.mockReturnValue(mockClient);
  });

  describe("signUp", () => {
    it("successfully creates new user account", async () => {
      const mockUser = { id: "user-123", email: "<EMAIL>" };
      mockClient.auth.signUp.mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });

      const result = await authService.signUp(
        "<EMAIL>",
        "password123"
      );

      expect(result.success).toBe(true);
      expect(result.user).toEqual(mockUser);
      expect(mockClient.auth.signUp).toHaveBeenCalledWith({
        email: "<EMAIL>",
        password: "password123",
      });
    });

    it("handles signup errors correctly", async () => {
      const mockError = { message: "Email already registered" };
      mockClient.auth.signUp.mockResolvedValue({
        data: { user: null },
        error: mockError,
      });

      const result = await authService.signUp(
        "<EMAIL>",
        "password123"
      );

      expect(result.success).toBe(false);
      expect(result.error).toEqual(mockError);
    });
  });

  describe("getUserProfile", () => {
    it("fetches user profile successfully", async () => {
      const mockProfile = {
        id: "profile-123",
        user_id: "user-123",
        name: "Test User",
      };
      mockClient.from().select().eq().single.mockResolvedValue({
        data: mockProfile,
        error: null,
      });

      const result = await authService.getUserProfile("user-123");

      expect(result.data).toEqual(mockProfile);
      expect(mockClient.from).toHaveBeenCalledWith("profiles");
    });
  });
});

// Example: E2E Test Pattern
// e2e/auth.e2e.js
describe("Authentication Flow", () => {
  beforeAll(async () => {
    await device.launchApp();
  });

  beforeEach(async () => {
    await device.reloadReactNative();
  });

  describe("Sign Up Flow", () => {
    it("should complete full signup process", async () => {
      // Navigate to signup screen
      await element(by.id("signup-button")).tap();

      // Fill signup form
      await element(by.id("email-input")).typeText("<EMAIL>");
      await element(by.id("password-input")).typeText("password123");
      await element(by.id("confirm-password-input")).typeText("password123");

      // Submit form
      await element(by.id("submit-signup")).tap();

      // Verify OTP screen appears
      await expect(element(by.id("otp-screen"))).toBeVisible();

      // Enter OTP (mock OTP for testing)
      await element(by.id("otp-input")).typeText("123456");
      await element(by.id("verify-otp")).tap();

      // Verify role selection screen
      await expect(element(by.id("role-selection-screen"))).toBeVisible();

      // Select customer role
      await element(by.id("customer-role-button")).tap();

      // Verify navigation to customer onboarding
      await expect(element(by.id("customer-onboarding-screen"))).toBeVisible();
    });

    it("should handle signup validation errors", async () => {
      await element(by.id("signup-button")).tap();

      // Try to submit without filling required fields
      await element(by.id("submit-signup")).tap();

      // Verify error messages appear
      await expect(element(by.text("Email is required"))).toBeVisible();
      await expect(element(by.text("Password is required"))).toBeVisible();
    });
  });

  describe("Sign In Flow", () => {
    it("should sign in existing user successfully", async () => {
      // Navigate to signin screen
      await element(by.id("signin-button")).tap();

      // Fill signin form
      await element(by.id("email-input")).typeText("<EMAIL>");
      await element(by.id("password-input")).typeText("password123");

      // Submit form
      await element(by.id("submit-signin")).tap();

      // Verify navigation to appropriate dashboard
      await expect(element(by.id("dashboard-screen"))).toBeVisible();
    });

    it("should handle invalid credentials", async () => {
      await element(by.id("signin-button")).tap();

      await element(by.id("email-input")).typeText("<EMAIL>");
      await element(by.id("password-input")).typeText("wrongpassword");

      await element(by.id("submit-signin")).tap();

      // Verify error message
      await expect(element(by.text("Invalid credentials"))).toBeVisible();
    });
  });
});
```

## Integration Points

```yaml
TESTING_ENVIRONMENT:
  - Jest configuration: Enhanced with performance and security testing
  - React Native Testing Library: Component and integration testing
  - Detox: E2E testing framework for critical user flows
  - MSW (Mock Service Worker): API mocking for integration tests
  - Custom test utilities: Provider wrappers and mock factories

COVERAGE_REQUIREMENTS:
  - Unit Tests: 90%+ coverage for utils, hooks, services
  - Component Tests: 85%+ coverage for all UI components
  - Integration Tests: 80%+ coverage for screens and flows
  - E2E Tests: 100% coverage for critical user journeys

CI_CD_INTEGRATION:
  - GitHub Actions: Automated test execution on PR/push
  - Quality Gates: Tests must pass before merge
  - Coverage Reports: Automated coverage reporting and tracking
  - Performance Monitoring: Benchmark tracking and alerts

MOCK_STRATEGY:
  - Supabase Client: Comprehensive mocking for all database operations
  - External APIs: Mock all third-party service calls
  - Device Features: Mock camera, location, storage, notifications
  - Navigation: Mock Expo Router navigation for isolated testing
```

## Validation Loop

### Level 1: Syntax & Style

```bash
# Run these FIRST - fix any errors before proceeding
npm run lint                     # ESLint checking
npm run type-check              # TypeScript compilation check
npm run test:setup              # Verify test environment setup

# Expected: No errors. If errors, READ the error and fix.
```

### Level 2: Unit Tests

```bash
# Run unit tests for each module category
npm run test:components         # All component tests
npm run test:hooks             # All hook tests
npm run test:services          # All service tests
npm run test:utils             # All utility tests

# Run with coverage reporting
npm run test:coverage          # Generate coverage report
npm run test:watch             # Watch mode for development

# Expected: 90%+ coverage, all tests passing
# If failing: Read error, understand root cause, fix code, re-run
```

### Level 3: Integration Tests

```bash
# Run integration tests for complex flows
npm run test:integration       # Screen and context integration tests
npm run test:api              # API integration tests with mocked Supabase
npm run test:navigation       # Navigation flow tests

# Expected: All integration scenarios pass
# If failing: Check mock setup and component interactions
```

### Level 4: E2E Tests

```bash
# Build app for testing
npm run build:e2e:ios         # Build iOS app for testing
npm run build:e2e:android     # Build Android app for testing

# Run E2E tests
npm run test:e2e:ios          # Run iOS E2E tests
npm run test:e2e:android      # Run Android E2E tests
npm run test:e2e:all          # Run all E2E tests

# Expected: All critical user flows complete successfully
# If failing: Check device setup, app build, and test selectors
```

### Level 5: Performance & Security Tests

```bash
# Run performance benchmarks
npm run test:performance      # Component rendering and navigation performance
npm run test:memory          # Memory usage and leak detection

# Run security tests
npm run test:security        # Authentication and authorization validation
npm run test:data-validation # Input validation and sanitization

# Expected: Performance within benchmarks, no security vulnerabilities
```

## Final Validation Checklist

- [ ] All unit tests pass: `npm run test:unit`
- [ ] All integration tests pass: `npm run test:integration`
- [ ] All E2E tests pass: `npm run test:e2e:all`
- [ ] Code coverage meets requirements: `npm run test:coverage`
- [ ] No linting errors: `npm run lint`
- [ ] No type errors: `npm run type-check`
- [ ] Performance benchmarks met: `npm run test:performance`
- [ ] Security tests pass: `npm run test:security`
- [ ] CI/CD pipeline configured and working
- [ ] Test documentation complete and accurate
- [ ] All critical user flows validated end-to-end
- [ ] Mock data and test utilities properly implemented

## Comprehensive Testing Scope

### Components Requiring Testing (50+ Components)

```yaml
UI_COMPONENTS:
  - Button, Input, OTPInput, Toast, DukancardLogo, GoogleIcon
  - Loading states, error states, success states
  - Accessibility compliance and screen reader support

BUSINESS_COMPONENTS:
  - BusinessCard, BusinessProfile, BusinessDashboard
  - BusinessSettings, BusinessMetrics, BusinessPosts
  - Subscription management, payment flows

CUSTOMER_COMPONENTS:
  - CustomerProfile, CustomerDashboard, CustomerSettings
  - CustomerInteractions, CustomerFeed, CustomerDiscovery

DISCOVERY_COMPONENTS:
  - SearchBar, FilterOptions, LocationPicker
  - BusinessList, CategorySelector, DistanceCalculator

FEED_COMPONENTS:
  - PostCard, PostList, PostCreation, PostInteractions
  - FeedFilters, FeedSorting, InfiniteScroll

FORM_COMPONENTS:
  - FormFields, FormValidation, FormSubmission
  - ImageUpload, FileUpload, FormWizards

LAYOUT_COMPONENTS:
  - ScreenContainers, AuthContainers, DashboardContainers
  - Headers, Footers, Navigation, TabBars

MODAL_COMPONENTS:
  - ConfirmationModals, InfoModals, FormModals
  - BottomSheets, ActionSheets, Overlays

NOTIFICATION_COMPONENTS:
  - NotificationList, NotificationItem, NotificationSettings
  - PushNotifications, InAppNotifications

PROFILE_COMPONENTS:
  - ProfileView, ProfileEdit, ProfileSettings
  - AvatarUpload, ProfileCompletion, ProfileValidation
```

### Services Requiring Testing (15+ Services)

```yaml
AUTH_SERVICES:
  - User authentication, OAuth flows, session management
  - Profile creation, role selection, onboarding flows
  - Password reset, email verification, account management

BUSINESS_SERVICES:
  - Business profile CRUD operations
  - Business verification, settings management
  - Business metrics, analytics, reporting

CUSTOMER_SERVICES:
  - Customer profile management
  - Customer interactions, preferences
  - Customer discovery, search, filtering

POST_SERVICES:
  - Post creation, editing, deletion
  - Post interactions (likes, comments, shares)
  - Feed algorithms, content moderation

PRODUCT_SERVICES:
  - Product catalog management
  - Product search, filtering, categorization
  - Inventory management, pricing

STORAGE_SERVICES:
  - Image upload, compression, optimization
  - File management, CDN integration
  - Media processing, thumbnail generation

LOCATION_SERVICES:
  - GPS location, address resolution
  - Distance calculation, proximity search
  - Location permissions, privacy settings

NOTIFICATION_SERVICES:
  - Push notification delivery
  - In-app notification management
  - Notification preferences, settings

REALTIME_SERVICES:
  - WebSocket connections, real-time updates
  - Live data synchronization
  - Connection management, reconnection logic
```

### Hooks Requiring Testing (15+ Hooks)

```yaml
STATE_MANAGEMENT_HOOKS:
  - useAuth, useAuthRefresh, useAuthErrorHandler
  - useLoadingState, useAlert, useDebounce
  - useTheme, useColorScheme, useThemeColor

BUSINESS_LOGIC_HOOKS:
  - useBusinessCardData, useBusinessInteractions
  - usePostOwnership, useSinglePost
  - useAvatarUpload, useSlugValidation

UTILITY_HOOKS:
  - useLocationPermission, usePincodeDetails
  - useDynamicSafeArea, use-mobile
  - Custom hooks for API calls, data fetching

PERFORMANCE_HOOKS:
  - Memoization hooks, optimization hooks
  - Lazy loading hooks, caching hooks
  - Memory management hooks
```

### Critical User Flows for E2E Testing

```yaml
AUTHENTICATION_FLOWS:
  - Complete signup process (email → OTP → role selection → onboarding)
  - Google OAuth signup and signin
  - Password reset and email verification
  - Account deletion and data cleanup

ONBOARDING_FLOWS:
  - Business onboarding (profile → verification → dashboard)
  - Customer onboarding (profile → preferences → discovery)
  - Role switching and profile completion

CORE_BUSINESS_FLOWS:
  - Business dashboard navigation and management
  - Post creation, editing, and publishing
  - Product catalog management
  - Customer interaction and engagement

DISCOVERY_FLOWS:
  - Location-based business discovery
  - Search and filtering functionality
  - Business profile viewing and interaction
  - QR code scanning and sharing

CUSTOMER_FLOWS:
  - Feed browsing and interaction
  - Business following and unfollowing
  - Profile management and settings
  - Notification management
```

---

## Anti-Patterns to Avoid

- ❌ Don't test implementation details - focus on behavior and user interactions
- ❌ Don't create brittle tests that break with minor UI changes
- ❌ Don't skip edge cases and error scenarios
- ❌ Don't use real API calls in unit tests - always mock external dependencies
- ❌ Don't ignore performance implications of test setup and teardown
- ❌ Don't write tests without clear assertions and expectations
- ❌ Don't duplicate test logic - use shared utilities and helpers
- ❌ Don't ignore accessibility testing and screen reader compatibility
- ❌ Don't skip testing on different device sizes and orientations
- ❌ Don't forget to test offline scenarios and network failures

## Confidence Score: 9/10

High confidence due to:

- Comprehensive analysis of existing codebase structure
- Clear understanding of React Native testing best practices
- Detailed breakdown of all components, services, and flows requiring testing
- Established testing infrastructure with Jest and Detox already configured
- Specific examples and patterns for each type of testing
- Clear validation gates and success criteria

Minor uncertainty around:

- Specific Supabase mocking strategies for complex real-time features
- Performance testing benchmarks for React Native components
- Device-specific testing requirements for camera and location features
