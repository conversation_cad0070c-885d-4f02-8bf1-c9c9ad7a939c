import { supabase } from "@/src/config/supabase";
import { ProductVariants, ProductVariantsInsert, ProductVariantsUpdate } from "@/src/types/database";

export interface VariantFormData {
  variant_name: string;
  variant_values: Record<string, string>;
  base_price?: number | null;
  discounted_price?: number | null;
  is_available: boolean;
  images?: string[]; // Base64 encoded images
  featured_image_index?: number;
}

// Add a new product variant
export async function addProductVariant(
  productId: string,
  variantData: VariantFormData
): Promise<{ success: boolean; error?: string; data?: ProductVariants }> {
  try {
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user) {
      return { success: false, error: "User not authenticated." };
    }

    // Verify the product belongs to the user
    const { data: product, error: productError } = await supabase
      .from("products_services")
      .select("id, business_id")
      .eq("id", productId)
      .eq("business_id", user.id)
      .single();

    if (productError || !product) {
      return { success: false, error: "Product not found or access denied." };
    }

    // Prepare variant data for insertion
    const variantToInsert: ProductVariantsInsert = {
      product_id: productId,
      variant_name: variantData.variant_name,
      variant_values: variantData.variant_values,
      base_price: variantData.base_price || null,
      discounted_price: variantData.discounted_price || null,
      is_available: variantData.is_available,
      featured_image_index: variantData.featured_image_index || 0,
    };

    // Insert the variant
    const { data: insertedVariant, error: insertError } = await supabase
      .from("product_variants")
      .insert(variantToInsert)
      .select()
      .single();

    if (insertError) {
      return { success: false, error: insertError.message };
    }

    // Handle image upload if provided
    if (variantData.images && variantData.images.length > 0) {
      const imageUrls: string[] = [];
      
      for (let i = 0; i < variantData.images.length; i++) {
        const image = variantData.images[i];
        if (!image.startsWith("data:image")) continue;

        try {
          const fileExt = image.split(";")[0].split("/")[1];
          const filePath = `${user.id}/${productId}/variants/${insertedVariant.id}/variant_image_${i}.${fileExt}`;
          
          // Convert base64 to buffer
          const base64Data = image.split(",")[1];
          const buffer = Buffer.from(base64Data, "base64");

          const { error: uploadError } = await supabase.storage
            .from("business")
            .upload(filePath, buffer, {
              contentType: `image/${fileExt}`,
              upsert: true,
            });

          if (uploadError) {
            console.error("Variant image upload error:", uploadError);
            continue;
          }

          const { data: urlData } = supabase.storage
            .from("business")
            .getPublicUrl(filePath);
          
          imageUrls.push(urlData.publicUrl);
        } catch (error) {
          console.error("Error processing variant image:", error);
          continue;
        }
      }

      // Update variant with image URLs
      if (imageUrls.length > 0) {
        const featuredImageIndex = Math.min(
          variantData.featured_image_index || 0,
          imageUrls.length - 1
        );

        const { data: updatedVariant, error: updateError } = await supabase
          .from("product_variants")
          .update({
            images: imageUrls,
            featured_image_index: featuredImageIndex,
          })
          .eq("id", insertedVariant.id)
          .select()
          .single();

        if (updateError) {
          return { success: false, error: updateError.message };
        }

        return { success: true, data: updatedVariant };
      }
    }

    return { success: true, data: insertedVariant };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to add variant",
    };
  }
}

// Update an existing product variant
export async function updateProductVariant(
  variantId: string,
  variantData: Partial<VariantFormData>
): Promise<{ success: boolean; error?: string; data?: ProductVariants }> {
  try {
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user) {
      return { success: false, error: "User not authenticated." };
    }

    // Verify the variant belongs to the user's product
    const { data: variant, error: variantError } = await supabase
      .from("product_variants")
      .select(`
        id,
        product_id,
        products_services!inner(business_id)
      `)
      .eq("id", variantId)
      .single();

    if (variantError || !variant || (variant.products_services as any).business_id !== user.id) {
      return { success: false, error: "Variant not found or access denied." };
    }

    // Prepare update data
    const updateData: Partial<ProductVariantsUpdate> = {
      updated_at: new Date().toISOString(),
    };

    if (variantData.variant_name !== undefined) updateData.variant_name = variantData.variant_name;
    if (variantData.variant_values !== undefined) updateData.variant_values = variantData.variant_values;
    if (variantData.base_price !== undefined) updateData.base_price = variantData.base_price;
    if (variantData.discounted_price !== undefined) updateData.discounted_price = variantData.discounted_price;
    if (variantData.is_available !== undefined) updateData.is_available = variantData.is_available;
    if (variantData.featured_image_index !== undefined) updateData.featured_image_index = variantData.featured_image_index;

    // Handle image updates if provided
    if (variantData.images && variantData.images.length > 0) {
      const imageUrls: string[] = [];
      
      for (let i = 0; i < variantData.images.length; i++) {
        const image = variantData.images[i];
        
        if (image.startsWith("data:image")) {
          // New base64 image - upload it
          try {
            const fileExt = image.split(";")[0].split("/")[1];
            const filePath = `${user.id}/${variant.product_id}/variants/${variantId}/variant_image_${Date.now()}_${i}.${fileExt}`;
            
            const base64Data = image.split(",")[1];
            const buffer = Buffer.from(base64Data, "base64");

            const { error: uploadError } = await supabase.storage
              .from("business")
              .upload(filePath, buffer, {
                contentType: `image/${fileExt}`,
                upsert: true,
              });

            if (uploadError) {
              console.error("Variant image upload error:", uploadError);
              continue;
            }

            const { data: urlData } = supabase.storage
              .from("business")
              .getPublicUrl(filePath);
            
            imageUrls.push(urlData.publicUrl);
          } catch (error) {
            console.error("Error processing variant image:", error);
            continue;
          }
        } else {
          // Existing URL - keep it
          imageUrls.push(image);
        }
      }

      updateData.images = imageUrls;
    }

    // Update the variant
    const { data: updatedVariant, error: updateError } = await supabase
      .from("product_variants")
      .update(updateData)
      .eq("id", variantId)
      .select()
      .single();

    if (updateError) {
      return { success: false, error: updateError.message };
    }

    return { success: true, data: updatedVariant };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update variant",
    };
  }
}

// Delete a product variant
export async function deleteProductVariant(
  variantId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user) {
      return { success: false, error: "User not authenticated." };
    }

    // Verify the variant belongs to the user's product and get images for cleanup
    const { data: variant, error: variantError } = await supabase
      .from("product_variants")
      .select(`
        id,
        product_id,
        images,
        products_services!inner(business_id)
      `)
      .eq("id", variantId)
      .single();

    if (variantError || !variant || (variant.products_services as any).business_id !== user.id) {
      return { success: false, error: "Variant not found or access denied." };
    }

    // Delete associated images from storage
    if (variant.images && variant.images.length > 0) {
      const imagePaths = variant.images.map((url: string) => {
        // Extract path from URL
        const urlParts = url.split("/");
        const pathIndex = urlParts.findIndex(part => part === "business");
        if (pathIndex !== -1) {
          return urlParts.slice(pathIndex + 1).join("/");
        }
        return null;
      }).filter(Boolean);

      if (imagePaths.length > 0) {
        const { error: deleteError } = await supabase.storage
          .from("business")
          .remove(imagePaths);

        if (deleteError) {
          console.error("Error deleting variant images:", deleteError);
          // Continue with variant deletion even if image cleanup fails
        }
      }
    }

    // Delete the variant
    const { error: deleteError } = await supabase
      .from("product_variants")
      .delete()
      .eq("id", variantId);

    if (deleteError) {
      return { success: false, error: deleteError.message };
    }

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to delete variant",
    };
  }
}

// Get variants for a product
export async function getProductVariants(
  productId: string
): Promise<{ success: boolean; error?: string; data?: ProductVariants[] }> {
  try {
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser();
    if (authError || !user) {
      return { success: false, error: "User not authenticated." };
    }

    // Verify the product belongs to the user
    const { data: product, error: productError } = await supabase
      .from("products_services")
      .select("id")
      .eq("id", productId)
      .eq("business_id", user.id)
      .single();

    if (productError || !product) {
      return { success: false, error: "Product not found or access denied." };
    }

    // Get variants
    const { data: variants, error: variantsError } = await supabase
      .from("product_variants")
      .select("*")
      .eq("product_id", productId)
      .order("created_at", { ascending: true });

    if (variantsError) {
      return { success: false, error: variantsError.message };
    }

    return { success: true, data: variants || [] };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to get variants",
    };
  }
}
