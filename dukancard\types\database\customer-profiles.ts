export interface CustomerProfilesRow {
  id: string;
  name: string | null;
  email: string | null;
  created_at: string;
  updated_at: string;
  avatar_url: string | null;
  address: string | null;
  pincode: string | null;
  state: string | null;
  city: string | null;
  locality: string | null;
  city_slug: string | null;
  state_slug: string | null;
  locality_slug: string | null;
  phone: string | null;
  latitude: number | null;
  longitude: number | null;
}

export interface CustomerProfilesInsert {
  id?: string;
  name?: string | null;
  email?: string | null;
  created_at?: string;
  updated_at?: string;
  avatar_url?: string | null;
  address?: string | null;
  pincode?: string | null;
  state?: string | null;
  city?: string | null;
  locality?: string | null;
  city_slug?: string | null;
  state_slug?: string | null;
  locality_slug?: string | null;
  phone?: string | null;
  latitude?: number | null;
  longitude?: number | null;
}

export interface CustomerProfilesUpdate {
  id?: string;
  name?: string | null;
  email?: string | null;
  created_at?: string;
  updated_at?: string;
  avatar_url?: string | null;
  address?: string | null;
  pincode?: string | null;
  state?: string | null;
  city?: string | null;
  locality?: string | null;
  city_slug?: string | null;
  state_slug?: string | null;
  locality_slug?: string | null;
  phone?: string | null;
  latitude?: number | null;
  longitude?: number | null;
}

export type CustomerProfiles = CustomerProfilesRow;

// Public view type - only safe data exposed
export interface CustomerProfilesPublicRow {
  id: string;
  name: string | null;
  avatar_url: string | null;
  city: string | null;
  state: string | null;
  locality: string | null;
  city_slug: string | null;
  state_slug: string | null;
  locality_slug: string | null;
  created_at: string;
  updated_at: string;
}

export type CustomerProfilesPublic = CustomerProfilesPublicRow;
