import { StyleSheet } from "react-native";
import { useTheme } from "@/src/hooks/useTheme";

export const createBusinessDetailsSectionStyles = (
  theme: ReturnType<typeof useTheme>
) => {
  const { isDark } = theme;
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: isDark ? "#000" : "#FFF",
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      padding: theme.spacing.md,
      paddingBottom: theme.spacing.xl,
    },
    section: {
      marginBottom: theme.spacing.lg,
    },
    sectionTitle: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
      color: isDark ? "#FFF" : "#000",
      marginBottom: theme.spacing.sm,
    },
    
    // Business hours styles
    hoursGroup: {
      backgroundColor: isDark ? "rgba(255, 255, 255, 0.05)" : "rgba(0, 0, 0, 0.02)",
      borderRadius: theme.borderRadius.lg,
      borderWidth: 1,
      borderColor: theme.colors.border,
      marginBottom: theme.spacing.sm,
      overflow: "hidden",
    },
    hoursGroupHeader: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      padding: theme.spacing.md,
      backgroundColor: isDark ? "rgba(255, 255, 255, 0.03)" : "rgba(0, 0, 0, 0.02)",
    },
    hoursGroupInfo: {
      flexDirection: "row",
      alignItems: "center",
      flex: 1,
    },
    hoursGroupTitle: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
      color: isDark ? "#FFF" : "#000",
      marginLeft: theme.spacing.sm,
    },
    hoursGroupContent: {
      padding: theme.spacing.md,
      backgroundColor: isDark ? "#000" : "#FFF",
    },
    timeRow: {
      flexDirection: "row",
      alignItems: "center",
      flexWrap: "wrap",
      gap: theme.spacing.sm,
    },
    timeLabel: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
      minWidth: 50,
    },
    timeInputs: {
      flexDirection: "row",
      alignItems: "center",
      flex: 1,
      gap: theme.spacing.sm,
    },
    timeButton: {
      backgroundColor: isDark ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.05)",
      borderRadius: theme.borderRadius.md,
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: theme.spacing.xs,
      borderWidth: 1,
      borderColor: theme.colors.border,
      minWidth: 80,
      alignItems: "center",
    },
    timeButtonText: {
      fontSize: theme.typography.fontSize.sm,
      color: isDark ? "#FFF" : "#000",
      fontWeight: "500",
    },
    timeSeparator: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
    },
    
    // Info box
    infoBox: {
      backgroundColor: isDark ? "rgba(212, 175, 55, 0.1)" : "rgba(212, 175, 55, 0.1)",
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      marginTop: theme.spacing.md,
    },
    infoText: {
      fontSize: theme.typography.fontSize.sm,
      color: isDark ? "#D4AF37" : "#B8860B",
      lineHeight: 20,
    },
    
    // Footer styles
    footer: {
      padding: theme.spacing.md,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      backgroundColor: isDark ? "#000" : "#FFF",
    },
    saveButton: {
      borderRadius: theme.borderRadius.lg,
      paddingVertical: theme.spacing.md,
      alignItems: "center",
      justifyContent: "center",
      minHeight: 50,
    },
    saveButtonDisabled: {
      opacity: 0.6,
    },
    saveButtonText: {
      color: "white",
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
    },
  });
};
