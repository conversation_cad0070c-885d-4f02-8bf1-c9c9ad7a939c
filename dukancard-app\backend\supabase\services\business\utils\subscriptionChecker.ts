import { supabase } from "../../../../../src/config/supabase";

export async function checkForceOfflineStatus(userId: string): Promise<{
  shouldForceOffline: boolean;
  reason?: string;
}> {
  const { data, error } = await supabase
    .from("business_profiles")
    .select("has_active_subscription, trial_end_date")
    .eq("id", userId)
    .single();

  if (error) {
    console.error("Error fetching subscription status:", error);
    return {
      shouldForceOffline: true,
      reason: "Failed to fetch subscription status.",
    };
  }

  if (!data) {
    return { shouldForceOffline: true, reason: "Business profile not found." };
  }

  const hasActiveSubscription = data.has_active_subscription;
  const trialEndDate = data.trial_end_date
    ? new Date(data.trial_end_date)
    : null;
  const isTrialExpired = trialEndDate ? trialEndDate < new Date() : false;

  if (!hasActiveSubscription && isTrialExpired) {
    return {
      shouldForceOffline: true,
      reason: "Subscription expired and no active trial.",
    };
  }

  return { shouldForceOffline: false };
}
