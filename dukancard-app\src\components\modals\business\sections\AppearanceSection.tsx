import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from "react-native";
import { Controller, useFormContext } from "react-hook-form";
import { Pa<PERSON>, Eye } from "lucide-react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { useToast } from "@/src/components/ui/Toast";
import { updateAppearance } from "@/backend/supabase/services/business/businessCardService";
import { AppearanceData, appearanceSchema } from "@/backend/supabase/services/business/schemas";
import { createAppearanceSectionStyles } from "@/styles/modals/business/sections/appearance-section";

interface AppearanceSectionProps {
  onBack: () => void;
}

// Predefined theme colors
const THEME_COLORS = [
  { name: "Gold", value: "#D4AF37", description: "Classic business gold" },
  { name: "<PERSON>", value: "#3B82F6", description: "Professional blue" },
  { name: "<PERSON>", value: "#10B981", description: "Fresh green" },
  { name: "<PERSON>", value: "#8B5CF6", description: "Creative purple" },
  { name: "Red", value: "#EF4444", description: "Bold red" },
  { name: "Orange", value: "#F97316", description: "Energetic orange" },
  { name: "Pink", value: "#EC4899", description: "Vibrant pink" },
  { name: "Teal", value: "#14B8A6", description: "Modern teal" },
  { name: "Indigo", value: "#6366F1", description: "Deep indigo" },
  { name: "Gray", value: "#6B7280", description: "Neutral gray" },
];

export default function AppearanceSection({ onBack }: AppearanceSectionProps) {
  const theme = useTheme();
  const styles = createAppearanceSectionStyles(theme);
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const { control, handleSubmit, watch, setValue } = useFormContext();
  const currentThemeColor = watch("theme_color") || "#D4AF37";

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    
    // Extract only appearance fields
    const appearanceData: AppearanceData = {
      theme_color: data.theme_color,
    };

    // Validate the data
    const validation = appearanceSchema.safeParse(appearanceData);
    if (!validation.success) {
      toast.error("Validation Error", "Please select a valid theme color.");
      setIsLoading(false);
      return;
    }

    const { success, error } = await updateAppearance(appearanceData);
    if (success) {
      toast.success("Success", "Appearance updated successfully.");
      onBack();
    } else {
      toast.error("Error", error || "Failed to update appearance.");
    }
    setIsLoading(false);
  };

  const handleColorSelect = (color: string) => {
    setValue("theme_color", color);
  };

  const getSelectedColorInfo = () => {
    return THEME_COLORS.find(color => color.value === currentThemeColor) || 
           { name: "Custom", value: currentThemeColor, description: "Custom color" };
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {/* Current Theme Preview */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Current Theme</Text>
          
          <View style={styles.previewCard}>
            <View style={styles.previewHeader}>
              <View style={[styles.previewColorCircle, { backgroundColor: currentThemeColor }]} />
              <View style={styles.previewInfo}>
                <Text style={styles.previewColorName}>{getSelectedColorInfo().name}</Text>
                <Text style={styles.previewColorDescription}>
                  {getSelectedColorInfo().description}
                </Text>
                <Text style={styles.previewColorValue}>{currentThemeColor}</Text>
              </View>
              <View style={styles.previewIcon}>
                <Eye size={20} color={theme.colors.textSecondary} />
              </View>
            </View>
          </View>
        </View>

        {/* Theme Color Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Choose Theme Color</Text>
          <Text style={styles.sectionDescription}>
            Select a color that represents your brand. This will be used for buttons, links, and accents on your business card.
          </Text>
          
          <View style={styles.colorGrid}>
            {THEME_COLORS.map((color) => (
              <TouchableOpacity
                key={color.value}
                style={[
                  styles.colorOption,
                  currentThemeColor === color.value && styles.colorOptionSelected,
                ]}
                onPress={() => handleColorSelect(color.value)}
              >
                <View style={[styles.colorCircle, { backgroundColor: color.value }]}>
                  {currentThemeColor === color.value && (
                    <View style={styles.colorCheckmark}>
                      <Text style={styles.colorCheckmarkText}>✓</Text>
                    </View>
                  )}
                </View>
                <Text style={styles.colorName}>{color.name}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Theme Preview Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Preview</Text>
          
          <View style={styles.mockupCard}>
            <View style={styles.mockupHeader}>
              <View style={styles.mockupAvatar} />
              <View style={styles.mockupInfo}>
                <Text style={styles.mockupName}>Your Business Name</Text>
                <Text style={styles.mockupTitle}>Your Title</Text>
              </View>
            </View>
            
            <View style={styles.mockupActions}>
              <TouchableOpacity 
                style={[styles.mockupButton, { backgroundColor: currentThemeColor }]}
                disabled
              >
                <Text style={styles.mockupButtonText}>Call Now</Text>
              </TouchableOpacity>
              <TouchableOpacity 
                style={[styles.mockupButton, { backgroundColor: currentThemeColor }]}
                disabled
              >
                <Text style={styles.mockupButtonText}>WhatsApp</Text>
              </TouchableOpacity>
            </View>
            
            <View style={styles.mockupLinks}>
              <Text style={[styles.mockupLink, { color: currentThemeColor }]}>
                Visit Website
              </Text>
              <Text style={[styles.mockupLink, { color: currentThemeColor }]}>
                View Location
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.infoBox}>
          <Text style={styles.infoText}>
            🎨 Your theme color will be applied to buttons, links, and highlights across your business card to create a cohesive brand experience.
          </Text>
        </View>
      </ScrollView>

      {/* Save Button */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.saveButton,
            { backgroundColor: theme.colors.primary },
            isLoading && styles.saveButtonDisabled,
          ]}
          onPress={handleSubmit(onSubmit)}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="white" />
          ) : (
            <Text style={styles.saveButtonText}>Save Appearance</Text>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
}
