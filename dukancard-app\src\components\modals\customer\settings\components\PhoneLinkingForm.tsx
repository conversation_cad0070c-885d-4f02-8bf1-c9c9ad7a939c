import React from "react";
import { View, Text } from "react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { createSettingsModalStyles } from "@/styles/modals/customer/settings-modal";
import { Phone } from "lucide-react-native";

interface PhoneLinkingFormProps {
  currentPhone?: string | null;
}

const PhoneLinkingForm: React.FC<PhoneLinkingFormProps> = ({
  currentPhone,
}) => {
  const theme = useTheme();
  const styles = createSettingsModalStyles(theme);

  return (
    <View style={styles.formContainer}>
      {currentPhone ? (
        <View>
          <Text style={styles.settingTitle}>Current Phone Number</Text>
          <Text style={styles.settingSubtitle}>{currentPhone}</Text>
          <Text style={styles.settingSubtitle}>
            Phone number changes are not currently supported. Contact support if
            you need to update your number.
          </Text>
        </View>
      ) : (
        <View style={{ alignItems: "center", padding: theme.spacing.lg }}>
          <Phone size={48} color={theme.colors.textSecondary} />
          <Text style={[styles.settingTitle, { marginTop: theme.spacing.md }]}>
            No Phone Number
          </Text>
          <Text style={styles.settingSubtitle}>
            No phone number is currently linked to your account. Phone number
            linking is not available at this time.
          </Text>
        </View>
      )}
    </View>
  );
};

export default PhoneLinkingForm;
