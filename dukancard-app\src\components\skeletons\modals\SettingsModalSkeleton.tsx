import React from "react";
import { View } from "react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { createSettingsModalStyles } from "@/styles/modals/customer/settings-modal";
import { SkeletonLoader as Skeleton } from "@/src/components/ui/SkeletonLoader";

const SettingsModalSkeleton = () => {
  const theme = useTheme();
  const styles = createSettingsModalStyles(theme);

  return (
    <View style={styles.container}>
      <View style={styles.section}>
        <Skeleton
          height={30}
          width={150}
          style={{ marginBottom: theme.spacing.md }}
        />
        <View style={styles.settingItem}>
          <Skeleton height={20} width="80%" />
        </View>
        <View style={styles.settingItem}>
          <Skeleton height={20} width="80%" />
        </View>
        <View style={styles.settingItem}>
          <Skeleton height={20} width="80%" />
        </View>
        <View style={styles.settingItem}>
          <Skeleton height={20} width="80%" />
        </View>
      </View>
    </View>
  );
};

export default SettingsModalSkeleton;
