import { StyleSheet } from "react-native";
import { useTheme } from "@/src/hooks/useTheme";

export const createAppearanceSectionStyles = (
  theme: ReturnType<typeof useTheme>
) => {
  const { isDark } = theme;
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: isDark ? "#000" : "#FFF",
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      padding: theme.spacing.md,
      paddingBottom: theme.spacing.xl,
    },
    section: {
      marginBottom: theme.spacing.lg,
    },
    sectionTitle: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
      color: isDark ? "#FFF" : "#000",
      marginBottom: theme.spacing.sm,
    },
    sectionDescription: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing.md,
      lineHeight: 20,
    },
    
    // Preview card styles
    previewCard: {
      backgroundColor: isDark ? "rgba(255, 255, 255, 0.05)" : "rgba(0, 0, 0, 0.02)",
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    previewHeader: {
      flexDirection: "row",
      alignItems: "center",
    },
    previewColorCircle: {
      width: 48,
      height: 48,
      borderRadius: 24,
      marginRight: theme.spacing.sm,
    },
    previewInfo: {
      flex: 1,
    },
    previewColorName: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
      color: isDark ? "#FFF" : "#000",
      marginBottom: 2,
    },
    previewColorDescription: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
      marginBottom: 2,
    },
    previewColorValue: {
      fontSize: theme.typography.fontSize.xs,
      color: theme.colors.textSecondary,
      fontFamily: "monospace",
    },
    previewIcon: {
      padding: theme.spacing.xs,
    },
    
    // Color grid styles
    colorGrid: {
      flexDirection: "row",
      flexWrap: "wrap",
      gap: theme.spacing.sm,
      justifyContent: "space-between",
    },
    colorOption: {
      alignItems: "center",
      width: "18%",
      minWidth: 60,
      marginBottom: theme.spacing.sm,
    },
    colorOptionSelected: {
      transform: [{ scale: 1.05 }],
    },
    colorCircle: {
      width: 48,
      height: 48,
      borderRadius: 24,
      marginBottom: theme.spacing.xs,
      alignItems: "center",
      justifyContent: "center",
      borderWidth: 2,
      borderColor: "transparent",
    },
    colorCheckmark: {
      width: 20,
      height: 20,
      borderRadius: 10,
      backgroundColor: "rgba(255, 255, 255, 0.9)",
      alignItems: "center",
      justifyContent: "center",
    },
    colorCheckmarkText: {
      fontSize: 12,
      fontWeight: "bold",
      color: "#000",
    },
    colorName: {
      fontSize: theme.typography.fontSize.xs,
      color: isDark ? "#FFF" : "#000",
      textAlign: "center",
      fontWeight: "500",
    },

    // Mockup card styles
    mockupCard: {
      backgroundColor: isDark ? "rgba(255, 255, 255, 0.05)" : "rgba(0, 0, 0, 0.02)",
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    mockupHeader: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: theme.spacing.md,
    },
    mockupAvatar: {
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: theme.colors.border,
      marginRight: theme.spacing.sm,
    },
    mockupInfo: {
      flex: 1,
    },
    mockupName: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
      color: isDark ? "#FFF" : "#000",
      marginBottom: 2,
    },
    mockupTitle: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
    },
    mockupActions: {
      flexDirection: "row",
      gap: theme.spacing.sm,
      marginBottom: theme.spacing.md,
    },
    mockupButton: {
      flex: 1,
      paddingVertical: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
      alignItems: "center",
    },
    mockupButtonText: {
      color: "white",
      fontSize: theme.typography.fontSize.sm,
      fontWeight: "600",
    },
    mockupLinks: {
      gap: theme.spacing.xs,
    },
    mockupLink: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: "500",
    },

    // Info box
    infoBox: {
      backgroundColor: isDark ? "rgba(212, 175, 55, 0.1)" : "rgba(212, 175, 55, 0.1)",
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      marginTop: theme.spacing.md,
    },
    infoText: {
      fontSize: theme.typography.fontSize.sm,
      color: isDark ? "#D4AF37" : "#B8860B",
      lineHeight: 20,
    },

    // Footer styles
    footer: {
      padding: theme.spacing.md,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      backgroundColor: isDark ? "#000" : "#FFF",
    },
    saveButton: {
      borderRadius: theme.borderRadius.lg,
      paddingVertical: theme.spacing.md,
      alignItems: "center",
      justifyContent: "center",
      minHeight: 50,
    },
    saveButtonDisabled: {
      opacity: 0.6,
    },
    saveButtonText: {
      color: "white",
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
    },
  });
};
