import { getPlanDisplayName, getPlanLimitDisplayText } from "@/backend/supabase/services/business/planLimitService";

// Mock the supabase client
jest.mock("@/src/config/supabase", () => ({
  supabase: {
    auth: {
      getUser: jest.fn(),
    },
    from: jest.fn(),
  },
}));

// Mock the plans config
jest.mock("@/lib/config/plans", () => {
  const originalModule = jest.requireActual("@/lib/config/plans");
  return {
    ...originalModule,
    PLANS: [
      {
        id: "free",
        name: "Free",
        features: [
          { name: "Contact Management", limit: 100 },
        ],
      },
      {
        id: "basic",
        name: "Basic",
        features: [
          { name: "Contact Management", limit: 500 },
        ],
      },
      {
        id: "pro",
        name: "Pro",
        features: [
          { name: "Contact Management", limit: "unlimited" },
        ],
      },
    ],
    getProductLimit: jest.fn((planId) => {
      switch (planId) {
        case "free": return 100;
        case "basic": return 500;
        case "pro": return 999999;
        default: return 100;
      }
    }),
  };
});

describe("Plan Limit Service", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("getPlanDisplayName", () => {
    it("should return correct plan display names", () => {
      expect(getPlanDisplayName("free" as any)).toBe("Free");
      expect(getPlanDisplayName("basic" as any)).toBe("Basic");
      expect(getPlanDisplayName("pro" as any)).toBe("Pro");
      expect(getPlanDisplayName("invalid" as any)).toBe("Free");
    });
  });

  describe("getPlanLimitDisplayText", () => {
    it("should return 'Unlimited' for null limit", () => {
      expect(getPlanLimitDisplayText(null)).toBe("Unlimited");
    });

    it("should return number as string for numeric limit", () => {
      expect(getPlanLimitDisplayText(5)).toBe("5");
      expect(getPlanLimitDisplayText(25)).toBe("25");
    });
  });

  describe("Plan Limit Logic", () => {
    it("should correctly identify when user can add more products", () => {
      // Test free plan scenario
      const freeUserInfo = {
        planId: "free" as const,
        planLimit: 100,
        currentAvailableCount: 50,
        totalCount: 80,
        canAddMore: 50 < 100,
        isAtLimit: 50 >= 100,
      };

      expect(freeUserInfo.canAddMore).toBe(true);
      expect(freeUserInfo.isAtLimit).toBe(false);
    });

    it("should correctly identify when user is at plan limit", () => {
      // Test at-limit scenario
      const atLimitUserInfo = {
        planId: "free" as const,
        planLimit: 100,
        currentAvailableCount: 100,
        totalCount: 120,
        canAddMore: 100 < 100,
        isAtLimit: 100 >= 100,
      };

      expect(atLimitUserInfo.canAddMore).toBe(false);
      expect(atLimitUserInfo.isAtLimit).toBe(true);
    });

    it("should handle unlimited plans correctly", () => {
      // Test unlimited plan scenario
      const unlimitedUserInfo = {
        planId: "pro" as const,
        planLimit: null,
        currentAvailableCount: 1000,
        totalCount: 1500,
        canAddMore: true, // always true for unlimited
        isAtLimit: false, // never at limit for unlimited
      };

      expect(unlimitedUserInfo.canAddMore).toBe(true);
      expect(unlimitedUserInfo.isAtLimit).toBe(false);
    });
  });

  describe("Plan Configuration Integration", () => {
    it("should validate plan limit conversion logic", () => {
      // Test the logic for converting getProductLimit results to our format
      const testConversion = (productLimit: number) => {
        if (productLimit >= 999999) {
          return null; // unlimited
        }
        return productLimit;
      };

      expect(testConversion(100)).toBe(100);
      expect(testConversion(500)).toBe(500);
      expect(testConversion(999999)).toBe(null); // unlimited
    });

    it("should handle plan limit validation correctly", () => {
      // Test plan limit validation logic
      const validatePlanLimit = (currentCount: number, planLimit: number | null) => {
        if (planLimit === null) {
          return { canAddMore: true, isAtLimit: false };
        }
        return {
          canAddMore: currentCount < planLimit,
          isAtLimit: currentCount >= planLimit,
        };
      };

      // Test with numeric limits
      expect(validatePlanLimit(5, 10)).toEqual({ canAddMore: true, isAtLimit: false });
      expect(validatePlanLimit(10, 10)).toEqual({ canAddMore: false, isAtLimit: true });
      expect(validatePlanLimit(15, 10)).toEqual({ canAddMore: false, isAtLimit: true });

      // Test with unlimited (null)
      expect(validatePlanLimit(1000, null)).toEqual({ canAddMore: true, isAtLimit: false });
    });
  });
});
