// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<Input /> applies custom containerStyle and style 1`] = `
<View
  style={
    [
      {
        "marginBottom": 8,
      },
      {
        "borderColor": "red",
        "borderWidth": 2,
      },
    ]
  }
>
  <View
    style={
      {
        "position": "relative",
      }
    }
  >
    <TextInput
      autoCapitalize="sentences"
      autoCorrect={true}
      keyboardType="default"
      onBlur={[Function]}
      onChangeText={[MockFunction]}
      onFocus={[Function]}
      placeholderTextColor="#808080"
      secureTextEntry={false}
      style={
        {
          "fontSize": 20,
        }
      }
    />
  </View>
</View>
`;

exports[`<Input /> renders correctly with basic props 1`] = `
<View
  style={
    [
      {
        "marginBottom": 8,
      },
      undefined,
    ]
  }
>
  <View
    style={
      {
        "position": "relative",
      }
    }
  >
    <TextInput
      autoCapitalize="sentences"
      autoCorrect={true}
      keyboardType="default"
      onBlur={[Function]}
      onChangeText={[MockFunction]}
      onFocus={[Function]}
      placeholder="Enter text"
      placeholderTextColor="#808080"
      secureTextEntry={false}
      style={
        [
          {
            "backgroundColor": "transparent",
            "borderColor": "#E5E5E5",
            "borderRadius": 12,
            "borderWidth": 1,
            "color": "#000000",
            "fontSize": 16,
            "fontWeight": "normal",
            "height": 48,
            "letterSpacing": 0.5,
            "paddingLeft": 8,
            "paddingRight": 8,
            "paddingVertical": 0,
            "textAlignVertical": "center",
          },
          undefined,
        ]
      }
    />
  </View>
</View>
`;

exports[`<Input /> renders leftIcon 1`] = `
<View
  style={
    [
      {
        "marginBottom": 8,
      },
      undefined,
    ]
  }
>
  <View
    style={
      {
        "position": "relative",
      }
    }
  >
    <View
      style={
        {
          "left": 8,
          "position": "absolute",
          "top": 14,
          "zIndex": 1,
        }
      }
    >
      <View
        ref={null}
        style={
          {
            "backgroundColor": undefined,
            "height": undefined,
            "width": undefined,
          }
        }
        testID="mail-icon"
      >
        Mail
      </View>
    </View>
    <TextInput
      autoCapitalize="sentences"
      autoCorrect={true}
      keyboardType="default"
      onBlur={[Function]}
      onChangeText={[MockFunction]}
      onFocus={[Function]}
      placeholderTextColor="#808080"
      secureTextEntry={false}
      style={
        [
          {
            "backgroundColor": "transparent",
            "borderColor": "#E5E5E5",
            "borderRadius": 12,
            "borderWidth": 1,
            "color": "#000000",
            "fontSize": 16,
            "fontWeight": "normal",
            "height": 48,
            "letterSpacing": 0.5,
            "paddingLeft": 40,
            "paddingRight": 8,
            "paddingVertical": 0,
            "textAlignVertical": "center",
          },
          undefined,
        ]
      }
    />
  </View>
</View>
`;

exports[`<Input /> renders rightIcon 1`] = `
<View
  style={
    [
      {
        "marginBottom": 8,
      },
      undefined,
    ]
  }
>
  <View
    style={
      {
        "position": "relative",
      }
    }
  >
    <TouchableOpacity
      disabled={true}
      style={
        {
          "position": "absolute",
          "right": 8,
          "top": 14,
          "zIndex": 1,
        }
      }
    >
      <View
        ref={null}
        style={
          {
            "backgroundColor": undefined,
            "height": undefined,
            "width": undefined,
          }
        }
        testID="eye-icon"
      >
        Eye
      </View>
    </TouchableOpacity>
    <TextInput
      autoCapitalize="sentences"
      autoCorrect={true}
      keyboardType="default"
      onBlur={[Function]}
      onChangeText={[MockFunction]}
      onFocus={[Function]}
      placeholderTextColor="#808080"
      secureTextEntry={false}
      style={
        [
          {
            "backgroundColor": "transparent",
            "borderColor": "#E5E5E5",
            "borderRadius": 12,
            "borderWidth": 1,
            "color": "#000000",
            "fontSize": 16,
            "fontWeight": "normal",
            "height": 48,
            "letterSpacing": 0.5,
            "paddingLeft": 8,
            "paddingRight": 42,
            "paddingVertical": 0,
            "textAlignVertical": "center",
          },
          undefined,
        ]
      }
    />
  </View>
</View>
`;
