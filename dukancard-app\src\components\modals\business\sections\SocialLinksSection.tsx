import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from "react-native";
import { Controller, useFormContext } from "react-hook-form";
import { MessageCircle, Instagram, Facebook } from "lucide-react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { FormField } from "@/src/components/ui/FormField";
import { useToast } from "@/src/components/ui/Toast";
import { updateSocialLinks } from "@/backend/supabase/services/business/businessCardService";
import { SocialLinksData, socialLinksSchema } from "@/backend/supabase/services/business/schemas";
import { createSocialLinksSectionStyles } from "@/styles/modals/business/sections/social-links-section";

interface SocialLinksSectionProps {
  onBack: () => void;
}

export default function SocialLinksSection({ onBack }: SocialLinksSectionProps) {
  const theme = useTheme();
  const styles = createSocialLinksSectionStyles(theme);
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const { control, handleSubmit } = useFormContext();

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    
    // Extract only social links fields
    const socialLinksData: SocialLinksData = {
      instagram_url: data.instagram_url,
      facebook_url: data.facebook_url,
      whatsapp_number: data.whatsapp_number,
    };

    // Validate the data
    const validation = socialLinksSchema.safeParse(socialLinksData);
    if (!validation.success) {
      toast.error("Validation Error", "Please check all URLs and phone numbers.");
      setIsLoading(false);
      return;
    }

    // Convert null values to undefined for API compatibility
    const apiData = {
      ...socialLinksData,
      instagram_url: socialLinksData.instagram_url || undefined,
      facebook_url: socialLinksData.facebook_url || undefined,
      whatsapp_number: socialLinksData.whatsapp_number || undefined,
    };

    const { success, error } = await updateSocialLinks(apiData);
    if (success) {
      toast.success("Success", "Social links updated successfully.");
      onBack();
    } else {
      toast.error("Error", error || "Failed to update social links.");
    }
    setIsLoading(false);
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {/* WhatsApp Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>WhatsApp Business</Text>
          
          <View style={styles.socialCard}>
            <View style={styles.socialCardHeader}>
              <View style={styles.socialIconContainer}>
                <MessageCircle size={24} color="#25D366" />
              </View>
              <View style={styles.socialCardInfo}>
                <Text style={styles.socialCardTitle}>WhatsApp Number</Text>
                <Text style={styles.socialCardDescription}>
                  Let customers contact you directly via WhatsApp
                </Text>
              </View>
            </View>
            
            <Controller
              control={control}
              name="whatsapp_number"
              render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
                <FormField
                  label="WhatsApp Number"
                  value={value || ""}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  error={error?.message}
                  type="phone"
                  placeholder="Enter 10-digit WhatsApp number"
                  maxLength={10}
                />
              )}
            />
          </View>
        </View>

        {/* Instagram Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Instagram</Text>
          
          <View style={styles.socialCard}>
            <View style={styles.socialCardHeader}>
              <View style={styles.socialIconContainer}>
                <Instagram size={24} color="#E4405F" />
              </View>
              <View style={styles.socialCardInfo}>
                <Text style={styles.socialCardTitle}>Instagram Profile</Text>
                <Text style={styles.socialCardDescription}>
                  Share your Instagram profile with customers
                </Text>
              </View>
            </View>
            
            <Controller
              control={control}
              name="instagram_url"
              render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
                <FormField
                  label="Instagram URL"
                  value={value || ""}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  error={error?.message}
                  placeholder="https://instagram.com/yourbusiness"
                  autoCapitalize="none"
                />
              )}
            />
          </View>
        </View>

        {/* Facebook Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Facebook</Text>
          
          <View style={styles.socialCard}>
            <View style={styles.socialCardHeader}>
              <View style={styles.socialIconContainer}>
                <Facebook size={24} color="#1877F2" />
              </View>
              <View style={styles.socialCardInfo}>
                <Text style={styles.socialCardTitle}>Facebook Page</Text>
                <Text style={styles.socialCardDescription}>
                  Connect your Facebook business page
                </Text>
              </View>
            </View>
            
            <Controller
              control={control}
              name="facebook_url"
              render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
                <FormField
                  label="Facebook URL"
                  value={value || ""}
                  onChangeText={onChange}
                  onBlur={onBlur}
                  error={error?.message}
                  placeholder="https://facebook.com/yourbusiness"
                  autoCapitalize="none"
                />
              )}
            />
          </View>
        </View>

        {/* Tips Section */}
        <View style={styles.tipsSection}>
          <Text style={styles.tipsTitle}>Social Media Tips</Text>
          <View style={styles.tipsList}>
            <View style={styles.tipItem}>
              <Text style={styles.tipBullet}>•</Text>
              <Text style={styles.tipText}>
                Use your complete profile URLs for better visibility
              </Text>
            </View>
            <View style={styles.tipItem}>
              <Text style={styles.tipBullet}>•</Text>
              <Text style={styles.tipText}>
                Keep your social media profiles updated with business information
              </Text>
            </View>
            <View style={styles.tipItem}>
              <Text style={styles.tipBullet}>•</Text>
              <Text style={styles.tipText}>
                WhatsApp Business helps customers reach you instantly
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.infoBox}>
          <Text style={styles.infoText}>
            🔗 Social media links help customers connect with your business across different platforms and build trust.
          </Text>
        </View>
      </ScrollView>

      {/* Save Button */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.saveButton,
            { backgroundColor: theme.colors.primary },
            isLoading && styles.saveButtonDisabled,
          ]}
          onPress={handleSubmit(onSubmit)}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="white" />
          ) : (
            <Text style={styles.saveButtonText}>Save Social Links</Text>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
}
