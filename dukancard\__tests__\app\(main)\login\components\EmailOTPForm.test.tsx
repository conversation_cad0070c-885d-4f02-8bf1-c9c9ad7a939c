import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { EmailOTPForm } from '@/app/(main)/login/components/EmailOTPForm';

describe('EmailOTPForm', () => {
  const onEmailSubmit = jest.fn();
  const onOTPSubmit = jest.fn();
  const onResendOTP = jest.fn();
  const onBackToEmail = jest.fn();

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders email form by default', () => {
    render(
      <EmailOTPForm
        step="email"
        email=""
        countdown={0}
        isPending={false}
        onEmailSubmit={onEmailSubmit}
        onOTPSubmit={onOTPSubmit}
        onResendOTP={onResendOTP}
        onBackToEmail={onBackToEmail}
      />
    );
    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /continue/i })).toBeInTheDocument();
  });

  it('calls onEmailSubmit with the correct email', async () => {
    render(
      <EmailOTPForm
        step="email"
        email=""
        countdown={0}
        isPending={false}
        onEmailSubmit={onEmailSubmit}
        onOTPSubmit={onOTPSubmit}
        onResendOTP={onResendOTP}
        onBackToEmail={onBackToEmail}
      />
    );
    const emailInput = screen.getByLabelText(/email address/i);
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.click(screen.getByRole('button', { name: /continue/i }));
    await waitFor(() => {
      expect(onEmailSubmit).toHaveBeenCalledWith({ email: '<EMAIL>' });
    });
  });

  it('renders OTP form when step is "otp"', () => {
    render(
      <EmailOTPForm
        step="otp"
        email="<EMAIL>"
        countdown={0}
        isPending={false}
        onEmailSubmit={onEmailSubmit}
        onOTPSubmit={onOTPSubmit}
        onResendOTP={onResendOTP}
        onBackToEmail={onBackToEmail}
      />
    );
    expect(screen.getByText(/enter verification code/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /verify & sign in/i })).toBeInTheDocument();
  });

  it('calls onOTPSubmit with the correct OTP', async () => {
    render(
      <EmailOTPForm
        step="otp"
        email="<EMAIL>"
        countdown={0}
        isPending={false}
        onEmailSubmit={onEmailSubmit}
        onOTPSubmit={onOTPSubmit}
        onResendOTP={onResendOTP}
        onBackToEmail={onBackToEmail}
      />
    );
    const otpInput = screen.getByLabelText(/enter verification code/i);
    fireEvent.change(otpInput, { target: { value: '123456' } });
    fireEvent.click(screen.getByRole('button', { name: /verify & sign in/i }));
    await waitFor(() => {
      expect(onOTPSubmit).toHaveBeenCalledWith({ otp: '123456' });
    });
  });

  it('disables resend OTP button when countdown is greater than 0', () => {
    render(
      <EmailOTPForm
        step="otp"
        email="<EMAIL>"
        countdown={30}
        isPending={false}
        onEmailSubmit={onEmailSubmit}
        onOTPSubmit={onOTPSubmit}
        onResendOTP={onResendOTP}
        onBackToEmail={onBackToEmail}
      />
    );
    expect(screen.getByRole('button', { name: /resend otp in 30s/i })).toBeDisabled();
  });

  it('calls onResendOTP when resend button is clicked', () => {
    render(
      <EmailOTPForm
        step="otp"
        email="<EMAIL>"
        countdown={0}
        isPending={false}
        onEmailSubmit={onEmailSubmit}
        onOTPSubmit={onOTPSubmit}
        onResendOTP={onResendOTP}
        onBackToEmail={onBackToEmail}
      />
    );
    fireEvent.click(screen.getByRole('button', { name: /resend otp/i }));
    expect(onResendOTP).toHaveBeenCalled();
  });

  it('calls onBackToEmail when back button is clicked', () => {
    render(
      <EmailOTPForm
        step="otp"
        email="<EMAIL>"
        countdown={0}
        isPending={false}
        onEmailSubmit={onEmailSubmit}
        onOTPSubmit={onOTPSubmit}
        onResendOTP={onResendOTP}
        onBackToEmail={onBackToEmail}
      />
    );
    fireEvent.click(screen.getByText(/change email address/i));
    expect(onBackToEmail).toHaveBeenCalled();
  });
});