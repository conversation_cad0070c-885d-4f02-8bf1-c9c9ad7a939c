// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<OTPInput /> auto-focuses the first input by default 1`] = `
<View
  style={
    {
      "width": "100%",
    }
  }
>
  <View
    style={
      {
        "flexDirection": "row",
        "gap": 12,
        "justifyContent": "space-between",
        "width": "100%",
      }
    }
  >
    <Pressable
      onPress={[Function]}
      style={
        {
          "flex": 1,
        }
      }
    >
      <TextInput
        autoComplete="one-time-code"
        editable={true}
        keyboardType="numeric"
        maxLength={1}
        onChangeText={[Function]}
        onFocus={[Function]}
        onKeyPress={[Function]}
        ref={[Function]}
        selectTextOnFocus={true}
        style={
          [
            {
              "aspectRatio": 1,
              "borderRadius": 12,
              "borderWidth": 1,
              "flex": 1,
              "fontWeight": "600",
              "textAlign": "center",
            },
            {
              "backgroundColor": "#ffffff",
              "borderColor": "#C29D5B",
              "borderWidth": 2,
              "color": "#000000",
              "elevation": 2,
              "fontSize": 20,
              "opacity": 1,
              "shadowColor": "#C29D5B",
              "shadowOffset": {
                "height": 2,
                "width": 0,
              },
              "shadowOpacity": 0.1,
              "shadowRadius": 4,
            },
          ]
        }
        testID="otp-input-0"
        textAlign="center"
        value=""
      />
    </Pressable>
    <Pressable
      onPress={[Function]}
      style={
        {
          "flex": 1,
        }
      }
    >
      <TextInput
        autoComplete="one-time-code"
        editable={true}
        keyboardType="numeric"
        maxLength={1}
        onChangeText={[Function]}
        onFocus={[Function]}
        onKeyPress={[Function]}
        ref={[Function]}
        selectTextOnFocus={true}
        style={
          [
            {
              "aspectRatio": 1,
              "borderRadius": 12,
              "borderWidth": 1,
              "flex": 1,
              "fontWeight": "600",
              "textAlign": "center",
            },
            {
              "backgroundColor": "#ffffff",
              "borderColor": "#cccccc",
              "borderWidth": 1,
              "color": "#000000",
              "elevation": 0,
              "fontSize": 20,
              "opacity": 1,
              "shadowColor": "transparent",
              "shadowOffset": {
                "height": 2,
                "width": 0,
              },
              "shadowOpacity": 0,
              "shadowRadius": 4,
            },
          ]
        }
        testID="otp-input-1"
        textAlign="center"
        value=""
      />
    </Pressable>
    <Pressable
      onPress={[Function]}
      style={
        {
          "flex": 1,
        }
      }
    >
      <TextInput
        autoComplete="one-time-code"
        editable={true}
        keyboardType="numeric"
        maxLength={1}
        onChangeText={[Function]}
        onFocus={[Function]}
        onKeyPress={[Function]}
        ref={[Function]}
        selectTextOnFocus={true}
        style={
          [
            {
              "aspectRatio": 1,
              "borderRadius": 12,
              "borderWidth": 1,
              "flex": 1,
              "fontWeight": "600",
              "textAlign": "center",
            },
            {
              "backgroundColor": "#ffffff",
              "borderColor": "#cccccc",
              "borderWidth": 1,
              "color": "#000000",
              "elevation": 0,
              "fontSize": 20,
              "opacity": 1,
              "shadowColor": "transparent",
              "shadowOffset": {
                "height": 2,
                "width": 0,
              },
              "shadowOpacity": 0,
              "shadowRadius": 4,
            },
          ]
        }
        testID="otp-input-2"
        textAlign="center"
        value=""
      />
    </Pressable>
    <Pressable
      onPress={[Function]}
      style={
        {
          "flex": 1,
        }
      }
    >
      <TextInput
        autoComplete="one-time-code"
        editable={true}
        keyboardType="numeric"
        maxLength={1}
        onChangeText={[Function]}
        onFocus={[Function]}
        onKeyPress={[Function]}
        ref={[Function]}
        selectTextOnFocus={true}
        style={
          [
            {
              "aspectRatio": 1,
              "borderRadius": 12,
              "borderWidth": 1,
              "flex": 1,
              "fontWeight": "600",
              "textAlign": "center",
            },
            {
              "backgroundColor": "#ffffff",
              "borderColor": "#cccccc",
              "borderWidth": 1,
              "color": "#000000",
              "elevation": 0,
              "fontSize": 20,
              "opacity": 1,
              "shadowColor": "transparent",
              "shadowOffset": {
                "height": 2,
                "width": 0,
              },
              "shadowOpacity": 0,
              "shadowRadius": 4,
            },
          ]
        }
        testID="otp-input-3"
        textAlign="center"
        value=""
      />
    </Pressable>
    <Pressable
      onPress={[Function]}
      style={
        {
          "flex": 1,
        }
      }
    >
      <TextInput
        autoComplete="one-time-code"
        editable={true}
        keyboardType="numeric"
        maxLength={1}
        onChangeText={[Function]}
        onFocus={[Function]}
        onKeyPress={[Function]}
        ref={[Function]}
        selectTextOnFocus={true}
        style={
          [
            {
              "aspectRatio": 1,
              "borderRadius": 12,
              "borderWidth": 1,
              "flex": 1,
              "fontWeight": "600",
              "textAlign": "center",
            },
            {
              "backgroundColor": "#ffffff",
              "borderColor": "#cccccc",
              "borderWidth": 1,
              "color": "#000000",
              "elevation": 0,
              "fontSize": 20,
              "opacity": 1,
              "shadowColor": "transparent",
              "shadowOffset": {
                "height": 2,
                "width": 0,
              },
              "shadowOpacity": 0,
              "shadowRadius": 4,
            },
          ]
        }
        testID="otp-input-4"
        textAlign="center"
        value=""
      />
    </Pressable>
    <Pressable
      onPress={[Function]}
      style={
        {
          "flex": 1,
        }
      }
    >
      <TextInput
        autoComplete="one-time-code"
        editable={true}
        keyboardType="numeric"
        maxLength={1}
        onChangeText={[Function]}
        onFocus={[Function]}
        onKeyPress={[Function]}
        ref={[Function]}
        selectTextOnFocus={true}
        style={
          [
            {
              "aspectRatio": 1,
              "borderRadius": 12,
              "borderWidth": 1,
              "flex": 1,
              "fontWeight": "600",
              "textAlign": "center",
            },
            {
              "backgroundColor": "#ffffff",
              "borderColor": "#cccccc",
              "borderWidth": 1,
              "color": "#000000",
              "elevation": 0,
              "fontSize": 20,
              "opacity": 1,
              "shadowColor": "transparent",
              "shadowOffset": {
                "height": 2,
                "width": 0,
              },
              "shadowOpacity": 0,
              "shadowRadius": 4,
            },
          ]
        }
        testID="otp-input-5"
        textAlign="center"
        value=""
      />
    </Pressable>
  </View>
</View>
`;

exports[`<OTPInput /> displays an error message 1`] = `
<View
  style={
    {
      "width": "100%",
    }
  }
>
  <View
    style={
      {
        "flexDirection": "row",
        "gap": 12,
        "justifyContent": "space-between",
        "width": "100%",
      }
    }
  >
    <Pressable
      onPress={[Function]}
      style={
        {
          "flex": 1,
        }
      }
    >
      <TextInput
        autoComplete="one-time-code"
        editable={true}
        keyboardType="numeric"
        maxLength={1}
        onChangeText={[Function]}
        onFocus={[Function]}
        onKeyPress={[Function]}
        ref={[Function]}
        selectTextOnFocus={true}
        style={
          [
            {
              "aspectRatio": 1,
              "borderRadius": 12,
              "borderWidth": 1,
              "flex": 1,
              "fontWeight": "600",
              "textAlign": "center",
            },
            {
              "backgroundColor": "#ffffff",
              "borderColor": "#ff0000",
              "borderWidth": 2,
              "color": "#000000",
              "elevation": 2,
              "fontSize": 20,
              "opacity": 1,
              "shadowColor": "#C29D5B",
              "shadowOffset": {
                "height": 2,
                "width": 0,
              },
              "shadowOpacity": 0.1,
              "shadowRadius": 4,
            },
          ]
        }
        testID="otp-input-0"
        textAlign="center"
        value=""
      />
    </Pressable>
    <Pressable
      onPress={[Function]}
      style={
        {
          "flex": 1,
        }
      }
    >
      <TextInput
        autoComplete="one-time-code"
        editable={true}
        keyboardType="numeric"
        maxLength={1}
        onChangeText={[Function]}
        onFocus={[Function]}
        onKeyPress={[Function]}
        ref={[Function]}
        selectTextOnFocus={true}
        style={
          [
            {
              "aspectRatio": 1,
              "borderRadius": 12,
              "borderWidth": 1,
              "flex": 1,
              "fontWeight": "600",
              "textAlign": "center",
            },
            {
              "backgroundColor": "#ffffff",
              "borderColor": "#ff0000",
              "borderWidth": 2,
              "color": "#000000",
              "elevation": 0,
              "fontSize": 20,
              "opacity": 1,
              "shadowColor": "transparent",
              "shadowOffset": {
                "height": 2,
                "width": 0,
              },
              "shadowOpacity": 0,
              "shadowRadius": 4,
            },
          ]
        }
        testID="otp-input-1"
        textAlign="center"
        value=""
      />
    </Pressable>
    <Pressable
      onPress={[Function]}
      style={
        {
          "flex": 1,
        }
      }
    >
      <TextInput
        autoComplete="one-time-code"
        editable={true}
        keyboardType="numeric"
        maxLength={1}
        onChangeText={[Function]}
        onFocus={[Function]}
        onKeyPress={[Function]}
        ref={[Function]}
        selectTextOnFocus={true}
        style={
          [
            {
              "aspectRatio": 1,
              "borderRadius": 12,
              "borderWidth": 1,
              "flex": 1,
              "fontWeight": "600",
              "textAlign": "center",
            },
            {
              "backgroundColor": "#ffffff",
              "borderColor": "#ff0000",
              "borderWidth": 2,
              "color": "#000000",
              "elevation": 0,
              "fontSize": 20,
              "opacity": 1,
              "shadowColor": "transparent",
              "shadowOffset": {
                "height": 2,
                "width": 0,
              },
              "shadowOpacity": 0,
              "shadowRadius": 4,
            },
          ]
        }
        testID="otp-input-2"
        textAlign="center"
        value=""
      />
    </Pressable>
    <Pressable
      onPress={[Function]}
      style={
        {
          "flex": 1,
        }
      }
    >
      <TextInput
        autoComplete="one-time-code"
        editable={true}
        keyboardType="numeric"
        maxLength={1}
        onChangeText={[Function]}
        onFocus={[Function]}
        onKeyPress={[Function]}
        ref={[Function]}
        selectTextOnFocus={true}
        style={
          [
            {
              "aspectRatio": 1,
              "borderRadius": 12,
              "borderWidth": 1,
              "flex": 1,
              "fontWeight": "600",
              "textAlign": "center",
            },
            {
              "backgroundColor": "#ffffff",
              "borderColor": "#ff0000",
              "borderWidth": 2,
              "color": "#000000",
              "elevation": 0,
              "fontSize": 20,
              "opacity": 1,
              "shadowColor": "transparent",
              "shadowOffset": {
                "height": 2,
                "width": 0,
              },
              "shadowOpacity": 0,
              "shadowRadius": 4,
            },
          ]
        }
        testID="otp-input-3"
        textAlign="center"
        value=""
      />
    </Pressable>
    <Pressable
      onPress={[Function]}
      style={
        {
          "flex": 1,
        }
      }
    >
      <TextInput
        autoComplete="one-time-code"
        editable={true}
        keyboardType="numeric"
        maxLength={1}
        onChangeText={[Function]}
        onFocus={[Function]}
        onKeyPress={[Function]}
        ref={[Function]}
        selectTextOnFocus={true}
        style={
          [
            {
              "aspectRatio": 1,
              "borderRadius": 12,
              "borderWidth": 1,
              "flex": 1,
              "fontWeight": "600",
              "textAlign": "center",
            },
            {
              "backgroundColor": "#ffffff",
              "borderColor": "#ff0000",
              "borderWidth": 2,
              "color": "#000000",
              "elevation": 0,
              "fontSize": 20,
              "opacity": 1,
              "shadowColor": "transparent",
              "shadowOffset": {
                "height": 2,
                "width": 0,
              },
              "shadowOpacity": 0,
              "shadowRadius": 4,
            },
          ]
        }
        testID="otp-input-4"
        textAlign="center"
        value=""
      />
    </Pressable>
    <Pressable
      onPress={[Function]}
      style={
        {
          "flex": 1,
        }
      }
    >
      <TextInput
        autoComplete="one-time-code"
        editable={true}
        keyboardType="numeric"
        maxLength={1}
        onChangeText={[Function]}
        onFocus={[Function]}
        onKeyPress={[Function]}
        ref={[Function]}
        selectTextOnFocus={true}
        style={
          [
            {
              "aspectRatio": 1,
              "borderRadius": 12,
              "borderWidth": 1,
              "flex": 1,
              "fontWeight": "600",
              "textAlign": "center",
            },
            {
              "backgroundColor": "#ffffff",
              "borderColor": "#ff0000",
              "borderWidth": 2,
              "color": "#000000",
              "elevation": 0,
              "fontSize": 20,
              "opacity": 1,
              "shadowColor": "transparent",
              "shadowOffset": {
                "height": 2,
                "width": 0,
              },
              "shadowOpacity": 0,
              "shadowRadius": 4,
            },
          ]
        }
        testID="otp-input-5"
        textAlign="center"
        value=""
      />
    </Pressable>
  </View>
  <Text
    style={
      [
        {
          "fontSize": 14,
          "fontWeight": "500",
          "marginTop": 8,
          "textAlign": "center",
        },
        {
          "color": "#ff0000",
        },
      ]
    }
  >
    Invalid OTP
  </Text>
</View>
`;
