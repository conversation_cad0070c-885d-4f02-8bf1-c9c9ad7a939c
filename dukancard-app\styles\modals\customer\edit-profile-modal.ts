import { StyleSheet } from "react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { responsiveFontSize } from "@/lib/theme/colors";

export const createEditProfileModalStyles = (
  theme: ReturnType<typeof useTheme>
) => {
  const { isDark } = theme;
  return StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: isDark ? "#000" : "#FFF",
    },
    keyboardAvoidingView: {
      flex: 1,
    },
    modalContainer: {
      flex: 1,
      backgroundColor: isDark ? "#000" : "#FFF",
    },
    header: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: "600",
      color: isDark ? "#FFF" : "#000",
    },
    closeButton: {
      padding: theme.spacing.xs,
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      paddingBottom: theme.spacing.xxxl, // Space for footer
    },
    form: {
      paddingHorizontal: theme.spacing.lg,
      paddingBottom: theme.spacing.lg,
    },
    footer: {
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.md,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      backgroundColor: isDark ? "#000" : "#FFF",
    },
    submitButton: {
      borderRadius: theme.borderRadius.lg,
      paddingVertical: theme.spacing.md,
      alignItems: "center",
      justifyContent: "center",
      minHeight: responsiveFontSize(56),
    },
    submitButtonDisabled: {
      opacity: 0.6,
    },
    submitButtonText: {
      color: theme.colors.primaryForeground,
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
    },
    // Copied from complete-customer-profile-styles
    inputGroup: {
      marginBottom: theme.spacing.md,
    },
    label: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: "500",
      marginBottom: theme.spacing.xs,
      color: isDark ? "#FFF" : "#000",
    },
    input: {
      borderWidth: 1.5,
      borderRadius: theme.borderRadius.lg,
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      fontSize: theme.typography.fontSize.base,
      minHeight: responsiveFontSize(52),
      marginBottom: theme.spacing.xs,
      color: isDark ? "#FFF" : "#000",
    },
    inputError: {
      borderColor: "#EF4444",
    },
    errorText: {
      color: theme.colors.error,
      fontSize: theme.typography.fontSize.xs,
      marginTop: theme.spacing.xs,
    },
    row: {
      flexDirection: "row",
      justifyContent: "space-between",
      gap: theme.spacing.sm,
    },
    halfWidth: {
      flex: 1,
    },
  });
};
