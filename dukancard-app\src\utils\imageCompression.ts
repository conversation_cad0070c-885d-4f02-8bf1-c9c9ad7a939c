import { manipulateAsync, SaveFormat } from 'expo-image-manipulator';

export interface CompressionOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number;
  format?: SaveFormat;
  targetSizeKB?: number;
}

export interface CompressionResult {
  uri: string;
  base64?: string;
  width: number;
  height: number;
  size: number; // in bytes
}

/**
 * Compress an image with aggressive optimization similar to Next.js implementation
 * Targets ~100KB output size with good quality
 */
export async function compressImageUltraAggressive(
  imageUri: string,
  options: CompressionOptions = {}
): Promise<CompressionResult> {
  const {
    maxWidth = 800,
    maxHeight = 800,
    quality = 0.8,
    format = SaveFormat.JPEG,
    targetSizeKB = 100,
  } = options;

  try {
    // First pass: Resize and compress
    let result = await manipulateAsync(
      imageUri,
      [
        {
          resize: {
            width: maxWidth,
            height: maxHeight,
          },
        },
      ],
      {
        compress: quality,
        format,
        base64: true,
      }
    );

    // Calculate current size
    let currentSizeKB = result.base64 ? (result.base64.length * 0.75) / 1024 : 0;

    // If still too large, reduce quality iteratively
    let currentQuality = quality;
    let attempts = 0;
    const maxAttempts = 5;

    while (currentSizeKB > targetSizeKB && attempts < maxAttempts && currentQuality > 0.3) {
      attempts++;
      currentQuality = Math.max(0.3, currentQuality - 0.15);

      result = await manipulateAsync(
        result.uri,
        [],
        {
          compress: currentQuality,
          format,
          base64: true,
        }
      );

      currentSizeKB = result.base64 ? (result.base64.length * 0.75) / 1024 : 0;
    }

    // If still too large, reduce dimensions
    if (currentSizeKB > targetSizeKB && attempts >= maxAttempts) {
      const scaleFactor = Math.sqrt(targetSizeKB / currentSizeKB);
      const newWidth = Math.floor(result.width * scaleFactor);
      const newHeight = Math.floor(result.height * scaleFactor);

      result = await manipulateAsync(
        result.uri,
        [
          {
            resize: {
              width: Math.max(200, newWidth),
              height: Math.max(200, newHeight),
            },
          },
        ],
        {
          compress: 0.7,
          format,
          base64: true,
        }
      );
    }

    return {
      uri: result.uri,
      base64: result.base64,
      width: result.width,
      height: result.height,
      size: result.base64 ? (result.base64.length * 0.75) : 0,
    };
  } catch (error) {
    console.error('Image compression failed:', error);
    throw new Error('Failed to compress image');
  }
}

/**
 * Compress multiple images in parallel
 */
export async function compressMultipleImages(
  imageUris: string[],
  options: CompressionOptions = {}
): Promise<CompressionResult[]> {
  try {
    const compressionPromises = imageUris.map(uri => 
      compressImageUltraAggressive(uri, options)
    );

    return await Promise.all(compressionPromises);
  } catch (error) {
    console.error('Multiple image compression failed:', error);
    throw new Error('Failed to compress images');
  }
}

/**
 * Convert compressed image to base64 data URL format
 */
export function toBase64DataUrl(
  base64: string,
  format: SaveFormat = SaveFormat.JPEG
): string {
  const mimeType = format === SaveFormat.PNG ? 'image/png' : 'image/jpeg';
  return `data:${mimeType};base64,${base64}`;
}

/**
 * Get image dimensions without loading the full image
 */
export async function getImageDimensions(imageUri: string): Promise<{ width: number; height: number }> {
  try {
    const result = await manipulateAsync(
      imageUri,
      [],
      {
        compress: 1,
        format: SaveFormat.JPEG,
      }
    );

    return {
      width: result.width,
      height: result.height,
    };
  } catch (error) {
    console.error('Failed to get image dimensions:', error);
    throw new Error('Failed to get image dimensions');
  }
}

/**
 * Validate image file size and format
 */
export function validateImage(
  base64: string,
  maxSizeKB: number = 15000 // 15MB default
): { isValid: boolean; error?: string; sizeKB: number } {
  try {
    const sizeKB = (base64.length * 0.75) / 1024;

    if (sizeKB > maxSizeKB) {
      return {
        isValid: false,
        error: `Image size (${Math.round(sizeKB)}KB) exceeds maximum allowed size (${maxSizeKB}KB)`,
        sizeKB,
      };
    }

    // Check if it's a valid base64 image
    if (!base64.startsWith('data:image/')) {
      return {
        isValid: false,
        error: 'Invalid image format',
        sizeKB,
      };
    }

    return {
      isValid: true,
      sizeKB,
    };
  } catch (error) {
    return {
      isValid: false,
      error: 'Failed to validate image',
      sizeKB: 0,
    };
  }
}

/**
 * Create a thumbnail from an image
 */
export async function createThumbnail(
  imageUri: string,
  size: number = 150
): Promise<CompressionResult> {
  try {
    const result = await manipulateAsync(
      imageUri,
      [
        {
          resize: {
            width: size,
            height: size,
          },
        },
      ],
      {
        compress: 0.8,
        format: SaveFormat.JPEG,
        base64: true,
      }
    );

    return {
      uri: result.uri,
      base64: result.base64,
      width: result.width,
      height: result.height,
      size: result.base64 ? (result.base64.length * 0.75) : 0,
    };
  } catch (error) {
    console.error('Thumbnail creation failed:', error);
    throw new Error('Failed to create thumbnail');
  }
}

/**
 * Crop image to square aspect ratio
 */
export async function cropToSquare(
  imageUri: string,
  size: number = 800
): Promise<CompressionResult> {
  try {
    // First get dimensions
    const dimensions = await getImageDimensions(imageUri);
    const minDimension = Math.min(dimensions.width, dimensions.height);
    
    // Calculate crop origin to center the crop
    const originX = (dimensions.width - minDimension) / 2;
    const originY = (dimensions.height - minDimension) / 2;

    const result = await manipulateAsync(
      imageUri,
      [
        {
          crop: {
            originX,
            originY,
            width: minDimension,
            height: minDimension,
          },
        },
        {
          resize: {
            width: size,
            height: size,
          },
        },
      ],
      {
        compress: 0.8,
        format: SaveFormat.JPEG,
        base64: true,
      }
    );

    return {
      uri: result.uri,
      base64: result.base64,
      width: result.width,
      height: result.height,
      size: result.base64 ? (result.base64.length * 0.75) : 0,
    };
  } catch (error) {
    console.error('Square crop failed:', error);
    throw new Error('Failed to crop image to square');
  }
}

/**
 * Batch process images with different operations
 */
export async function batchProcessImages(
  imageUris: string[],
  operations: {
    compress?: CompressionOptions;
    thumbnail?: boolean;
    square?: boolean;
  } = {}
): Promise<{
  compressed: CompressionResult[];
  thumbnails?: CompressionResult[];
  squares?: CompressionResult[];
}> {
  try {
    const results: any = {};

    // Compress images
    if (operations.compress) {
      results.compressed = await compressMultipleImages(imageUris, operations.compress);
    }

    // Create thumbnails
    if (operations.thumbnail) {
      const thumbnailPromises = imageUris.map(uri => createThumbnail(uri));
      results.thumbnails = await Promise.all(thumbnailPromises);
    }

    // Create square crops
    if (operations.square) {
      const squarePromises = imageUris.map(uri => cropToSquare(uri));
      results.squares = await Promise.all(squarePromises);
    }

    return results;
  } catch (error) {
    console.error('Batch image processing failed:', error);
    throw new Error('Failed to process images');
  }
}
