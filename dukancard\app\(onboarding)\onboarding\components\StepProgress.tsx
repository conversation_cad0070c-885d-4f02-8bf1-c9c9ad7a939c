import React from "react";
import { Check } from "lucide-react";
import { STEP_TITLES, getStepDescription } from "../constants/onboardingSteps";
import { ExistingBusinessProfileData } from "../types/onboarding";

interface StepProgressProps {
  currentStep: number;
  existingData: ExistingBusinessProfileData | null;
  _isLoadingExistingData: boolean;
}

export function StepProgress({ currentStep, existingData, _isLoadingExistingData }: StepProgressProps) {
  return (
    <div className="mb-6 sm:mb-8">
      {/* Step indicator circles */}
      <div className="flex justify-center items-center mb-4">
        <div className="flex items-center space-x-2">
          {[1, 2, 3, 4].map((step) => (
            <div key={step} className="flex items-center">
              <div
                className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center transition-colors ${
                  step < currentStep
                    ? "bg-primary dark:bg-[var(--brand-gold)] text-white dark:text-black"
                    : step === currentStep
                    ? "bg-primary/20 dark:bg-[var(--brand-gold)]/20 text-primary dark:text-[var(--brand-gold)] border border-primary dark:border-[var(--brand-gold)]"
                    : "bg-muted dark:bg-neutral-800 text-muted-foreground dark:text-neutral-500"
                }`}
              >
                {step < currentStep ? (
                  <Check className="w-4 h-4 sm:w-5 sm:h-5" aria-label="Check" />
                ) : (
                  <span className="text-xs sm:text-sm font-medium">{step}</span>
                )}
              </div>
              {step < 4 && (
                <div data-testid="progress-line" className={`w-8 sm:w-12 h-0.5 mx-1 transition-colors ${
                  step < currentStep ? "bg-primary dark:bg-[var(--brand-gold)]" : "bg-muted dark:bg-neutral-700"
                }`} />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Current step info */}
      <div className="text-center">
        <div className="text-xs sm:text-sm text-muted-foreground dark:text-neutral-400 mb-1">
          Step {currentStep} of 4
        </div>
        <h3 className="text-sm sm:text-base font-medium text-primary dark:text-[var(--brand-gold)]">
          {STEP_TITLES[currentStep - 1]}
        </h3>
      </div>

      {/* Step description */}
      <div className="text-center mt-6 sm:mt-8">
        <p
          key={`desc-${currentStep}`}
          className="text-sm sm:text-base text-muted-foreground dark:text-neutral-400"
        >
          {getStepDescription(currentStep - 1, existingData?.hasExistingSubscription)}
        </p>


      </div>
    </div>
  );
}
