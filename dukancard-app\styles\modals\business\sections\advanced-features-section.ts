import { StyleSheet } from "react-native";
import { useTheme } from "@/src/hooks/useTheme";

export const createAdvancedFeaturesSectionStyles = (
  theme: ReturnType<typeof useTheme>
) => {
  const { isDark } = theme;
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: isDark ? "#000" : "#FFF",
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      padding: theme.spacing.md,
      paddingBottom: theme.spacing.xl,
    },
    section: {
      marginBottom: theme.spacing.lg,
    },
    sectionTitle: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
      color: isDark ? "#FFF" : "#000",
      marginBottom: theme.spacing.sm,
    },
    
    // Premium header styles
    premiumHeader: {
      alignItems: "center",
      padding: theme.spacing.lg,
      backgroundColor: isDark ? "rgba(212, 175, 55, 0.1)" : "rgba(212, 175, 55, 0.1)",
      borderRadius: theme.borderRadius.lg,
      marginBottom: theme.spacing.lg,
    },
    premiumIconContainer: {
      width: 64,
      height: 64,
      borderRadius: 32,
      backgroundColor: isDark ? "rgba(212, 175, 55, 0.2)" : "rgba(212, 175, 55, 0.2)",
      alignItems: "center",
      justifyContent: "center",
      marginBottom: theme.spacing.sm,
    },
    premiumTitle: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: "700",
      color: isDark ? "#D4AF37" : "#B8860B",
      marginBottom: theme.spacing.xs,
    },
    premiumSubtitle: {
      fontSize: theme.typography.fontSize.sm,
      color: isDark ? "#D4AF37" : "#B8860B",
      textAlign: "center",
      lineHeight: 20,
    },
    
    // Plan card styles
    planCard: {
      backgroundColor: isDark ? "rgba(255, 255, 255, 0.05)" : "rgba(0, 0, 0, 0.02)",
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      marginBottom: theme.spacing.lg,
    },
    planHeader: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      marginBottom: theme.spacing.xs,
    },
    planTitle: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
      color: isDark ? "#FFF" : "#000",
    },
    planBadge: {
      backgroundColor: isDark ? "rgba(156, 163, 175, 0.2)" : "rgba(156, 163, 175, 0.2)",
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: 2,
      borderRadius: theme.borderRadius.sm,
    },
    planBadgeText: {
      fontSize: theme.typography.fontSize.xs,
      color: "#9CA3AF",
      fontWeight: "600",
    },
    planDescription: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
      marginBottom: theme.spacing.md,
    },
    upgradeButton: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: "#D4AF37",
      paddingVertical: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
      gap: theme.spacing.xs,
    },
    upgradeButtonText: {
      color: "white",
      fontSize: theme.typography.fontSize.sm,
      fontWeight: "600",
    },
    
    // Feature card styles
    featureCard: {
      backgroundColor: isDark ? "rgba(255, 255, 255, 0.05)" : "rgba(0, 0, 0, 0.02)",
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      marginBottom: theme.spacing.sm,
    },
    featureHeader: {
      flexDirection: "row",
      alignItems: "center",
    },
    featureIconContainer: {
      width: 48,
      height: 48,
      borderRadius: 24,
      alignItems: "center",
      justifyContent: "center",
      marginRight: theme.spacing.sm,
    },
    featureInfo: {
      flex: 1,
    },
    featureTitleRow: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 2,
    },
    featureTitle: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
      color: isDark ? "#FFF" : "#000",
      marginRight: theme.spacing.xs,
    },
    premiumBadge: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: isDark ? "rgba(212, 175, 55, 0.2)" : "rgba(212, 175, 55, 0.2)",
      paddingHorizontal: theme.spacing.xs,
      paddingVertical: 2,
      borderRadius: theme.borderRadius.sm,
      gap: 2,
    },
    premiumBadgeText: {
      fontSize: theme.typography.fontSize.xs,
      color: "#D4AF37",
      fontWeight: "600",
    },
    featureDescription: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
      lineHeight: 18,
    },

    // Benefits list styles
    benefitsList: {
      gap: theme.spacing.sm,
    },
    benefitItem: {
      flexDirection: "row",
      alignItems: "flex-start",
    },
    benefitBullet: {
      fontSize: theme.typography.fontSize.base,
      marginRight: theme.spacing.sm,
      marginTop: 2,
    },
    benefitText: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
      flex: 1,
      lineHeight: 20,
    },

    // Pricing card styles
    pricingCard: {
      backgroundColor: isDark ? "rgba(212, 175, 55, 0.1)" : "rgba(212, 175, 55, 0.1)",
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing.lg,
      alignItems: "center",
      marginBottom: theme.spacing.lg,
    },
    pricingTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: "700",
      color: isDark ? "#D4AF37" : "#B8860B",
      marginBottom: theme.spacing.sm,
    },
    pricingRow: {
      flexDirection: "row",
      alignItems: "baseline",
      marginBottom: theme.spacing.xs,
    },
    pricingAmount: {
      fontSize: 32,
      fontWeight: "800",
      color: isDark ? "#D4AF37" : "#B8860B",
    },
    pricingPeriod: {
      fontSize: theme.typography.fontSize.base,
      color: isDark ? "#D4AF37" : "#B8860B",
      marginLeft: 4,
    },
    pricingDescription: {
      fontSize: theme.typography.fontSize.sm,
      color: isDark ? "#D4AF37" : "#B8860B",
      textAlign: "center",
      marginBottom: theme.spacing.md,
    },
    pricingButton: {
      backgroundColor: "#D4AF37",
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
    },
    pricingButtonText: {
      color: "white",
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
    },

    // Info box
    infoBox: {
      backgroundColor: isDark ? "rgba(59, 130, 246, 0.1)" : "rgba(59, 130, 246, 0.1)",
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      marginTop: theme.spacing.md,
    },
    infoText: {
      fontSize: theme.typography.fontSize.sm,
      color: isDark ? "#60A5FA" : "#2563EB",
      lineHeight: 20,
    },

    // Footer styles
    footer: {
      padding: theme.spacing.md,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      backgroundColor: isDark ? "#000" : "#FFF",
    },
    backButton: {
      borderRadius: theme.borderRadius.lg,
      paddingVertical: theme.spacing.md,
      alignItems: "center",
      justifyContent: "center",
      minHeight: 50,
      borderWidth: 1,
    },
    backButtonText: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
    },
  });
};
