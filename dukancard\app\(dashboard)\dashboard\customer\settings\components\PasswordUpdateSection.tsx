"use client";

import { useTransition } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import { motion } from "framer-motion";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Loader2, KeyRound, Info } from "lucide-react";
import { updateCustomerPassword } from "../actions";
import { Button } from "@/components/ui/button";
import { PasswordComplexitySchema } from "@/lib/schemas/authSchemas";

// Password schema
const PasswordSchema = z.object({
  currentPassword: z.string().min(1, "Current password is required"),
  newPassword: PasswordComplexitySchema,
  confirmPassword: z.string().min(1, "Please confirm your password"),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords do not match",
  path: ["confirmPassword"],
});

interface PasswordUpdateSectionProps {
  registrationType: 'google' | 'email' | 'phone';
}

export default function PasswordUpdateSection({
  registrationType,
}: PasswordUpdateSectionProps) {
  const isGoogleLogin = registrationType === 'google';
  const [isPending, startTransition] = useTransition();

  // Password Form
  const passwordForm = useForm<z.infer<typeof PasswordSchema>>({
    resolver: zodResolver(PasswordSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  // Handle password update
  const onPasswordSubmit = (data: z.infer<typeof PasswordSchema>) => {
    startTransition(async () => {
      try {
        // Create FormData object for the server action
        const formData = new FormData();
        formData.append('currentPassword', data.currentPassword);
        formData.append('newPassword', data.newPassword);

        // Call the server action with the required parameters
        const result = await updateCustomerPassword(
          { message: null, success: false }, // Initial state
          formData
        );

        if (result.success) {
          toast.success("Password updated successfully!");
          passwordForm.reset();
        } else {
          toast.error(result.message || "Failed to update password");

          // Set server-side field errors into react-hook-form state
          if (result.errors?.currentPassword) {
            passwordForm.setError('currentPassword', {
              type: 'server',
              message: result.errors.currentPassword.join(', ')
            });
          }

          if (result.errors?.newPassword) {
            passwordForm.setError('newPassword', {
              type: 'server',
              message: result.errors.newPassword.join(', ')
            });
          }
        }
      } catch (error) {
        toast.error("An unexpected error occurred");
        console.error(error);
      }
    });
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4, delay: 0.1 }}
      className="space-y-6"
    >
      {/* Section Header */}
      <div className="pb-6 border-b border-neutral-200/60 dark:border-neutral-700/60">
        <div className="flex items-center gap-3 mb-3">
          <div className="flex items-center justify-center w-8 h-8 rounded-lg bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20">
            <KeyRound className="w-4 h-4 text-primary" />
          </div>
          <h2 className="text-xl font-semibold text-neutral-900 dark:text-neutral-100">
            Password
          </h2>
        </div>
        <p className="text-sm text-neutral-600 dark:text-neutral-400 leading-relaxed">
          Update your password to keep your account secure.
        </p>
      </div>

      {/* Section Content */}
      <div className="space-y-6">

      {isGoogleLogin ? (
        <div className="p-4 bg-neutral-50 dark:bg-neutral-800/50 rounded-lg border border-neutral-200 dark:border-neutral-700">
          <div className="flex items-center gap-2">
            <Info className="w-4 h-4 text-amber-500" />
            <p className="text-sm text-neutral-700 dark:text-neutral-300">
              You signed up with Google. Password management is handled by your Google account.
            </p>
          </div>
        </div>
      ) : (
        <Form {...passwordForm}>
          <form onSubmit={passwordForm.handleSubmit(onPasswordSubmit)}>
            <div className="space-y-4">
              <FormField
                control={passwordForm.control}
                name="currentPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm text-neutral-700 dark:text-neutral-300">
                      Current Password
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="••••••••"
                        {...field}
                        disabled={isPending}
                        className="bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700"
                      />
                    </FormControl>
                    <FormMessage className="text-xs text-red-500" />
                  </FormItem>
                )}
              />
              <FormField
                control={passwordForm.control}
                name="newPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm text-neutral-700 dark:text-neutral-300">
                      New Password
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="••••••••"
                        {...field}
                        disabled={isPending}
                        className="bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700"
                      />
                    </FormControl>
                    <FormDescription className="text-xs text-neutral-500 dark:text-neutral-400">
                      Must contain: 6+ chars, 1 uppercase letter, 1 lowercase letter, 1 number, 1 symbol
                    </FormDescription>
                    <FormMessage className="text-xs text-red-500" />
                  </FormItem>
                )}
              />
              <FormField
                control={passwordForm.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm text-neutral-700 dark:text-neutral-300">
                      Confirm Password
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="password"
                        placeholder="••••••••"
                        {...field}
                        disabled={isPending}
                        className="bg-neutral-50 dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700"
                      />
                    </FormControl>
                    <FormMessage className="text-xs text-red-500" />
                  </FormItem>
                )}
              />
              <div className="mt-4 sm:mt-6 flex justify-end">
                  <Button
                    type="submit"
                    disabled={isPending}
                    className="bg-primary hover:bg-primary/90 text-primary-foreground"
                  >
                    {isPending && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    Change Password
                  </Button>
              </div>
            </div>
          </form>
        </Form>
      )}
      </div>
    </motion.div>
  );
}
