import React from "react";
import {
  Modal,
  View,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { ProductVariants } from "@/src/types/database";
import VariantForm from "./components/VariantForm";

interface VariantModalProps {
  visible: boolean;
  variant?: ProductVariants | null;
  onSubmit: (data: any) => Promise<void>;
  onCancel: () => void;
  loading: boolean;
}

const VariantModal: React.FC<VariantModalProps> = ({
  visible,
  variant,
  onSubmit,
  onCancel,
  loading,
}) => {
  const theme = useTheme();

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onCancel}
    >
      <SafeAreaView style={{ flex: 1, backgroundColor: theme.colors.background }}>
        <KeyboardAvoidingView
          style={{ flex: 1 }}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 20}
        >
          <VariantForm
            variant={variant}
            onSubmit={onSubmit}
            onCancel={onCancel}
            loading={loading}
          />
        </KeyboardAvoidingView>
      </SafeAreaView>
    </Modal>
  );
};

export default VariantModal;
