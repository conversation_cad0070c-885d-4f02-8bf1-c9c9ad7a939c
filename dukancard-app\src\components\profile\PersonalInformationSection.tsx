import React from "react";
import { View, Text, TextInput, StyleSheet } from "react-native";
import { Control, Controller, FieldErrors } from "react-hook-form";
import { FormData } from "@/src/types/profile";

interface PersonalInformationSectionProps {
  control: Control<FormData>;
  textColor: string;
  borderColor: string;
  styles: any; // TODO: Define a proper style type
}

const PersonalInformationSection: React.FC<PersonalInformationSectionProps> = ({
  control,
  textColor,
  borderColor,
  styles,
}) => {
  return (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: textColor }]}>
        Personal Information
      </Text>

      <View style={styles.inputGroup}>
        <Text style={[styles.label, { color: textColor }]}>
          Full Name *
        </Text>
        <Controller
          control={control}
          name="name"
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <>
              <TextInput
                style={[
                  styles.input,
                  { borderColor, color: textColor },
                  error && styles.inputError,
                ]}
                value={value || ""}
                onChangeText={onChange}
                placeholder="Enter your full name"
                placeholderTextColor={textColor}
              />
              {error && (
                <Text style={styles.errorText}>{error.message}</Text>
              )}
            </>
          )}
        />
      </View>
    </View>
  );
};

export default PersonalInformationSection;
