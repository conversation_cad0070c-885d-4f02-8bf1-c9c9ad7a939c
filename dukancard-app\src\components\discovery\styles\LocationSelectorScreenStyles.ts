/**
 * Styles for LocationSelectorScreen Component
 */

import { StyleSheet } from "react-native";

export const createLocationSelectorScreenStyles = () =>
  StyleSheet.create({
    container: {
      flex: 1,
    },
    header: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: 16,
      paddingVertical: 12,
      borderBottomWidth: 1,
    },
    closeButton: {
      padding: 8,
      marginLeft: -8,
    },
    headerTitle: {
      fontSize: 18,
      fontWeight: "600",
      flex: 1,
      textAlign: "center",
      marginHorizontal: 16,
    },
    headerCenter: {
      flex: 1,
      alignItems: "center",
      justifyContent: "center",
    },
    saveButton: {
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 8,
    },
    saveButtonText: {
      color: "#ffffff",
      fontSize: 16,
      fontWeight: "600",
    },
    content: {
      flex: 1,
      padding: 16,
    },
    formContainer: {
      // Remove card styling - just a simple container
    },
    currentLocationButton: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      padding: 12,
      borderWidth: 1,
      borderRadius: 8,
      marginBottom: 24,
      gap: 8,
    },
    currentLocationText: {
      fontSize: 16,
      fontWeight: "500",
    },
    inputContainer: {
      marginBottom: 20,
    },
    label: {
      fontSize: 16,
      fontWeight: "600",
      marginBottom: 8,
    },
    input: {
      borderWidth: 1,
      borderRadius: 8,
      padding: 12,
      fontSize: 16,
      minHeight: 48,
    },
    cityInputContainer: {
      position: "relative",
    },
    cityInput: {
      paddingRight: 40,
    },
    cityLoadingIndicator: {
      position: "absolute",
      right: 12,
      top: 12,
    },
    citySuggestionsDropdown: {
      borderWidth: 1,
      borderRadius: 8,
      marginTop: 4,
      maxHeight: 200,
    },
    citySuggestionItem: {
      paddingHorizontal: 12,
      paddingVertical: 16, // Increased vertical padding for better spacing between border and text
      borderBottomWidth: 1,
      borderBottomColor: "rgba(128, 128, 128, 0.2)", // Faint color that works in both light and dark mode
    },
    citySuggestionText: {
      fontSize: 16,
    },
    pincodeContainer: {
      position: "relative",
    },
    pincodeInput: {
      paddingRight: 40,
    },
    loadingIndicator: {
      position: "absolute",
      right: 12,
      top: 12,
    },
    localityPicker: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
    },
    localityText: {
      fontSize: 16,
      flex: 1,
    },
    localityDropdown: {
      borderWidth: 1,
      borderRadius: 8,
      marginTop: 4,
      maxHeight: 200,
    },
    localityOption: {
      padding: 12,
      borderBottomWidth: 1,
      borderBottomColor: "#f0f0f0",
    },
    localityOptionText: {
      fontSize: 16,
    },
    errorText: {
      fontSize: 12,
      marginTop: 4,
      fontWeight: "500",
    },
    clearButton: {
      position: "absolute",
      right: 12,
      top: "50%",
      transform: [{ translateY: -8 }],
      padding: 4,
      zIndex: 1,
    },
  });
