import { renderHook, waitFor, act } from '@testing-library/react';
import { useOnboardingForm } from '@/app/(onboarding)/onboarding/hooks/useOnboardingForm';
import { useSearchParams } from 'next/navigation';
import { toast } from 'sonner';
import { createBusinessProfile } from '@/app/(onboarding)/onboarding/actions';
import { onboardingPlans } from '@/lib/PricingPlans';
import { formSchema } from '@/app/(onboarding)/onboarding/types/onboarding';
import { stepFields } from '@/app/(onboarding)/onboarding/constants/onboardingSteps';
import { cleanPhoneFromAuth } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';

// Mock external modules and hooks
jest.mock('next/navigation', () => ({
  useSearchParams: jest.fn(() => new URLSearchParams()),
  useRouter: jest.fn(() => ({ push: jest.fn() })),
}));
jest.mock('sonner', () => ({
  toast: {
    error: jest.fn(),
    success: jest.fn(),
  },
}));
jest.mock('@/app/(onboarding)/onboarding/actions', () => ({
  createBusinessProfile: jest.fn(),
}));
jest.mock('@/lib/PricingPlans', () => ({
  onboardingPlans: [
    { id: 'free', name: 'Free Plan', price: 'Free', available: true, recommended: false },
    { id: 'pro', name: 'Pro Plan', price: '$10/month', available: true, recommended: true },
  ],
}));
jest.mock('@/app/(onboarding)/onboarding/types/onboarding', () => ({
  formSchema: jest.fn(), // Mock formSchema
}));
jest.mock('@/app/(onboarding)/onboarding/constants/onboardingSteps', () => ({
  stepFields: [
    ['businessName', 'email'],
    ['memberName', 'title', 'phone', 'businessCategory', 'businessSlug'],
    ['addressLine', 'pincode', 'city', 'state', 'locality', 'businessStatus'],
    ['planId'],
  ],
}));
jest.mock('@/lib/utils', () => ({
  cleanPhoneFromAuth: jest.fn((phone) => phone),
}));
jest.mock('@hookform/resolvers/zod', () => ({
  zodResolver: jest.fn(() => jest.fn()),
}));

// Mock react-hook-form's useForm to control its behavior
jest.mock('react-hook-form', () => ({
  useForm: jest.fn(() => {
    const defaultValues = {
      businessName: "",
      email: "",
      memberName: "",
      title: "",
      phone: "",
      businessCategory: "",
      businessSlug: "",
      addressLine: "",
      pincode: "",
      city: "",
      state: "",
      locality: "",
      businessStatus: "online",
      planId: "",
    };
    return {
      control: {},
      handleSubmit: jest.fn((cb) => cb), // Mock handleSubmit to directly call the callback
      register: jest.fn(),
      setValue: jest.fn(),
      formState: { errors: {} },
      trigger: jest.fn(() => true), // Default to valid
      setError: jest.fn(),
      clearErrors: jest.fn(),
      getValues: jest.fn(() => defaultValues), // Return defaultValues
    };
  }),
}));

describe('useOnboardingForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset localStorage mock before each test
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(),
        setItem: jest.fn(),
        removeItem: jest.fn(),
      },
      writable: true,
    });
  });

  it('should initialize with default form values', () => {
    const { result } = renderHook(() => useOnboardingForm({
      user: null,
      selectedPlan: null,
    }));

    expect(result.current.form.getValues()).toEqual({
      businessName: "",
      email: "",
      memberName: "",
      title: "",
      phone: "",
      businessCategory: "",
      businessSlug: "",
      addressLine: "",
      pincode: "",
      city: "",
      state: "",
      locality: "",
      businessStatus: "online",
      planId: "",
    });
    expect(result.current.currentStep).toBe(1);
    expect(result.current.isSubmitting).toBe(false);
  });

  it('should handle redirectSlug and message from searchParams', async () => {
    const { result, rerender } = renderHook(() => useOnboardingForm({
      user: null,
      selectedPlan: null,
    }));

    act(() => {
      (useSearchParams as jest.Mock).mockReturnValue(new URLSearchParams('redirect=card-slug&message=welcome'));
      rerender(); // Trigger a re-render after updating the mock
    });

    await waitFor(() => {
      expect(result.current.cardRedirect).toBe('card-slug');
      expect(result.current.messageParam).toBe('welcome');
    });
  });

  it('should handle redirectSlug and message from localStorage', () => {
    (useSearchParams as jest.Mock).mockReturnValue(new URLSearchParams());
    localStorage.getItem.mockImplementation((key: string) => {
      if (key === 'postOnboardingRedirect') return 'stored-redirect';
      if (key === 'postOnboardingMessage') return 'stored-message';
      return null;
    });

    const { result } = renderHook(() => useOnboardingForm({
      user: null,
      selectedPlan: null,
    }));

    expect(result.current.cardRedirect).toBe('stored-redirect');
    expect(result.current.messageParam).toBe('stored-message');
    expect(localStorage.removeItem).toHaveBeenCalledWith('postOnboardingRedirect');
    expect(localStorage.removeItem).toHaveBeenCalledWith('postOnboardingMessage');
  });

  it('should pre-fill email from user data', () => {
    const mockUser = { email: '<EMAIL>' } as any;
    const { result } = renderHook(() => useOnboardingForm({
      user: mockUser,
      selectedPlan: null,
    }));

    expect(result.current.form.setValue).toHaveBeenCalledWith('email', '<EMAIL>', { shouldValidate: true });
  });

  it('should pre-fill memberName from user data', () => {
    const mockUser = { user_metadata: { full_name: 'John Doe' } } as any;
    const { result } = renderHook(() => useOnboardingForm({
      user: mockUser,
      selectedPlan: null,
    }));

    expect(result.current.form.setValue).toHaveBeenCalledWith('memberName', 'John Doe', { shouldValidate: true });
  });

  it('should pre-fill phone from user data', () => {
    const mockUser = { phone: '+919876543210' } as any;
    (cleanPhoneFromAuth as jest.Mock).mockReturnValueOnce('9876543210');
    const { result } = renderHook(() => useOnboardingForm({
      user: mockUser,
      selectedPlan: null,
    }));

    expect(result.current.form.setValue).toHaveBeenCalledWith('phone', '9876543210', { shouldValidate: true });
  });

  it('should navigate to the next step on handleNextStep if validation passes', async () => {
    const { result } = renderHook(() => useOnboardingForm({
      user: null,
      selectedPlan: null,
    }));

    act(() => {
      result.current.handleNextStep();
    });

    await waitFor(() => {
      expect(result.current.currentStep).toBe(2);
      expect(result.current.form.trigger).toHaveBeenCalledWith(stepFields[0]);
    });
  });

  it('should not navigate to the next step if validation fails', async () => {
    const { result } = renderHook(() => useOnboardingForm({
      user: null,
      selectedPlan: null,
    }));
    result.current.form.trigger.mockResolvedValueOnce(false); // Simulate validation failure

    act(() => {
      result.current.handleNextStep();
    });

    await waitFor(() => {
      expect(result.current.currentStep).toBe(1); // Should remain on step 1
    });
  });

  it('should navigate to the previous step on handlePreviousStep', () => {
    const { result } = renderHook(() => useOnboardingForm({
      user: null,
      selectedPlan: null,
    }));

    act(() => {
      result.current.setCurrentStep(2); // Manually set to step 2 for testing previous
    });

    act(() => {
      result.current.handlePreviousStep();
    });

    expect(result.current.currentStep).toBe(1);
  });

  it('should call createBusinessProfile on onSubmitHandler if submit is intended and plan is selected', async () => {
    const mockSelectedPlan = { id: 'free' } as any;
    const { result } = renderHook(() => useOnboardingForm({
      user: null,
      selectedPlan: mockSelectedPlan,
    }));

    result.current.setIsSubmitIntended(true);
    (createBusinessProfile as jest.Mock).mockResolvedValueOnce({}); // Mock successful submission

    await act(async () => {
      await result.current.onSubmitHandler({});
    });

    expect(createBusinessProfile).toHaveBeenCalledTimes(1);
    expect(toast.success).toHaveBeenCalledWith('Onboarding complete! Welcome aboard.');
  });

  it('should show error toast and set field errors on onSubmitHandler failure', async () => {
    const mockSelectedPlan = { id: 'free' } as any;
    const { result } = renderHook(() => useOnboardingForm({
      user: null,
      selectedPlan: mockSelectedPlan,
    }));

    result.current.setIsSubmitIntended(true);
    (createBusinessProfile as jest.Mock).mockResolvedValueOnce({
      error: 'Submission failed',
      fieldErrors: { businessName: ['Name is required'] },
    });

    await act(async () => {
      await result.current.onSubmitHandler({});
    });

    expect(toast.error).toHaveBeenCalledWith('Onboarding failed: Submission failed');
    expect(result.current.form.setError).toHaveBeenCalledWith('businessName', { message: 'Name is required' });
  });

  it('should set currentStep to 2 if businessSlug error occurs on submission', async () => {
    const mockSelectedPlan = { id: 'free' } as any;
    const { result } = renderHook(() => useOnboardingForm({
      user: null,
      selectedPlan: mockSelectedPlan,
    }));

    result.current.setIsSubmitIntended(true);
    (createBusinessProfile as jest.Mock).mockResolvedValueOnce({
      error: 'Slug taken',
      fieldErrors: { businessSlug: ['This URL slug is already taken.'] },
    });

    await act(async () => {
      await result.current.onSubmitHandler({});
    });

    expect(result.current.currentStep).toBe(2);
  });

  it('should not submit if isSubmitIntended is false', async () => {
    const mockSelectedPlan = { id: 'free' } as any;
    const { result } = renderHook(() => useOnboardingForm({
      user: null,
      selectedPlan: mockSelectedPlan,
    }));

    // isSubmitIntended is false by default
    await act(async () => {
      await result.current.onSubmitHandler({});
    });

    expect(createBusinessProfile).not.toHaveBeenCalled();
    expect(toast.error).not.toHaveBeenCalled();
  });

  it('should not submit if no plan is selected', async () => {
    const { result } = renderHook(() => useOnboardingForm({
      user: null,
      selectedPlan: null,
    }));

    result.current.setIsSubmitIntended(true);

    await act(async () => {
      await result.current.onSubmitHandler({});
    });

    expect(createBusinessProfile).not.toHaveBeenCalled();
    expect(toast.error).toHaveBeenCalledWith('Please select a plan.');
  });

  it('should handle slugAvailable check on handleNextStep for step 2', async () => {
    const { result } = renderHook(() => useOnboardingForm({
      user: null,
      selectedPlan: null,
      slugAvailable: false, // Simulate slug not available
    }));

    act(() => {
      result.current.setCurrentStep(2); // Set to step 2
    });

    act(() => {
      result.current.handleNextStep();
    });

    await waitFor(() => {
      expect(result.current.form.setError).toHaveBeenCalledWith('businessSlug', {
        type: 'manual',
        message: 'This URL slug is already taken.',
      });
      expect(result.current.currentStep).toBe(2); // Should remain on step 2
    });
  });

  it('should pre-fill memberName from user_metadata.display_name', () => {
    const mockUser = { user_metadata: { display_name: 'Display Name' } } as any;
    const { result } = renderHook(() => useOnboardingForm({
      user: mockUser,
      selectedPlan: null,
    }));

    expect(result.current.form.setValue).toHaveBeenCalledWith('memberName', 'Display Name', { shouldValidate: true });
  });
});