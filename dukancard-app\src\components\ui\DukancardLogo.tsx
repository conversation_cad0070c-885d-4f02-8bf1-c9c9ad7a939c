import { useColorScheme } from '@/src/hooks/useColorScheme';
import React from 'react';
import { Text, View, ViewStyle } from 'react-native';

interface DukancardLogoProps {
  size?: 'small' | 'medium' | 'large' | 'auth' | 'hero';
  showText?: boolean;
  showTagline?: boolean;
  style?: ViewStyle;
}

export function DukancardLogo({
  size = 'medium',
  showText = true,
  showTagline = true,
  style
}: DukancardLogoProps) {
  const colorScheme = useColorScheme();

  const logoSizes = {
    small: { fontSize: 18, taglineSize: 10, marginBottom: 8, letterSpacing: 0.5 },
    medium: { fontSize: 24, taglineSize: 12, marginBottom: 16, letterSpacing: 1 },
    large: { fontSize: 32, taglineSize: 14, marginBottom: 20, letterSpacing: 1.2 },
    auth: { fontSize: 28, taglineSize: 12, marginBottom: 12, letterSpacing: 1 },
    hero: { fontSize: 36, taglineSize: 14, marginBottom: 24, letterSpacing: 1.5 },
  };

  const currentLogoSize = logoSizes[size];

  return (
    <View style={[{ alignItems: 'center' }, style]}>
      {/* Brand Text */}
      {showText && (
        <View style={{ alignItems: 'center' }}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 6 }}>
            <Text
              style={{
                color: '#C29D5B',
                fontWeight: '900',
                fontSize: currentLogoSize.fontSize,
                letterSpacing: currentLogoSize.letterSpacing
              }}
            >
              Dukan
            </Text>
            <Text
              style={{
                color: colorScheme === 'dark' ? '#ffffff' : '#000000',
                fontWeight: '900',
                fontSize: currentLogoSize.fontSize,
                letterSpacing: currentLogoSize.letterSpacing
              }}
            >
              card
            </Text>
          </View>
          {showTagline && (
            <Text
              style={{
                fontSize: currentLogoSize.taglineSize,
                fontWeight: '600',
                letterSpacing: 2,
                textTransform: 'uppercase',
                color: colorScheme === 'dark' ? '#9ca3af' : '#6b7280',
                marginBottom: currentLogoSize.marginBottom
              }}
            >
              Connect • Discover • Grow
            </Text>
          )}
        </View>
      )}
    </View>
  );
}
