import { StyleSheet } from "react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { responsiveFontSize } from "@/lib/theme/colors";

export const createManageProductsModalStyles = (
  theme: ReturnType<typeof useTheme>
) => {
  const { isDark } = theme;
  return StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: isDark ? "#000" : "#FFF",
    },
    keyboardAvoidingView: {
      flex: 1,
    },
    modalContainer: {
      flex: 1,
      backgroundColor: isDark ? "#000" : "#FFF",
    },
    header: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: "600",
      color: isDark ? "#FFF" : "#000",
    },
    closeButton: {
      padding: theme.spacing.xs,
    },
    backButton: {
      padding: theme.spacing.xs,
    },
    container: {
      flex: 1,
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      paddingBottom: theme.spacing.xxxl,
    },
    contentContainer: {
      flex: 1,
    },
    searchContainer: {
      paddingHorizontal: theme.spacing.md,
      paddingTop: theme.spacing.md,
      marginBottom: theme.spacing.md,
    },
    searchInputContainer: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: theme.colors.background,
      borderRadius: theme.borderRadius.md,
      paddingHorizontal: theme.spacing.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      height: 48,
    },
    searchInput: {
      flex: 1,
      marginLeft: theme.spacing.sm,
      fontSize: theme.typography.fontSize.base,
      color: theme.colors.foreground,
    },
    clearSearchButton: {
      padding: theme.spacing.xs,
    },
    searchIconButton: {
      padding: theme.spacing.xs,
    },
    actionsContainer: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingHorizontal: theme.spacing.md,
      marginBottom: theme.spacing.md,
      gap: theme.spacing.sm,
    },
    sortButton: {
      width: 44,
      height: 44,
      borderRadius: theme.borderRadius.lg,
      borderWidth: 1,
      borderColor: theme.colors.border,
      backgroundColor: theme.colors.card,
      alignItems: "center",
      justifyContent: "center",
    },
    sortButtonText: {
      fontSize: theme.typography.fontSize.sm,
      color: "#C29D5B",
      fontWeight: "600",
    },
    addButton: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: theme.colors.foreground,
      paddingVertical: theme.spacing.sm,
      paddingHorizontal: theme.spacing.lg,
      borderRadius: theme.borderRadius.lg,
      justifyContent: "center",
      gap: theme.spacing.xs,
      minHeight: 44,
      flex: 1,
      shadowColor: theme.colors.primary,
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.3,
      shadowRadius: 4,
      elevation: 4,
    },
    addButtonText: {
      color: theme.colors.background,
      fontSize: theme.typography.fontSize.sm,
      fontWeight: "600",
    },
    listContainer: {
      flex: 1,
    },
    listContentContainer: {
      paddingBottom: theme.spacing.lg,
    },
    productCard: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      minHeight: 72,
      backgroundColor: "transparent",
    },
    productImageContainer: {
      marginRight: theme.spacing.sm,
    },
    productImage: {
      width: 50,
      height: 50,
      borderRadius: 25,
    },
    placeholderImage: {
      width: 50,
      height: 50,
      borderRadius: 25,
      backgroundColor: theme.colors.muted,
      justifyContent: "center",
      alignItems: "center",
    },
    productInfo: {
      flex: 1,
      marginRight: theme.spacing.sm,
    },
    productName: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
      color: theme.colors.foreground,
      marginBottom: 4,
    },
    productDescription: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.mutedForeground,
      marginBottom: 6,
    },
    priceContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 4,
    },
    price: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
      color: theme.colors.foreground,
    },
    discountedPrice: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
      color: theme.colors.foreground,
      marginRight: theme.spacing.sm,
    },
    originalPrice: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.mutedForeground,
      textDecorationLine: "line-through",
    },
    productMeta: {
      flexDirection: "row",
      gap: theme.spacing.sm,
    },
    productType: {
      fontSize: theme.typography.fontSize.xs,
      color: theme.colors.mutedForeground,
      backgroundColor: theme.colors.muted,
      paddingHorizontal: theme.spacing.xs,
      paddingVertical: 2,
      borderRadius: theme.borderRadius.sm,
      fontWeight: "500",
    },
    productStatus: {
      fontSize: theme.typography.fontSize.xs,
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: 4,
      borderRadius: theme.borderRadius.md,
      fontWeight: "600",
      overflow: "hidden",
    },
    productStatusAvailable: {
      color: "#22C55E",
      backgroundColor: isDark ? "#22C55E20" : "#22C55E15",
    },
    productStatusUnavailable: {
      color: "#EF4444",
      backgroundColor: isDark ? "#EF444420" : "#EF444415",
    },
    productActions: {
      flexDirection: "row",
      justifyContent: "flex-end",
      alignItems: "flex-end",
      gap: theme.spacing.sm,
      position: "absolute",
      bottom: theme.spacing.sm,
      right: theme.spacing.md,
    },

    editButton: {
      backgroundColor: theme.colors.card,
      borderColor: theme.colors.border,
    },
    deleteButton: {
      backgroundColor: isDark ? "#EF444420" : "#EF444415",
      borderColor: "#EF4444",
    },
    footerLoadingContainer: {
      paddingVertical: theme.spacing.lg,
      paddingBottom: theme.spacing.xl,
      alignItems: "center",
      justifyContent: "center",
    },
    emptyStateContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      paddingVertical: theme.spacing.xxxl,
      paddingHorizontal: theme.spacing.lg,
    },
    emptyStateIcon: {
      marginBottom: theme.spacing.lg,
      opacity: 0.6,
    },

    emptyStateMessage: {
      fontSize: theme.typography.fontSize.base,
      color: theme.colors.muted,
      textAlign: "center",
      lineHeight: 22,
    },

    // Form styles
    sectionContainer: {
      marginBottom: theme.spacing.lg,
    },
    inputContainer: {
      marginBottom: theme.spacing.md,
    },
    inputLabel: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: "500",
      color: isDark ? "#FFF" : "#000",
      marginBottom: theme.spacing.xs,
    },
    input: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      fontSize: theme.typography.fontSize.base,
      color: isDark ? "#FFF" : "#000",
      backgroundColor: isDark ? theme.colors.input : theme.colors.background,
    },
    inputError: {
      borderColor: theme.colors.destructive,
    },
    textArea: {
      height: 100,
      textAlignVertical: "top",
    },
    errorText: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.destructive,
      marginTop: theme.spacing.xs,
    },

    addImageButton: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.lg,
      borderWidth: 2,
      borderColor: theme.colors.border,
      borderStyle: "dashed",
      borderRadius: theme.borderRadius.md,
      marginBottom: theme.spacing.md,
    },
    addImageButtonText: {
      fontSize: theme.typography.fontSize.base,
      color: theme.colors.primary,
      marginLeft: theme.spacing.sm,
    },
    imageGrid: {
      flexDirection: "row",
      flexWrap: "wrap",
      gap: theme.spacing.sm,
    },
    imageContainer: {
      position: "relative",
      width: 100,
      height: 100,
    },
    imagePreview: {
      width: "100%",
      height: "100%",
      borderRadius: theme.borderRadius.md,
    },
    removeImageButton: {
      position: "absolute",
      top: -8,
      right: -8,
      backgroundColor: theme.colors.destructive,
      borderRadius: 12,
      width: 24,
      height: 24,
      alignItems: "center",
      justifyContent: "center",
    },
    featuredBadge: {
      position: "absolute",
      bottom: 4,
      left: 4,
      backgroundColor: theme.colors.primary,
      paddingHorizontal: theme.spacing.xs,
      paddingVertical: 2,
      borderRadius: theme.borderRadius.sm,
    },
    featuredBadgeText: {
      fontSize: theme.typography.fontSize.xs,
      color: theme.colors.primaryForeground,
      fontWeight: "600",
    },
    setFeaturedButton: {
      position: "absolute",
      bottom: 4,
      right: 4,
      backgroundColor: isDark ? "rgba(0,0,0,0.7)" : "rgba(255,255,255,0.9)",
      paddingHorizontal: theme.spacing.xs,
      paddingVertical: 2,
      borderRadius: theme.borderRadius.sm,
    },
    setFeaturedButtonText: {
      fontSize: theme.typography.fontSize.xs,
      color: theme.colors.primary,
      fontWeight: "500",
    },
    switchContainer: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingVertical: theme.spacing.sm,
      marginTop: 2,
    },

    buttonContainer: {
      flexDirection: "row",
      gap: theme.spacing.md,
      paddingTop: theme.spacing.lg,
      marginTop: theme.spacing.lg,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    stickyButtonContainer: {
      flexDirection: "row",
      gap: theme.spacing.md,
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.md,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      backgroundColor: isDark ? "#000" : "#FFF",
    },
    cancelButton: {
      flex: 1,
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.lg,
      borderRadius: theme.borderRadius.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      backgroundColor: isDark ? theme.colors.input : theme.colors.card,
      alignItems: "center",
    },
    cancelButtonText: {
      fontSize: theme.typography.fontSize.base,
      color: isDark ? "#FFF" : "#000",
      fontWeight: "500",
    },
    submitButton: {
      flex: 1,
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.lg,
      borderRadius: theme.borderRadius.md,
      backgroundColor: isDark ? "#FFFFFF" : "#000000",
      alignItems: "center",
    },
    submitButtonDisabled: {
      opacity: 0.6,
    },
    submitButtonText: {
      fontSize: theme.typography.fontSize.base,
      color: isDark ? "#000000" : "#FFFFFF",
      fontWeight: "600",
    },
    separator: {
      height: 1,
      backgroundColor: theme.colors.border,
      marginLeft: 62, // Align with text content (image width + margin)
    },

    // New styles for enhanced UI
    section: {
      marginBottom: theme.spacing.xl,
      paddingHorizontal: theme.spacing.md,
    },
    sectionTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: "600",
      color: theme.colors.foreground,
      marginBottom: theme.spacing.xs,
    },
    sectionDescription: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.mutedForeground,
      marginBottom: theme.spacing.md,
      lineHeight: 20,
    },
    sectionHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: theme.spacing.md,
    },
    sectionIconContainer: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: theme.colors.primary + '20',
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: theme.spacing.md,
    },
    sectionTitleContainer: {
      flex: 1,
    },
    currencyIcon: {
      fontSize: 18,
      fontWeight: '700',
      color: theme.colors.primary,
    },

    // Enhanced Segmented Control
    enhancedSegmentedControl: {
      gap: theme.spacing.sm,
    },
    enhancedSegmentButton: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: theme.spacing.md,
      borderRadius: theme.borderRadius.md,
      borderWidth: 2,
      borderColor: theme.colors.border,
      backgroundColor: theme.colors.card,
    },
    enhancedSegmentButtonActive: {
      borderColor: isDark ? "#FFFFFF" : "#000000",
      backgroundColor: isDark ? "#FFFFFF" : "#000000",
    },
    segmentIconContainer: {
      marginRight: theme.spacing.sm,
    },
    segmentTextContainer: {
      flex: 1,
    },
    enhancedSegmentButtonText: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: '600',
      color: theme.colors.foreground,
    },
    enhancedSegmentButtonTextActive: {
      color: isDark ? "#000000" : "#FFFFFF",
    },
    segmentButtonSubtext: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.mutedForeground,
      marginTop: 2,
    },
    segmentButtonSubtextActive: {
      color: isDark ? "#00000080" : "#FFFFFF80",
    },
    selectedIndicator: {
      marginLeft: theme.spacing.sm,
    },
    selectedDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      backgroundColor: isDark ? "#000000" : "#FFFFFF",
    },

    // Enhanced Image Styles
    enhancedAddImageButton: {
      backgroundColor: theme.colors.card,
      borderWidth: 2,
      borderColor: theme.colors.primary,
      borderStyle: 'dashed',
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.lg,
      marginBottom: theme.spacing.md,
    },
    addImageButtonDisabled: {
      borderColor: theme.colors.muted,
      backgroundColor: theme.colors.muted,
    },
    addImageButtonContent: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    addImageIconContainer: {
      marginRight: theme.spacing.md,
    },
    addImageTextContainer: {
      flex: 1,
    },
    enhancedAddImageButtonText: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: '600',
      color: theme.colors.primary,
      marginBottom: 4,
    },
    addImageButtonTextDisabled: {
      color: theme.colors.mutedForeground,
    },
    addImageButtonSubtext: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.mutedForeground,
    },
    enhancedImageSection: {
      marginTop: theme.spacing.md,
    },
    imageGridHeader: {
      marginBottom: theme.spacing.md,
    },
    imageGridTitle: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: '600',
      color: theme.colors.foreground,
      marginBottom: 4,
    },
    imageGridSubtitle: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.mutedForeground,
    },
    enhancedImageGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      gap: theme.spacing.md,
    },
    enhancedImageContainer: {
      position: 'relative',
      width: 120,
      height: 120,
      borderRadius: theme.borderRadius.md,
      overflow: 'hidden',
      backgroundColor: theme.colors.muted,
    },
    enhancedImagePreview: {
      width: '100%',
      height: '100%',
      borderRadius: theme.borderRadius.md,
    },
    enhancedRemoveImageButton: {
      position: 'absolute',
      top: -8,
      right: -8,
      backgroundColor: theme.colors.destructive,
      borderRadius: 16,
      width: 32,
      height: 32,
      alignItems: 'center',
      justifyContent: 'center',
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.25,
      shadowRadius: 4,
      elevation: 5,
    },
    imageIndexBadge: {
      position: 'absolute',
      top: 8,
      left: 8,
      backgroundColor: isDark ? 'rgba(0,0,0,0.8)' : 'rgba(255,255,255,0.9)',
      borderRadius: 12,
      width: 24,
      height: 24,
      alignItems: 'center',
      justifyContent: 'center',
    },
    imageIndexText: {
      fontSize: theme.typography.fontSize.xs,
      fontWeight: '700',
      color: theme.colors.foreground,
    },
    enhancedFeaturedBadge: {
      position: 'absolute',
      bottom: 8,
      left: 8,
      right: 8,
      backgroundColor: theme.colors.primary,
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: 4,
      borderRadius: theme.borderRadius.sm,
      alignItems: 'center',
    },
    enhancedFeaturedBadgeText: {
      fontSize: theme.typography.fontSize.xs,
      color: theme.colors.primaryForeground,
      fontWeight: '700',
    },
    enhancedSetFeaturedButton: {
      position: 'absolute',
      bottom: 8,
      left: 8,
      right: 8,
      backgroundColor: isDark ? 'rgba(0,0,0,0.8)' : 'rgba(255,255,255,0.95)',
      paddingHorizontal: theme.spacing.sm,
      paddingVertical: 4,
      borderRadius: theme.borderRadius.sm,
      alignItems: 'center',
    },
    enhancedSetFeaturedButtonText: {
      fontSize: theme.typography.fontSize.xs,
      color: theme.colors.primary,
      fontWeight: '600',
    },

    // Enhanced Plan Limit Styles
    enhancedPlanLimitContainer: {
      backgroundColor: theme.colors.primary + '15',
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.md,
      borderWidth: 1,
      borderColor: theme.colors.primary + '30',
    },
    planLimitHeader: {
      flexDirection: 'row',
      alignItems: 'flex-start',
    },
    planLimitIconContainer: {
      marginRight: theme.spacing.sm,
      marginTop: 2,
    },
    planLimitTextContainer: {
      flex: 1,
    },
    enhancedPlanLimitTitle: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: '600',
      color: theme.colors.primary,
      marginBottom: 4,
    },
    enhancedPlanLimitText: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.foreground,
    },
    planLimitWarningContainer: {
      marginTop: theme.spacing.sm,
      padding: theme.spacing.sm,
      backgroundColor: theme.colors.destructive + '20',
      borderRadius: theme.borderRadius.sm,
      borderWidth: 1,
      borderColor: theme.colors.destructive + '40',
    },
    enhancedPlanLimitWarning: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.destructive,
      fontWeight: '500',
      lineHeight: 18,
    },

    // Enhanced Availability Styles
    enhancedAvailabilityContainer: {
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    enhancedSwitchRow: {
      flexDirection: 'row',
      alignItems: 'flex-start',
    },
    availabilityIconContainer: {
      marginRight: theme.spacing.sm,
      marginTop: 4,
    },
    availabilityStatusDot: {
      width: 12,
      height: 12,
      borderRadius: 6,
    },
    enhancedSwitchLabelContainer: {
      flex: 1,
      marginRight: theme.spacing.sm,
    },
    enhancedSwitchLabel: {
      fontSize: theme.typography.fontSize.base,
      color: theme.colors.foreground,
      fontWeight: '600',
      marginBottom: 4,
    },
    availabilityDescription: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.mutedForeground,
      lineHeight: 18,
    },
    enhancedSwitchSubLabel: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.destructive,
      marginTop: 4,
      fontWeight: '500',
    },

    // Enhanced Variant Styles
    enhancedAddVariantButton: {
      backgroundColor: isDark ? "#FFFFFF" : "#000000",
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.md,
      shadowColor: isDark ? "#FFFFFF" : "#000000",
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 3,
    },
    addVariantButtonDisabled: {
      backgroundColor: theme.colors.muted,
      shadowOpacity: 0,
      elevation: 0,
    },
    addVariantButtonContent: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    addVariantIconContainer: {
      marginRight: theme.spacing.md,
    },
    addVariantTextContainer: {
      flex: 1,
    },
    enhancedAddVariantButtonText: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: '600',
      color: isDark ? "#000000" : "#FFFFFF",
      marginBottom: 4,
    },
    addVariantButtonSubtext: {
      fontSize: theme.typography.fontSize.sm,
      color: isDark ? "#00000080" : "#FFFFFF80",
    },
    addVariantButtonTextDisabled: {
      color: theme.colors.mutedForeground,
    },

    // Enhanced Variant Modal Styles
    variantModalContainer: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    variantModalHeader: {
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.md,
      backgroundColor: theme.colors.card,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    variantModalTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: '700',
      color: theme.colors.foreground,
    },
    variantScrollView: {
      flex: 1,
    },
    variantScrollContent: {
      paddingBottom: theme.spacing.xxxl,
    },
    variantFormContainer: {
      paddingHorizontal: theme.spacing.lg,
    },

    // Variant Section Styles
    variantSection: {
      marginBottom: theme.spacing.xl,
    },
    variantSectionHeader: {
      flexDirection: 'row',
      alignItems: 'flex-start',
      marginBottom: theme.spacing.lg,
      paddingBottom: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    variantSectionIconContainer: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: theme.colors.primary + '20',
      alignItems: 'center',
      justifyContent: 'center',
      marginRight: theme.spacing.md,
    },
    variantSectionTitleContainer: {
      flex: 1,
    },
    variantSectionTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: '700',
      color: theme.colors.foreground,
      marginBottom: theme.spacing.xs,
    },
    variantSectionDescription: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.mutedForeground,
      lineHeight: 20,
    },

    // Variant Input Styles
    variantInputContainer: {
      marginBottom: theme.spacing.lg,
    },
    variantInputLabel: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: '600',
      color: theme.colors.foreground,
      marginBottom: theme.spacing.sm,
    },
    requiredIndicator: {
      color: theme.colors.destructive,
      fontWeight: '600',
    },
    variantInputWrapper: {
      position: 'relative',
    },
    variantTextInput: {
      backgroundColor: theme.colors.card,
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.md,
      fontSize: theme.typography.fontSize.base,
      color: theme.colors.foreground,
      minHeight: 48,
    },
    variantInputError: {
      borderColor: theme.colors.destructive,
      borderWidth: 2,
    },
    variantCharacterCount: {
      position: 'absolute',
      bottom: theme.spacing.xs,
      right: theme.spacing.sm,
      fontSize: theme.typography.fontSize.xs,
      color: theme.colors.mutedForeground,
      backgroundColor: theme.colors.card,
      paddingHorizontal: theme.spacing.xs,
    },
    variantInputHint: {
      fontSize: theme.typography.fontSize.xs,
      color: theme.colors.mutedForeground,
      marginTop: theme.spacing.xs,
      lineHeight: 16,
    },
    variantErrorText: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.destructive,
      marginTop: theme.spacing.xs,
    },

    // Enhanced Add Property Button
    enhancedAddPropertyButton: {
      backgroundColor: theme.colors.card,
      borderWidth: 2,
      borderColor: theme.colors.primary,
      borderStyle: 'dashed',
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.md,
    },
    addPropertyButtonDisabled: {
      borderColor: theme.colors.muted,
      backgroundColor: theme.colors.muted,
    },
    addPropertyButtonContent: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    addPropertyIconContainer: {
      marginRight: theme.spacing.md,
    },
    addPropertyTextContainer: {
      flex: 1,
    },
    enhancedAddPropertyButtonText: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: '600',
      color: theme.colors.primary,
      marginBottom: 4,
    },
    addPropertyButtonTextDisabled: {
      color: theme.colors.mutedForeground,
    },
    addPropertyButtonSubtext: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.mutedForeground,
    },

    // Variant Sticky Buttons
    variantStickyButtonContainer: {
      flexDirection: 'row',
      gap: theme.spacing.md,
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.md,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      backgroundColor: isDark ? "#000" : "#FFF",
    },
    variantCancelButton: {
      flex: 1,
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.lg,
      borderRadius: theme.borderRadius.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      backgroundColor: isDark ? theme.colors.input : theme.colors.card,
      alignItems: 'center',
    },
    variantCancelButtonText: {
      fontSize: theme.typography.fontSize.base,
      color: isDark ? "#FFF" : "#000",
      fontWeight: '500',
    },
    variantSubmitButton: {
      flex: 1,
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.lg,
      borderRadius: theme.borderRadius.md,
      backgroundColor: isDark ? "#FFFFFF" : "#000000",
      alignItems: 'center',
    },
    variantSubmitButtonDisabled: {
      opacity: 0.6,
    },
    variantSubmitButtonText: {
      fontSize: theme.typography.fontSize.base,
      color: isDark ? "#000000" : "#FFFFFF",
      fontWeight: '600',
    },
    segmentedControl: {
      flexDirection: "row",
      backgroundColor: theme.colors.muted,
      borderRadius: theme.borderRadius.md,
      padding: 2,
    },
    segmentButton: {
      flex: 1,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: theme.spacing.sm,
      paddingHorizontal: theme.spacing.md,
      borderRadius: theme.borderRadius.sm,
      gap: theme.spacing.xs,
    },
    segmentButtonActive: {
      backgroundColor: theme.colors.primary,
    },
    segmentButtonText: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: "500",
      color: theme.colors.mutedForeground,
    },
    segmentButtonTextActive: {
      color: theme.colors.primaryForeground,
    },
    priceRow: {
      flexDirection: "row",
      gap: theme.spacing.md,
    },
    priceInput: {
      flex: 1,
    },
    toggleButton: {
      padding: theme.spacing.xs,
      borderRadius: theme.borderRadius.sm,
      backgroundColor: theme.colors.muted,
    },
    switchRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingVertical: theme.spacing.sm,
    },
    switchLabelContainer: {
      flex: 1,
      marginRight: theme.spacing.sm,
    },
    switchLabel: {
      fontSize: theme.typography.fontSize.base,
      color: theme.colors.foreground,
      fontWeight: "500",
    },
    switchSubLabel: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.destructive,
      marginTop: 2,
    },
    planLimitContainer: {
      backgroundColor: theme.colors.muted,
      padding: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
      marginBottom: theme.spacing.sm,
    },
    planLimitTitle: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: "600",
      color: theme.colors.foreground,
      marginBottom: 4,
    },
    planLimitText: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.mutedForeground,
      marginBottom: 4,
    },
    planLimitWarning: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.destructive,
      fontWeight: "500",
    },

    // Variant management styles
    variantListContainer: {
      marginTop: theme.spacing.md,
    },
    variantListHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: theme.spacing.md,
    },
    variantListTitle: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
      color: theme.colors.foreground,
    },
    addVariantButton: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: theme.colors.primary,
      paddingVertical: theme.spacing.xs,
      paddingHorizontal: theme.spacing.sm,
      borderRadius: theme.borderRadius.sm,
      gap: theme.spacing.xs,
    },
    addVariantButtonText: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.primaryForeground,
      fontWeight: "500",
    },
    variantsList: {
      gap: theme.spacing.sm,
    },
    variantItem: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.sm,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    variantImageContainer: {
      marginRight: theme.spacing.sm,
    },
    variantImage: {
      width: 40,
      height: 40,
      borderRadius: theme.borderRadius.sm,
    },
    variantImagePlaceholder: {
      width: 40,
      height: 40,
      borderRadius: theme.borderRadius.sm,
      backgroundColor: theme.colors.muted,
      justifyContent: "center",
      alignItems: "center",
    },
    variantInfo: {
      flex: 1,
      marginRight: theme.spacing.sm,
    },
    variantName: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: "600",
      color: theme.colors.foreground,
      marginBottom: 2,
    },
    variantValues: {
      fontSize: theme.typography.fontSize.xs,
      color: theme.colors.mutedForeground,
      marginBottom: 4,
    },
    variantPricing: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: 4,
    },
    variantPrice: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: "500",
      color: theme.colors.foreground,
    },
    variantDiscountedPrice: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: "500",
      color: theme.colors.foreground,
      marginRight: theme.spacing.xs,
    },
    variantOriginalPrice: {
      fontSize: theme.typography.fontSize.xs,
      color: theme.colors.mutedForeground,
      textDecorationLine: "line-through",
    },
    variantStatus: {
      flexDirection: "row",
      alignItems: "center",
      gap: theme.spacing.xs,
    },
    statusIndicator: {
      width: 6,
      height: 6,
      borderRadius: 3,
    },
    statusText: {
      fontSize: theme.typography.fontSize.xs,
      color: theme.colors.mutedForeground,
    },
    variantActions: {
      flexDirection: "row",
      gap: theme.spacing.xs,
    },
    actionButton: {
      padding: theme.spacing.xs,
      borderRadius: theme.borderRadius.sm,
      backgroundColor: theme.colors.muted,
    },
    emptyState: {
      alignItems: "center",
      paddingVertical: theme.spacing.xl,
    },
    emptyStateTitle: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
      color: theme.colors.foreground,
      marginTop: theme.spacing.sm,
      marginBottom: theme.spacing.xs,
    },
    emptyStateDescription: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.mutedForeground,
      textAlign: "center",
      marginBottom: theme.spacing.md,
    },
    emptyStateButton: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: theme.colors.primary,
      paddingVertical: theme.spacing.sm,
      paddingHorizontal: theme.spacing.md,
      borderRadius: theme.borderRadius.md,
      gap: theme.spacing.xs,
    },
    emptyStateButtonText: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.primaryForeground,
      fontWeight: "500",
    },
    loadingContainer: {
      alignItems: "center",
      paddingVertical: theme.spacing.xl,
    },
    loadingText: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.mutedForeground,
      marginTop: theme.spacing.sm,
    },

    // Variant form styles
    variantRow: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: theme.spacing.sm,
      gap: theme.spacing.sm,
    },
    variantLabel: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.foreground,
      fontWeight: "500",
      minWidth: 60,
    },
    removeButton: {
      padding: theme.spacing.xs,
      borderRadius: theme.borderRadius.sm,
      backgroundColor: theme.colors.destructive + "20",
    },
    addVariantPropertyButton: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "center",
      paddingVertical: theme.spacing.sm,
      paddingHorizontal: theme.spacing.md,
      borderRadius: theme.borderRadius.md,
      borderWidth: 1,
      borderColor: theme.colors.primary,
      borderStyle: "dashed",
      gap: theme.spacing.xs,
    },
    addVariantPropertyButtonText: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.primary,
      fontWeight: "500",
    },

    // Inline variant management styles
    variantManagementContainer: {
      marginTop: theme.spacing.md,
      gap: theme.spacing.md,
    },
    inlineVariantForm: {
      backgroundColor: theme.colors.card,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
      marginTop: theme.spacing.sm,
    },
    variantFormHeader: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: theme.spacing.md,
    },
    variantFormTitle: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
      color: theme.colors.foreground,
    },
    closeVariantFormButton: {
      padding: theme.spacing.xs,
      borderRadius: theme.borderRadius.sm,
      backgroundColor: theme.colors.muted,
    },
    localVariantsList: {
      gap: theme.spacing.sm,
      marginTop: theme.spacing.sm,
    },
    // Additional styles for VariantForm
    formContainer: {
      flex: 1,
      padding: theme.spacing.md,
    },
    inputGroup: {
      marginBottom: theme.spacing.lg,
    },
    textInput: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      fontSize: theme.typography.fontSize.base,
      color: theme.colors.foreground,
      backgroundColor: theme.colors.background,
      minHeight: 44,
    },
    inputDescription: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.mutedForeground,
      marginTop: theme.spacing.xs,
    },
    variantTypeButton: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      backgroundColor: theme.colors.background,
      minHeight: 44,
      justifyContent: "center",
      alignItems: "center",
    },
    variantTypeButtonText: {
      fontSize: theme.typography.fontSize.base,
      color: theme.colors.foreground,
    },
    variantValueButton: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      backgroundColor: theme.colors.background,
      minHeight: 44,
      justifyContent: "center",
      alignItems: "center",
    },
    variantValueButtonText: {
      fontSize: theme.typography.fontSize.base,
      color: theme.colors.foreground,
    },
    variantValueInput: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      fontSize: theme.typography.fontSize.base,
      color: theme.colors.foreground,
      backgroundColor: theme.colors.background,
      minHeight: 44,
    },
    colorValueDisplay: {
      flexDirection: "row",
      alignItems: "center",
      gap: theme.spacing.sm,
    },
    colorSwatch: {
      width: 20,
      height: 20,
      borderRadius: 10,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    emptyStateText: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.mutedForeground,
      textAlign: "center",
    },
    imageActions: {
      flexDirection: "row",
      gap: theme.spacing.sm,
      marginTop: theme.spacing.sm,
    },
    imageActionButton: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
      backgroundColor: theme.colors.primary,
      alignItems: "center",
      justifyContent: "center",
    },
    imageActionText: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.primaryForeground,
      fontWeight: "500",
    },
  });
};
