import React from 'react';
import { View } from 'react-native';

const createMockSvgComponent = (name) => {
  const Component = ({ children, ...props }) => (
    <View
      {...props}
      testID={props.testID || `mock-${name.toLowerCase()}`}
      accessibilityLabel={props.accessibilityLabel || `Mock ${name}`}
    >
      {children}
    </View>
  );
  Component.displayName = `Mock${name}`;
  return Component;
};

const Svg = createMockSvgComponent('Svg');
const Path = createMockSvgComponent('Path');
const G = createMockSvgComponent('G');
const Circle = createMockSvgComponent('Circle');
const Rect = createMockSvgComponent('Rect');
const Line = createMockSvgComponent('Line');
const Polygon = createMockSvgComponent('Polygon');
const Polyline = createMockSvgComponent('Polyline');
const Ellipse = createMockSvgComponent('Ellipse');
const Text = createMockSvgComponent('Text');
const TSpan = createMockSvgComponent('TSpan');
const Defs = createMockSvgComponent('Defs');
const LinearGradient = createMockSvgComponent('LinearGradient');
const Stop = createMockSvgComponent('Stop');
const ClipPath = createMockSvgComponent('ClipPath');
const Mask = createMockSvgComponent('Mask');
const Use = createMockSvgComponent('Use');
const Symbol = createMockSvgComponent('Symbol');
const Marker = createMockSvgComponent('Marker');
const Image = createMockSvgComponent('Image');
const ForeignObject = createMockSvgComponent('ForeignObject');

export default Svg;
export {
  Svg,
  Path,
  G,
  Circle,
  Rect,
  Line,
  Polygon,
  Polyline,
  Ellipse,
  Text,
  TSpan,
  Defs,
  LinearGradient,
  Stop,
  ClipPath,
  Mask,
  Use,
  Symbol,
  Marker,
  Image,
  ForeignObject
};
