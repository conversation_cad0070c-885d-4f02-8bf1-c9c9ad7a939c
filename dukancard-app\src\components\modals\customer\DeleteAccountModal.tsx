/**
 * DeleteAccountModal Component
 * A dedicated modal with instructions on how to delete account on website
 */

import React from "react";
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  SafeAreaView,
  ScrollView,
  Linking,
} from "react-native";
import { X, ExternalLink, AlertTriangle, Globe } from "lucide-react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { createDeleteAccountModalStyles } from "@/styles/modals/customer/delete-account-modal";

interface DeleteAccountModalProps {
  visible: boolean;
  onClose: () => void;
}

export default function DeleteAccountModal({ visible, onClose }: DeleteAccountModalProps) {
  const theme = useTheme();
  const styles = createDeleteAccountModalStyles(theme);
  
  const handleOpenWebsite = async () => {
    try {
      await Linking.openURL("https://dukancard.in");
    } catch (error) {
      console.error("Failed to open website:", error);
      // Fallback to main website
      await Linking.openURL("https://dukancard.in");
    }
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <View style={{ width: 40 }} />
          <Text style={styles.headerTitle}>Delete Account</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <X size={24} color={theme.colors.foreground} />
          </TouchableOpacity>
        </View>

        {/* Content */}
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Warning Section */}
          <View style={styles.warningSection}>
            <AlertTriangle size={48} color="#ff4444" />
            <Text style={styles.warningTitle}>Account Deletion</Text>
            <Text style={styles.warningText}>
              This action cannot be undone. All your data will be permanently deleted.
            </Text>
          </View>

          {/* Instructions Section */}
          <View style={styles.instructionsSection}>
            <Text style={styles.sectionTitle}>How to Delete Your Account</Text>
            
            <View style={styles.stepContainer}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>1</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>Visit Our Website</Text>
                <Text style={styles.stepDescription}>
                  Go to dukancard.in on your web browser to access the account deletion feature.
                </Text>
              </View>
            </View>

            <View style={styles.stepContainer}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>2</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>Sign In to Your Account</Text>
                <Text style={styles.stepDescription}>
                  Log in using the same credentials you use for this mobile app.
                </Text>
              </View>
            </View>

            <View style={styles.stepContainer}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>3</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>Navigate to Account Settings</Text>
                <Text style={styles.stepDescription}>
                  Go to your profile settings and look for the &quot;Delete Account&quot; option.
                </Text>
              </View>
            </View>

            <View style={styles.stepContainer}>
              <View style={styles.stepNumber}>
                <Text style={styles.stepNumberText}>4</Text>
              </View>
              <View style={styles.stepContent}>
                <Text style={styles.stepTitle}>Confirm Deletion</Text>
                <Text style={styles.stepDescription}>
                  Follow the verification steps to permanently delete your account and all associated data.
                </Text>
              </View>
            </View>
          </View>

          {/* What Gets Deleted Section */}
          <View style={styles.deletionInfoSection}>
            <Text style={styles.sectionTitle}>What Will Be Deleted</Text>
            <View style={styles.deletionList}>
              <Text style={styles.deletionItem}>• Your profile information</Text>
              <Text style={styles.deletionItem}>• All your reviews and ratings</Text>
              <Text style={styles.deletionItem}>• Your liked businesses</Text>
              <Text style={styles.deletionItem}>• Your subscriptions and follows</Text>
              <Text style={styles.deletionItem}>• All app preferences and settings</Text>
            </View>
          </View>

          {/* Support Section */}
          <View style={styles.supportSection}>
            <Text style={styles.sectionTitle}>Need Help?</Text>
            <Text style={styles.supportText}>
              If you&apos;re having trouble deleting your account or have questions about the process,
              please contact our support team through the website.
            </Text>
          </View>
        </ScrollView>

        {/* Footer Button */}
        <View style={styles.footer}>
          <TouchableOpacity style={styles.websiteButton} onPress={handleOpenWebsite}>
            <Globe size={20} color="#fff" />
            <Text style={styles.websiteButtonText}>Open Website</Text>
            <ExternalLink size={16} color="#fff" />
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );
}
