import React from 'react';
import { render } from '@testing-library/react-native';
import { DukancardLogo } from '../../../../src/components/ui/DukancardLogo';

describe('<DukancardLogo />', () => {
  it('renders correctly with default props', () => {
    const { toJSON } = render(<DukancardLogo />);
    expect(toJSON()).toMatchSnapshot();
  });

  it('renders with showText prop', () => {
    const { getByText } = render(<DukancardLogo showText={true} />);
    expect(getByText('Dukan')).toBeDefined();
    expect(getByText('card')).toBeDefined();
  });

  it('renders with showTagline prop', () => {
    const { getByText } = render(<DukancardLogo showTagline={true} />);
    expect(getByText('Connect • Discover • Grow')).toBeDefined();
  });

  it('applies different size prop values', () => {
    const { toJSON: small } = render(<DukancardLogo size="small" />);
    expect(small()).toMatchSnapshot();

    const { toJSON: medium } = render(<DukancardLogo size="medium" />);
    expect(medium()).toMatchSnapshot();

    const { toJSON: large } = render(<DukancardLogo size="large" />);
    expect(large()).toMatchSnapshot();

    const { toJSON: hero } = render(<DukancardLogo size="hero" />);
    expect(hero()).toMatchSnapshot();
  });

  it('applies custom style prop', () => {
    const customStyle = { backgroundColor: 'red' };
    const { toJSON } = render(<DukancardLogo style={customStyle} />);
    expect(toJSON()).toMatchSnapshot();
  });
});