"use client";

/**
 * Realtime Subscription Notes
 *
 * This component uses Supabase Realtime to listen for new activities.
 *
 * Important: You need to enable realtime for the business_activities table in Supabase:
 * 1. Go to Supabase Dashboard > Database > Replication
 * 2. Find the "business_activities" table in the list
 * 3. Enable realtime by toggling it on
 *
 * The component subscribes to INSERT events on the business_activities table
 * with a filter for the specific business_profile_id:
 *
 * ```javascript
 * supabase
 *   .channel('dashboard-activities-changes')
 *   .on(
 *     'postgres_changes',
 *     {
 *       event: 'INSERT',
 *       schema: 'public',
 *       table: 'business_activities',
 *       filter: `business_profile_id=eq.${businessProfileId}`,
 *     },
 *     async () => {
 *       // Handle new activity
 *     }
 *   )
 *   .subscribe();
 * ```
 */

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Bell, Heart, Star, Users, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { formatDistanceToNow } from "date-fns";
import Link from "next/link";
import {
  BusinessActivity,
  getBusinessActivities,
} from "@/lib/actions/activities";
import { realtimeService } from "@/lib/services/realtimeService";

interface RecentActivitiesProps {
  businessProfileId: string;
  unreadCount: number;
}

export default function RecentActivities({
  businessProfileId,
  unreadCount,
}: RecentActivitiesProps) {
  const [activities, setActivities] = useState<BusinessActivity[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch recent activities
  useEffect(() => {
    const fetchActivities = async () => {
      setLoading(true);
      try {
        const { activities } = await getBusinessActivities({
          businessProfileId,
          page: 1,
          pageSize: 5,
          sortBy: "newest",
          filterBy: "all",
        });

        setActivities(activities || []);
      } catch (error) {
        console.error("Error fetching recent activities:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchActivities();
  }, [businessProfileId]);

  // Set up real-time subscription for new activities
  useEffect(() => {
    if (!businessProfileId) return;

    const subscription = realtimeService.subscribeToBusinessActivities(
      businessProfileId,
      async () => {
        // Fetch the updated activities
        const { activities: newActivities } = await getBusinessActivities({
          businessProfileId,
          page: 1,
          pageSize: 5,
          sortBy: "newest",
          filterBy: "all",
        });

        setActivities(newActivities || []);
      },
      "recent-activities"
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [businessProfileId]);

  // Container animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="group relative overflow-hidden rounded-2xl border border-neutral-200/60 dark:border-neutral-800/60 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm hover:border-neutral-300/60 dark:hover:border-neutral-700/60 transition-all duration-300 p-6 h-full"
    >
      {/* Gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/80 to-white/40 dark:from-neutral-900/80 dark:to-neutral-900/40" />
      <div className="relative z-10">
        <div className="flex items-center justify-between gap-3 mb-6">
          <div className="space-y-1">
            <div className="flex items-center gap-3">
              <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20">
                <Bell className="w-5 h-5 text-primary" />
              </div>
              <div className="h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700" />
              <div className="text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase">
                Activity Feed
              </div>
            </div>
            <div className="flex items-center gap-3">
              <h3 className="text-xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight">
                Recent Activities
              </h3>
              {unreadCount > 0 && (
                <Badge
                  variant="secondary"
                  className="bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300 border border-blue-200 dark:border-blue-800"
                >
                  {unreadCount > 99 ? "99+" : unreadCount} new
                </Badge>
              )}
            </div>
          </div>

          <Button
            asChild
            variant="outline"
            className="border-neutral-200 dark:border-neutral-700 hover:bg-neutral-50 dark:hover:bg-neutral-800 transition-all duration-200 px-4 py-2 h-auto font-medium"
            size="sm"
          >
            <Link
              href="/dashboard/business/activities"
              className="flex items-center gap-2"
            >
              <span>View All</span>
              <ArrowRight className="w-3 h-3" />
            </Link>
          </Button>
        </div>
      </div>

        {loading ? (
          <div className="space-y-3">
            <Skeleton className="h-16 w-full rounded-lg" />
            <Skeleton className="h-16 w-full rounded-lg" />
            <Skeleton className="h-16 w-full rounded-lg" />
          </div>
        ) : activities.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <Bell className="w-10 h-10 text-neutral-300 dark:text-neutral-600 mb-3" />
            <h4 className="text-base font-medium text-neutral-700 dark:text-neutral-300">
              No activities yet
            </h4>
            <p className="text-sm text-neutral-500 dark:text-neutral-400 mt-1">
              When someone interacts with your business, it will appear here.
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {activities.map((activity) => (
              <RecentActivityItem key={activity.id} activity={activity} />
            ))}
          </div>
        )}
    </motion.div>
  );
}

function RecentActivityItem({ activity }: { activity: BusinessActivity }) {
  // Determine if the user is a business or customer
  const isBusiness = activity.user_profile?.is_business || false;

  // Get the appropriate name and avatar
  const displayName =
    activity.user_profile && isBusiness
      ? (activity.user_profile as { is_business: true; business_name?: string })
          .business_name || "Business User"
      : activity.user_profile
      ? (activity.user_profile as { is_business: false; name?: string | null })
          .name || "Customer"
      : "Unknown User";

  const avatarUrl =
    activity.user_profile && isBusiness
      ? (
          activity.user_profile as {
            is_business: true;
            logo_url?: string | null;
          }
        ).logo_url
      : activity.user_profile
      ? (
          activity.user_profile as {
            is_business: false;
            avatar_url?: string | null;
          }
        ).avatar_url
      : null;

  // Get initials for avatar fallback
  const getInitials = (name?: string | null) => {
    if (!name) return "?";
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  const initials = getInitials(displayName);

  // Format the time
  const timeAgo = activity.created_at
    ? formatDistanceToNow(new Date(activity.created_at), { addSuffix: true })
    : "recently";

  // Get activity icon and text
  const getActivityDetails = () => {
    switch (activity.activity_type) {
      case "like":
        return {
          icon: <Heart className="w-4 h-4 text-rose-500" />,
          text: "liked your business",
          color:
            "bg-rose-50 text-rose-700 dark:bg-rose-900/20 dark:text-rose-300",
        };
      case "subscribe":
        return {
          icon: <Users className="w-4 h-4 text-blue-500" />,
          text: "subscribed to your business",
          color:
            "bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300",
        };
      case "rating":
        return {
          icon: <Star className="w-4 h-4 text-amber-500" />,
          text: `rated your business ${activity.rating_value}/5`,
          color:
            "bg-amber-50 text-amber-700 dark:bg-amber-900/20 dark:text-amber-300",
        };
      default:
        return {
          icon: <Star className="w-4 h-4" />,
          text: "interacted with your business",
          color:
            "bg-neutral-50 text-neutral-700 dark:bg-neutral-900/20 dark:text-neutral-300",
        };
    }
  };

  const { icon, text, color } = getActivityDetails();

  // Get profile link
  const profileLink =
    isBusiness &&
    activity.user_profile &&
    (activity.user_profile as { is_business: true; business_slug?: string })
      .business_slug
      ? `/${
          (
            activity.user_profile as {
              is_business: true;
              business_slug?: string;
            }
          ).business_slug
        }`
      : "#";

  // Item animation variants
  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <motion.div
      variants={itemVariants}
      className={cn(
        "relative rounded-lg border p-3 transition-all duration-300",
        activity.is_read
          ? "border-neutral-200 dark:border-neutral-800 bg-white dark:bg-black"
          : "border-blue-200 dark:border-blue-800 bg-blue-50/50 dark:bg-blue-900/10"
      )}
    >
      {/* Unread indicator */}
      {!activity.is_read && (
        <div className="absolute top-3 right-3 w-2 h-2 rounded-full bg-blue-500" />
      )}

      <div className="flex items-center gap-3">
        {/* Avatar - Only link for business users */}
        {isBusiness ? (
          <Link
            href={profileLink}
            className="shrink-0"
            target="_blank"
            rel="noopener noreferrer"
          >
            <Avatar className="h-8 w-8 rounded-lg border border-amber-200 dark:border-amber-800">
              {avatarUrl ? (
                <AvatarImage src={avatarUrl} alt={displayName || "User"} />
              ) : null}
              <AvatarFallback className="rounded-lg text-xs bg-amber-50 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300">
                {initials}
              </AvatarFallback>
            </Avatar>
          </Link>
        ) : (
          <div className="shrink-0">
            <Avatar className="h-8 w-8 rounded-lg border border-neutral-200 dark:border-neutral-700">
              {avatarUrl ? (
                <AvatarImage src={avatarUrl} alt={displayName || "User"} />
              ) : null}
              <AvatarFallback className="rounded-lg text-xs bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300">
                {initials}
              </AvatarFallback>
            </Avatar>
          </div>
        )}

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex flex-wrap items-center gap-1">
            {/* Only link for business users */}
            {isBusiness ? (
              <Link
                href={profileLink}
                className="font-medium hover:underline truncate text-sm"
                target="_blank"
                rel="noopener noreferrer"
              >
                {displayName || "Anonymous User"}
              </Link>
            ) : (
              <span className="font-medium truncate text-sm">
                {displayName || "Anonymous User"}
              </span>
            )}

            {/* Business badge */}
            {isBusiness && (
              <Badge
                variant="outline"
                className="bg-amber-50 text-amber-700 dark:bg-amber-900/30 dark:text-amber-300 border-amber-200 dark:border-amber-800 flex items-center gap-1 py-0 h-4 text-[10px]"
              >
                Business
              </Badge>
            )}

            <span className="text-neutral-500 dark:text-neutral-400 text-xs">
              {text}
            </span>
          </div>

          <div className="flex items-center justify-between mt-1">
            <div className="flex items-center gap-2">
              <Badge
                variant="secondary"
                className={cn("flex items-center gap-1 h-5 text-xs", color)}
              >
                {icon}
                <span className="text-[10px] capitalize">
                  {activity.activity_type}
                </span>
              </Badge>

              <span className="text-[10px] text-neutral-500 dark:text-neutral-400">
                {timeAgo}
              </span>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
