/**
 * Styles for CompactLocationPicker Component
 */

import { StyleSheet } from "react-native";

export const createCompactLocationPickerStyles = () =>
  StyleSheet.create({
    container: {
      borderWidth: 0,
      borderRadius: 0,
      marginBottom: 0,
      marginTop: 0,
      paddingHorizontal: 0,
      paddingVertical: 0,
      overflow: "hidden",
    },
    disabled: {
      opacity: 0.6,
    },
    content: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: 16,
      paddingVertical: 2,
      minHeight: 32,
    },
    iconContainer: {
      width: 32,
      height: 32,
      borderRadius: 16,
      alignItems: "center",
      justifyContent: "center",
      marginRight: 8,
    },
    textContainer: {
      flex: 1,
      marginRight: 4,
    },
    locationText: {
      fontSize: 16,
      fontWeight: "600",
      marginBottom: 2,
    },
    placeholderText: {
      fontWeight: "500",
    },
    subtextText: {
      fontSize: 13,
      fontWeight: "400",
    },
    chevronContainer: {
      padding: 4,
    },
  });
