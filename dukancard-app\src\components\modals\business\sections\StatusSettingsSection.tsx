import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Switch,
} from "react-native";
import { Controller, useFormContext } from "react-hook-form";
import { Globe, Eye, Link as LinkIcon } from "lucide-react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { FormField } from "@/src/components/ui/FormField";
import { useToast } from "@/src/components/ui/Toast";
import { updateStatusSettings } from "@/backend/supabase/services/business/businessCardService";
import { StatusSettingsData, statusSettingsSchema } from "@/backend/supabase/services/business/schemas";
import { createStatusSettingsSectionStyles } from "@/styles/modals/business/sections/status-settings-section";

interface StatusSettingsSectionProps {
  onBack: () => void;
}

export default function StatusSettingsSection({ onBack }: StatusSettingsSectionProps) {
  const theme = useTheme();
  const styles = createStatusSettingsSectionStyles(theme);
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const { control, handleSubmit, watch } = useFormContext();
  const currentStatus = watch("status");
  const currentSlug = watch("business_slug");

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    
    // Extract only status settings fields
    const statusSettingsData: StatusSettingsData = {
      status: data.status,
      business_slug: data.business_slug,
    };

    // Validate the data
    const validation = statusSettingsSchema.safeParse(statusSettingsData);
    if (!validation.success) {
      toast.error("Validation Error", "Please check all required fields.");
      setIsLoading(false);
      return;
    }

    const { success, error } = await updateStatusSettings(statusSettingsData);
    if (success) {
      toast.success("Success", "Status settings updated successfully.");
      onBack();
    } else {
      toast.error("Error", error || "Failed to update status settings.");
    }
    setIsLoading(false);
  };

  const getPublicCardUrl = () => {
    if (currentSlug && currentStatus === "online") {
      return `https://dukancard.com/card/${currentSlug}`;
    }
    return null;
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {/* Business Status Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Business Status</Text>
          
          <View style={styles.statusCard}>
            <View style={styles.statusHeader}>
              <View style={styles.statusIconContainer}>
                <Globe size={24} color={currentStatus === "online" ? "#22C55E" : "#6B7280"} />
              </View>
              <View style={styles.statusInfo}>
                <Text style={styles.statusTitle}>
                  {currentStatus === "online" ? "Online" : "Offline"}
                </Text>
                <Text style={styles.statusDescription}>
                  {currentStatus === "online" 
                    ? "Your business card is visible to customers"
                    : "Your business card is hidden from public view"
                  }
                </Text>
              </View>
              <Controller
                control={control}
                name="status"
                render={({ field: { onChange, value } }) => (
                  <Switch
                    value={value === "online"}
                    onValueChange={(isOnline) => onChange(isOnline ? "online" : "offline")}
                    trackColor={{ false: "#767577", true: "#22C55E" }}
                    thumbColor={value === "online" ? "#FFF" : "#f4f3f4"}
                  />
                )}
              />
            </View>
          </View>
        </View>

        {/* Business Slug Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Business Card URL</Text>
          
          <Controller
            control={control}
            name="business_slug"
            render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
              <FormField
                label="Custom URL Slug"
                value={value || ""}
                onChangeText={onChange}
                onBlur={onBlur}
                error={error?.message}
                placeholder="your-business-name"

              />
            )}
          />

          {getPublicCardUrl() && (
            <View style={styles.urlPreview}>
              <View style={styles.urlPreviewHeader}>
                <LinkIcon size={16} color={theme.colors.primary} />
                <Text style={styles.urlPreviewTitle}>Public Card URL</Text>
              </View>
              <Text style={styles.urlPreviewText}>{getPublicCardUrl()}</Text>
            </View>
          )}
        </View>

        {/* Status Information */}
        <View style={styles.infoSection}>
          <View style={styles.infoCard}>
            <Eye size={20} color={theme.colors.primary} />
            <View style={styles.infoContent}>
              <Text style={styles.infoTitle}>Visibility Settings</Text>
              <Text style={styles.infoDescription}>
                When online, customers can find and view your business card. 
                When offline, your card is hidden but you can still manage it.
              </Text>
            </View>
          </View>

          {currentStatus === "offline" && (
            <View style={styles.warningCard}>
              <Text style={styles.warningText}>
                ⚠️ Your business card is currently offline. Customers cannot view or find your business card.
              </Text>
            </View>
          )}

          {currentStatus === "online" && (
            <View style={styles.successCard}>
              <Text style={styles.successText}>
                ✅ Your business card is live! Customers can now find and view your business information.
              </Text>
            </View>
          )}
        </View>
      </ScrollView>

      {/* Save Button */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.saveButton,
            { backgroundColor: theme.colors.primary },
            isLoading && styles.saveButtonDisabled,
          ]}
          onPress={handleSubmit(onSubmit)}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="white" />
          ) : (
            <Text style={styles.saveButtonText}>Save Status Settings</Text>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
}
