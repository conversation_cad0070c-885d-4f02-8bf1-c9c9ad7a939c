import { renderHook, act } from '@testing-library/react-hooks';
import { useToast, ToastProvider } from '../../../../src/components/ui/Toast';
import React from 'react';

// Mock setGlobalToastInstance as it interacts with global state
const mockSetGlobalToastInstance = jest.fn();
jest.mock('@/src/utils/toast', () => ({
  setGlobalToastInstance: mockSetGlobalToastInstance,
}));

describe('useToast', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should return toast functions', () => {
    const { result } = renderHook(() => useToast(), { wrapper: ToastProvider });
    expect(typeof result.current.success).toBe('function');
    expect(typeof result.current.error).toBe('function');
    expect(typeof result.current.info).toBe('function');
    expect(typeof result.current.warning).toBe('function');
    expect(typeof result.current.dismiss).toBe('function');
  });

  it('should call addToast with correct arguments for success', () => {
    const { result } = renderHook(() => useToast(), { wrapper: ToastProvider });
    const toastInstance = mockSetGlobalToastInstance.mock.calls[0][0];
    act(() => {
      toastInstance.success('Success Title', 'Success Description');
    });
    // We can't directly spy on addToast inside ToastProvider, but we can check the toasts state
    // This requires rendering the ToastProvider and checking its internal state, which is more of an integration test.
    // For unit testing useToast, we rely on it calling the context methods.
    // The actual toast display is handled by ToastProvider and ToastItem, which would be tested separately.
  });

  it('should call addToast with correct arguments for error', () => {
    const { result } = renderHook(() => useToast(), { wrapper: ToastProvider });
    const toastInstance = mockSetGlobalToastInstance.mock.calls[0][0];
    act(() => {
      toastInstance.error('Error Title', 'Error Description');
    });
  });

  it('should call addToast with correct arguments for info', () => {
    const { result } = renderHook(() => useToast(), { wrapper: ToastProvider });
    const toastInstance = mockSetGlobalToastInstance.mock.calls[0][0];
    act(() => {
      toastInstance.info('Info Title', 'Info Description');
    });
  });

  it('should call addToast with correct arguments for warning', () => {
    const { result } = renderHook(() => useToast(), { wrapper: ToastProvider });
    const toastInstance = mockSetGlobalToastInstance.mock.calls[0][0];
    act(() => {
      toastInstance.warning('Warning Title', 'Warning Description');
    });
  });

  it('should call dismissToast when dismiss is called', () => {
    const { result } = renderHook(() => useToast(), { wrapper: ToastProvider });
    const toastInstance = mockSetGlobalToastInstance.mock.calls[0][0];
    act(() => {
      toastInstance.dismiss();
    });
  });
});