import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  Image,
} from "react-native";
import { useTheme } from "@/src/hooks/useTheme";
import {
  fetchBusinessLikesReceived,
  BusinessLikeReceived,
} from "@/backend/supabase/services/business/businessSocialService";
import { createLikesModalStyles } from "@/styles/modals/customer/likes-modal";

// Define interfaces for the expected data structure
interface BusinessProfileDataForLike {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
  address_line: string | null;
}

interface CustomerProfileDataForLike {
  id: string;
  name: string | null;
  email: string | null;
  avatar_url: string | null;
}

interface BusinessLikesListProps {
  businessId: string;
  searchTerm: string;
}

export default function BusinessLikesList({
  businessId,
  searchTerm,
}: BusinessLikesListProps) {
  const theme = useTheme();
  const styles = createLikesModalStyles(theme);
  const [likes, setLikes] = useState<BusinessLikeReceived[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const fetchLikes = useCallback(
    async (isRefreshing = false) => {
      if (loading) return;

      setLoading(true);
      if (isRefreshing) {
        setRefreshing(true);
      }

      try {
        const currentPage = isRefreshing ? 1 : page;
        const { items, hasMore: newHasMore } = await fetchBusinessLikesReceived(
          businessId,
          currentPage,
          10
        );

        if (isRefreshing) {
          setLikes(items);
        } else {
          setLikes((prev) => [...prev, ...items]);
        }
        setHasMore(newHasMore);
        if (newHasMore) {
          setPage(currentPage + 1);
        }
      } catch (error) {
        console.error("Failed to fetch likes:", error);
      } finally {
        setLoading(false);
        if (isRefreshing) {
          setRefreshing(false);
        }
      }
    },
    [businessId, loading, page]
  );

  useEffect(() => {
    fetchLikes(true);
  }, [searchTerm, fetchLikes]);

  const handleLoadMore = () => {
    if (!loading && hasMore) {
      fetchLikes();
    }
  };

  const onRefresh = () => {
    fetchLikes(true);
  };

  const renderItem = ({ item }: { item: BusinessLikeReceived }) => {
    const profile =
      item.profile_type === "business"
        ? item.business_profiles
        : item.customer_profiles;
    const name =
      item.profile_type === "business"
        ? (profile as BusinessProfileDataForLike)?.business_name
        : (profile as CustomerProfileDataForLike)?.name;
    const avatarUrl =
      item.profile_type === "business"
        ? (profile as any)?.logo_url
        : (profile as any)?.avatar_url;

    return (
      <View style={styles.header}>
        <Image
          source={{
            uri:
              avatarUrl ||
              "https://asset.brandfetch.io/id235U50sE/idj9kF8hYy.jpeg",
          }}
          style={{ width: 50, height: 50, borderRadius: 25 }}
        />
        <View style={{ flex: 1, marginLeft: 12 }}>
          <Text style={styles.headerTitle}>{name}</Text>
          <Text style={styles.emptyText}>
            {item.profile_type === "business" ? "Business" : "Customer"}
          </Text>
        </View>
      </View>
    );
  };

  const renderFooter = () => {
    if (!loading) return null;
    return (
      <View style={{ paddingVertical: 20 }}>
        <ActivityIndicator
          animating
          size="large"
          color={theme.colors.primary}
        />
      </View>
    );
  };

  if (loading && page === 1) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
      </View>
    );
  }

  if (!likes.length) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>No likes received yet.</Text>
      </View>
    );
  }

  return (
    <FlatList
      data={likes}
      renderItem={renderItem}
      keyExtractor={(item) => item.id}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.5}
      ListFooterComponent={renderFooter}
      refreshing={refreshing}
      onRefresh={onRefresh}
      contentContainerStyle={{ paddingBottom: 20 }}
    />
  );
}
