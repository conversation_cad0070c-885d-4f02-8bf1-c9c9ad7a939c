import React, { useState, useEffect, forwardRef, useImperativeHandle } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  Modal,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import { Search, X, Check } from "lucide-react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { createColorPickerBottomSheetStyles } from "@/styles/pickers/color-picker-bottom-sheet";
import { getPredefinedOptionsForType, PredefinedVariantOption } from "@/src/constants/predefinedVariants";

interface ColorPickerBottomSheetProps {
  selectedColor?: string;
  onSelect: (color: PredefinedVariantOption) => void;
  onClose: () => void;
}

export interface ColorPickerBottomSheetRef {
  open: () => void;
  close: () => void;
}

const ColorPickerBottomSheet = forwardRef<ColorPickerBottomSheetRef, ColorPickerBottomSheetProps>(
  ({ selectedColor, onSelect, onClose }, ref) => {
    const theme = useTheme();
    const styles = createColorPickerBottomSheetStyles(theme);
    
    const [visible, setVisible] = useState(false);
    const [searchQuery, setSearchQuery] = useState("");
    const [colors, setColors] = useState<PredefinedVariantOption[]>([]);
    const [filteredColors, setFilteredColors] = useState<PredefinedVariantOption[]>([]);

    useImperativeHandle(ref, () => ({
      open: () => setVisible(true),
      close: () => setVisible(false),
    }));

    useEffect(() => {
      const colorOptions = getPredefinedOptionsForType("color");
      setColors(colorOptions);
      setFilteredColors(colorOptions);
    }, []);

    useEffect(() => {
      if (!searchQuery.trim()) {
        setFilteredColors(colors);
      } else {
        const filtered = colors.filter(
          (color) =>
            color.display_value.toLowerCase().includes(searchQuery.toLowerCase()) ||
            color.value.toLowerCase().includes(searchQuery.toLowerCase())
        );
        setFilteredColors(filtered);
      }
    }, [searchQuery, colors]);

    const handleClose = () => {
      setVisible(false);
      setSearchQuery("");
      onClose();
    };

    const handleSelect = (color: PredefinedVariantOption) => {
      onSelect(color);
      handleClose();
    };

    const renderColor = ({ item }: { item: PredefinedVariantOption }) => {
      const isSelected = selectedColor === item.value;

      return (
        <TouchableOpacity
          style={[styles.colorItem, isSelected && styles.colorItemSelected]}
          onPress={() => handleSelect(item)}
          activeOpacity={0.7}
        >
          <View style={styles.colorInfo}>
            <View style={styles.colorPreview}>
              {item.color_code ? (
                <View
                  style={[
                    styles.colorSwatch,
                    { backgroundColor: item.color_code },
                    item.color_code === "#FFFFFF" && styles.whiteColorBorder,
                  ]}
                />
              ) : (
                <View style={styles.colorSwatchPlaceholder} />
              )}
            </View>
            <View style={styles.colorDetails}>
              <Text style={[styles.colorName, isSelected && styles.colorNameSelected]}>
                {item.display_value}
              </Text>
              <Text style={[styles.colorValue, isSelected && styles.colorValueSelected]}>
                {item.color_code || item.value}
              </Text>
            </View>
          </View>
          {isSelected && (
            <Check size={20} color={theme.colors.primary} />
          )}
        </TouchableOpacity>
      );
    };

    return (
      <Modal
        visible={visible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={handleClose}
      >
        <SafeAreaView style={styles.safeArea}>
          <KeyboardAvoidingView
            style={styles.container}
            behavior={Platform.OS === "ios" ? "padding" : "height"}
          >
            {/* Header */}
            <View style={styles.header}>
              <Text style={styles.headerTitle}>Select Color</Text>
              <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
                <X size={24} color={theme.colors.foreground} />
              </TouchableOpacity>
            </View>

            {/* Search */}
            <View style={styles.searchContainer}>
              <View style={styles.searchInputContainer}>
                <Search size={20} color={theme.colors.mutedForeground} />
                <TextInput
                  style={styles.searchInput}
                  placeholder="Search colors..."
                  placeholderTextColor={theme.colors.mutedForeground}
                  value={searchQuery}
                  onChangeText={setSearchQuery}
                  autoCapitalize="none"
                />
                {searchQuery.length > 0 && (
                  <TouchableOpacity
                    style={styles.clearButton}
                    onPress={() => setSearchQuery("")}
                  >
                    <X size={16} color={theme.colors.mutedForeground} />
                  </TouchableOpacity>
                )}
              </View>
            </View>

            {/* Colors List */}
            <FlatList
              data={filteredColors}
              renderItem={renderColor}
              keyExtractor={(item) => item.value}
              style={styles.list}
              contentContainerStyle={styles.listContent}
              showsVerticalScrollIndicator={false}
              ItemSeparatorComponent={() => <View style={styles.separator} />}
            />

            {filteredColors.length === 0 && searchQuery.length > 0 && (
              <View style={styles.emptyState}>
                <Text style={styles.emptyStateText}>
                  No colors found for &quot;{searchQuery}&quot;
                </Text>
              </View>
            )}
          </KeyboardAvoidingView>
        </SafeAreaView>
      </Modal>
    );
  }
);

ColorPickerBottomSheet.displayName = "ColorPickerBottomSheet";

export default ColorPickerBottomSheet;
