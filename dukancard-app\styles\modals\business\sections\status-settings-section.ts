import { StyleSheet } from "react-native";
import { useTheme } from "@/src/hooks/useTheme";

export const createStatusSettingsSectionStyles = (
  theme: ReturnType<typeof useTheme>
) => {
  const { isDark } = theme;
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: isDark ? "#000" : "#FFF",
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      padding: theme.spacing.md,
      paddingBottom: theme.spacing.xl,
    },
    section: {
      marginBottom: theme.spacing.lg,
    },
    sectionTitle: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
      color: isDark ? "#FFF" : "#000",
      marginBottom: theme.spacing.sm,
    },
    
    // Status card styles
    statusCard: {
      backgroundColor: isDark ? "rgba(255, 255, 255, 0.05)" : "rgba(0, 0, 0, 0.02)",
      borderRadius: theme.borderRadius.lg,
      padding: theme.spacing.md,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    statusHeader: {
      flexDirection: "row",
      alignItems: "center",
    },
    statusIconContainer: {
      width: 48,
      height: 48,
      borderRadius: 24,
      backgroundColor: isDark ? "rgba(255, 255, 255, 0.1)" : "rgba(0, 0, 0, 0.05)",
      alignItems: "center",
      justifyContent: "center",
      marginRight: theme.spacing.sm,
    },
    statusInfo: {
      flex: 1,
    },
    statusTitle: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
      color: isDark ? "#FFF" : "#000",
      marginBottom: 2,
    },
    statusDescription: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
    },
    
    // URL preview styles
    urlPreview: {
      backgroundColor: isDark ? "rgba(212, 175, 55, 0.1)" : "rgba(212, 175, 55, 0.1)",
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      marginTop: theme.spacing.sm,
    },
    urlPreviewHeader: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: theme.spacing.xs,
    },
    urlPreviewTitle: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: "600",
      color: theme.colors.primary,
      marginLeft: theme.spacing.xs,
    },
    urlPreviewText: {
      fontSize: theme.typography.fontSize.sm,
      color: isDark ? "#D4AF37" : "#B8860B",
      fontFamily: "monospace",
    },
    
    // Info section styles
    infoSection: {
      marginTop: theme.spacing.md,
    },
    infoCard: {
      flexDirection: "row",
      backgroundColor: isDark ? "rgba(255, 255, 255, 0.05)" : "rgba(0, 0, 0, 0.02)",
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.sm,
    },
    infoContent: {
      flex: 1,
      marginLeft: theme.spacing.sm,
    },
    infoTitle: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: "600",
      color: isDark ? "#FFF" : "#000",
      marginBottom: 2,
    },
    infoDescription: {
      fontSize: theme.typography.fontSize.xs,
      color: theme.colors.textSecondary,
      lineHeight: 16,
    },
    
    // Status cards
    warningCard: {
      backgroundColor: isDark ? "rgba(239, 68, 68, 0.1)" : "rgba(239, 68, 68, 0.1)",
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      marginTop: theme.spacing.sm,
    },
    warningText: {
      fontSize: theme.typography.fontSize.sm,
      color: isDark ? "#FCA5A5" : "#DC2626",
      lineHeight: 18,
    },
    successCard: {
      backgroundColor: isDark ? "rgba(34, 197, 94, 0.1)" : "rgba(34, 197, 94, 0.1)",
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      marginTop: theme.spacing.sm,
    },
    successText: {
      fontSize: theme.typography.fontSize.sm,
      color: isDark ? "#86EFAC" : "#16A34A",
      lineHeight: 18,
    },
    
    // Footer styles
    footer: {
      padding: theme.spacing.md,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      backgroundColor: isDark ? "#000" : "#FFF",
    },
    saveButton: {
      borderRadius: theme.borderRadius.lg,
      paddingVertical: theme.spacing.md,
      alignItems: "center",
      justifyContent: "center",
      minHeight: 50,
    },
    saveButtonDisabled: {
      opacity: 0.6,
    },
    saveButtonText: {
      color: "white",
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
    },
  });
};
