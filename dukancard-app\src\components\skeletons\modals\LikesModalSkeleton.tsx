import React from "react";
import { View } from "react-native";
import { SkeletonLoader } from "@/src/components/ui/SkeletonLoader";
import { useTheme } from "@/src/hooks/useTheme";

export const LikesModalSkeleton = () => {
  const theme = useTheme();
  const { isDark } = theme;

  return (
    <View>
      {/* Like List Item Skeletons */}
      {Array.from({ length: 8 }).map((_, index) => (
        <View key={index}>
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              paddingHorizontal: theme.spacing.md,
              paddingVertical: theme.spacing.sm,
              minHeight: 72,
            }}
          >
            {/* Avatar Skeleton */}
            <SkeletonLoader height={44} width={44} borderRadius={22} />

            {/* Content Skeleton */}
            <View style={{ marginLeft: theme.spacing.md, flex: 1 }}>
              <SkeletonLoader
                height={18}
                width="70%"
                borderRadius={theme.borderRadius.sm}
                style={{ marginBottom: theme.spacing.xs }}
              />
              <SkeletonLoader
                height={14}
                width="50%"
                borderRadius={theme.borderRadius.sm}
                style={{ marginBottom: theme.spacing.xs }}
              />
              <SkeletonLoader
                height={12}
                width="30%"
                borderRadius={theme.borderRadius.sm}
              />
            </View>

            {/* Action Button Skeleton */}
            <SkeletonLoader height={36} width={36} borderRadius={18} />
          </View>

          {/* Separator */}
          {index < 7 && (
            <View
              style={{
                height: 1,
                backgroundColor: isDark ? "#333" : "#f0f0f0",
                marginLeft: 72, // Align with text content
              }}
            />
          )}
        </View>
      ))}
    </View>
  );
};
