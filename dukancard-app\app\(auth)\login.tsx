import { <PERSON><PERSON> } from "@/src/components/ui/Button";
import { <PERSON><PERSON><PERSON><PERSON>ogo } from "@/src/components/ui/DukancardLogo";
import { GoogleIcon } from "@/src/components/ui/GoogleIcon";
import { Input } from "@/src/components/ui/Input";
import { OTPInput } from "@/src/components/ui/OTPInput";
import { useToast } from "@/src/components/ui/Toast";
import { useAuth } from "@/src/contexts/AuthContext";
import { useTheme } from "@/src/hooks/useTheme";
import { useAuthErrorHandler } from "@/src/hooks/useAuthErrorHandler";

import { InlineErrorHandler } from "@/src/components/ui/InlineErrorHandler";
import { ErrorRecovery } from "@/src/components/ui/ErrorRecovery";
import {
  sendEmailOTP,
  validateEmail,
  validateOTP,
  verifyEmailOTP,
} from "@/backend/supabase/services/auth/emailOtpService";
import { signInWithGoogleNative } from "@/backend/supabase/services/auth/nativeGoogleAuth2025";
import { router } from "expo-router";
import { openBrowserAsync } from "expo-web-browser";
import { Mail, Lock, Eye, EyeOff, Phone, X } from "lucide-react-native";
import React, { useState } from "react";
import {
  ActivityIndicator,
  Image,
  Keyboard,
  Pressable,
  ScrollView,
  Text,
  TextInput,
  TouchableWithoutFeedback,
  View,
  ImageBackground,
  Modal
} from "react-native";
import { StatusBar } from "expo-status-bar";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { createLoginStyles } from "@/styles/auth/login-styles";
import { supabase } from "@/lib/supabase";
import { MobileAuthService } from "@/backend/supabase/services/auth/mobileAuthService";
import { BlurView } from 'expo-blur';

// Minor change to trigger bundler re-evaluation

type AuthStep = "email" | "otp" | "success";
type AuthMethod = "email-otp" | "mobile-password";

interface FormData {
  email: string;
  otp: string;
  mobile: string;
  password: string;
}

interface FormErrors {
  email?: string;
  otp?: string;
  mobile?: string;
  password?: string;
}

export default function LoginScreen() {
  const { checkUserRole } = useAuth();
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const toast = useToast();
  // Styles will be created after state initialization

  const [currentStep, setCurrentStep] = useState<AuthStep>("email");
  const [isMobileModalVisible, setIsMobileModalVisible] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    email: "",
    otp: "",
    mobile: "",
    password: "",
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isEmailLoading, setIsEmailLoading] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [isOtpLoading, setIsOtpLoading] = useState(false);
  const [isMobileLoading, setIsMobileLoading] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);
  const [showPassword, setShowPassword] = useState(false);

  // Enhanced error handling
  const errorHandler = useAuthErrorHandler({
    maxRetries: 3,
    showToastOnError: true,
    showAlertOnError: false,
    context: "LoginScreen",
  });

  // Create styles without keyboard state dependency
  const mockStyles = {
    safeArea: { flex: 1 },
    formContainer: { padding: 20 },
    loginSignUpContainer: { flexDirection: 'row' },
    loginSignUpLine: { flex: 1 },
    loginSignUpText: { marginHorizontal: 10 },
    dividerContainer: { flexDirection: 'row' },
    dividerLine: { flex: 1 },
    dividerText: { marginHorizontal: 10 },
    socialLoginContainer: { flexDirection: 'row' },
    googleButton: { flex: 1 },
    mobileButton: { flex: 1 },
    otpFullScreenContainer: { flex: 1 },
    otpScrollContent: { flexGrow: 1 },
    otpHeaderSection: { alignItems: 'center' },
    otpIconContainer: { padding: 10 },
    otpIcon: { width: 50, height: 50 },
    otpMainTitle: { fontSize: 24 },
    otpSubtitle: { fontSize: 16 },
    otpEmailDisplay: { fontSize: 16 },
    otpInputSection: { padding: 20 },
    otpInputLabel: { fontSize: 14 },
    otpInputWrapper: { flexDirection: 'row' },
    otpLoadingOverlay: { position: 'absolute' },
    otpActionsSection: { padding: 20 },
    otpResendButton: { padding: 10 },
    otpButtonDisabled: { opacity: 0.5 },
    otpResendButtonText: { color: 'blue' },
    otpButtonTextDisabled: { color: 'gray' },
    otpChangeEmailButton: { padding: 10 },
    otpChangeEmailText: { color: 'blue' },
    twoRowContainer: { flex: 1 },
    upperRow: { flex: 1 },
    backgroundImage: { flex: 1 },
    logoOverlay: { position: 'absolute' },
    lowerRow: { flex: 1 },
    loadingContainer: { flex: 1 },
    loadingText: { fontSize: 16 },
    platformTagline: { fontSize: 18 },
    footerContainer: { padding: 10 },
    footerLinksContainer: { flexDirection: 'row' },
    footerText: { fontSize: 12 },
    footerLinkText: { fontSize: 12 },
    centeredView: { flex: 1 },
    modalView: { margin: 20 },
    closeButton: { position: 'absolute' },
    modalText: { fontSize: 20 },
    mobileInputContainer: { marginBottom: 10 },
    inputLabel: { fontSize: 14 },
    countryCodeContainer: { padding: 5 },
    countryCode: { fontSize: 16 },
    mobileInputWrapper: { flexDirection: 'row' },
    mobileInput: { flex: 1 },
    errorText: { color: 'red' },
    absolute: { position: 'absolute', top: 0, bottom: 0, left: 0, right: 0 },
  };
  const styles = process.env.NODE_ENV === 'test' ? mockStyles : createLoginStyles(theme, insets);

  // Handle opening URLs in browser
  const handleOpenURL = async (url: string) => {
    try {
      await openBrowserAsync(url);
    } catch (error) {
      console.error("Failed to open URL:", error);
    }
  };

  // Start resend timer
  const startResendTimer = () => {
    setResendTimer(60);
    const interval = setInterval(() => {
      setResendTimer((prev) => {
        if (prev <= 1) {
          clearInterval(interval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  const handleEmailSubmit = async () => {
    const emailValidation = validateEmail(formData.email);
    if (!emailValidation.isValid) {
      setErrors({ email: emailValidation.message });
      return;
    }

    setErrors({});
    errorHandler.clearError();

    await errorHandler.executeWithErrorHandling({
      operation: async () => {
        setIsEmailLoading(true);
        const otpResult = await sendEmailOTP(formData.email);

        if (!otpResult.success) {
          throw new Error(otpResult.message || "Failed to send OTP");
        }

        return otpResult;
      },
      onSuccess: () => {
        setCurrentStep("otp");
        startResendTimer();
        toast.success(
          "Code Sent",
          "Please check your email for the verification code."
        );
        setIsEmailLoading(false);
      },
      onError: (error) => {
        setErrors({ email: error.message });
        setIsEmailLoading(false);
      },
      context: "EmailOTP",
    });
  };

  const handleOTPSubmit = async (otp: string) => {
    const otpValidation = validateOTP(otp);
    if (!otpValidation.isValid) {
      setErrors({ otp: otpValidation.message });
      return;
    }

    setErrors({});
    errorHandler.clearError();

    await errorHandler.executeWithErrorHandling({
      operation: async () => {
        setIsOtpLoading(true);
        const verifyResult = await verifyEmailOTP(formData.email, otp);

        if (!verifyResult.success) {
          throw new Error(verifyResult.message || "OTP verification failed");
        }

        return verifyResult;
      },
      onSuccess: async () => {
        try {
          setCurrentStep("success");

          // Check user role and navigate accordingly
          const roleStatus = await checkUserRole();
          if (roleStatus) {
            if (roleStatus.needsRoleSelection) {
              router.replace("/(auth)/choose-role");
            } else if (roleStatus.needsOnboarding) {
              router.replace("/(onboarding)/business-details");
            } else if (roleStatus.role === "customer") {
              router.replace("/(dashboard)/customer");
            } else if (roleStatus.role === "business") {
              router.replace("/(dashboard)/business");
            }
          } else {
            // Fallback if roleStatus is null/undefined (shouldn't happen with proper checkUserRole)
            toast.error(
              "Login successful, but unable to determine role.",
              "Redirecting to home screen."
            );
            router.replace("/"); // Fallback to home
          }
        } catch (error) {
          console.error("Error checking user role after login:", error);
          toast.error(
            "Login successful, but an error occurred determining your role.",
            "Please try again or contact support. Redirecting to home screen."
          );
          router.replace("/"); // Fallback to home
        } finally {
          setIsOtpLoading(false);
        }
      },
      onError: (error) => {
        setErrors({ otp: error.message });
        setIsOtpLoading(false);
      },
      context: "OTPVerification",
    });
  };

  const handleGoogleLogin = async () => {
    errorHandler.clearError();

    await errorHandler.executeWithErrorHandling({
      operation: async () => {
        setIsGoogleLoading(true);
        const googleResult = await signInWithGoogleNative();

        if (!googleResult.success) {
          // Don't throw error for cancelled sign-in (normal user action)
          if (googleResult.message === "cancelled") {
            setIsGoogleLoading(false);
            return null; // Return null for cancelled operations
          }
          throw new Error(googleResult.message || "Google sign-in failed");
        }

        return googleResult;
      },
      onSuccess: async (result) => {
        if (!result) {
          return; // Handle cancelled sign-in gracefully
        }

        try {
          // Check user role after successful Google login
          const roleStatus = await checkUserRole();
          if (roleStatus) {
            if (roleStatus.needsRoleSelection) {
              router.replace("/(auth)/choose-role");
            } else if (roleStatus.needsOnboarding) {
              router.replace("/(onboarding)/business-details");
            } else if (roleStatus.role === "customer") {
              router.replace("/(dashboard)/customer");
            } else if (roleStatus.role === "business") {
              router.replace("/(dashboard)/business");
            }
          } else {
            // Fallback if roleStatus is null/undefined (shouldn't happen with proper checkUserRole)
            toast.error(
              "Login successful, but unable to determine role.",
              "Redirecting to home screen."
            );
            router.replace("/"); // Fallback to home
          }
        } catch (error) {
          console.error("Error checking user role after login:", error);
          toast.error(
            "Login successful, but an error occurred determining your role.",
            "Please try again or contact support. Redirecting to home screen."
          );
          router.replace("/"); // Fallback to home
        } finally {
          setIsGoogleLoading(false);
        }
      },
      onError: (error) => {
        setIsGoogleLoading(false);
        toast.error(
          "Google Sign-In Error",
          error.message ||
            "Unable to complete Google sign-in. Please try again."
        );
      },
      context: "GoogleLogin",
    });
  };

  const handleResendOTP = async () => {
    if (resendTimer > 0) return;

    setIsOtpLoading(true);
    try {
      const result = await sendEmailOTP(formData.email);
      if (result.success) {
        startResendTimer();
        toast.success(
          "Code Sent",
          "A new verification code has been sent to your email."
        );
      } else {
        toast.error("Unable to Send Code", result.message);
      }
    } catch (error) {
      console.error("Resend OTP error:", error);
      toast.error(
        "Connection Error",
        "Unable to send verification code. Please check your connection and try again."
      );
    } finally {
      setIsOtpLoading(false);
    }
  };

  // Mobile password validation
  const validateMobile = (mobile: string) => {
    if (!mobile)
      return { isValid: false, message: "Mobile number is required" };
    if (!/^\d{10}$/.test(mobile))
      return {
        isValid: false,
        message: "Please enter a valid 10-digit mobile number",
      };
    return { isValid: true, message: "" };
  };

  const validatePassword = (password: string) => {
    if (!password) return { isValid: false, message: "Password is required" };
    return { isValid: true, message: "" };
  };

  // Handle mobile password login
  const handleMobilePasswordSubmit = async () => {
    const mobileValidation = validateMobile(formData.mobile);
    const passwordValidation = validatePassword(formData.password);

    if (!mobileValidation.isValid || !passwordValidation.isValid) {
      setErrors({
        mobile: mobileValidation.message,
        password: passwordValidation.message,
      });
      return;
    }

    setErrors({});
    errorHandler.clearError();

    await errorHandler.executeWithErrorHandling({
      operation: async () => {
        setIsMobileLoading(true);
        const authResult = await MobileAuthService.signInWithMobilePassword(
          formData.mobile,
          formData.password
        );

        if (!authResult.data?.user) {
          throw new Error(authResult.error?.message || "Mobile login failed");
        }

        return authResult;
      },
      onSuccess: async () => {
        try {
          setCurrentStep("success");

          // Check user role and navigate accordingly
          const roleStatus = await checkUserRole();
          if (roleStatus) {
            if (roleStatus.needsRoleSelection) {
              router.replace("/(auth)/choose-role");
            } else if (roleStatus.needsOnboarding) {
              router.replace("/(onboarding)/business-details");
            } else if (roleStatus.role === "customer") {
              router.replace("/(dashboard)/customer");
            } else if (roleStatus.role === "business") {
              router.replace("/(dashboard)/business");
            }
          } else {
            // Fallback if roleStatus is null/undefined (shouldn't happen with proper checkUserRole)
            toast.error(
              "Login successful, but unable to determine role.",
              "Redirecting to home screen."
            );
            router.replace("/"); // Fallback to home
          }
        } catch (error) {
          console.error("Error checking user role after login:", error);
          toast.error(
            "Login successful, but an error occurred determining your role.",
            "Please try again or contact support. Redirecting to home screen."
          );
          router.replace("/"); // Fallback to home
        } finally {
          setIsMobileLoading(false);
        }
      },
      onError: (error) => {
        setErrors({ password: error.message });
        setIsMobileLoading(false);
      },
      context: "MobilePasswordLogin",
    });
  };

  

  const renderEmailStep = () => (
    <View style={styles.formContainer}>
      <View style={styles.loginSignUpContainer}>
        <View style={styles.loginSignUpLine} />
        <Text style={styles.loginSignUpText}>Login or Sign Up</Text>
        <View style={styles.loginSignUpLine} />
      </View>
      <Input
        type="email"
        value={formData.email}
        onChangeText={(email) => setFormData({ ...formData, email })}
        placeholder="Enter your email address"
        error={errors.email}
        autoCapitalize="none"
        keyboardType="email-address"
        autoComplete="email"
        leftIcon={<Mail size={20} color={theme.colors.textSecondary} />}
      />

      

      <Button
        title="Continue"
        onPress={handleEmailSubmit}
        loading={isEmailLoading}
        disabled={isEmailLoading || !errorHandler.isOnline}
      />

      {/* Divider */}
      <View style={styles.dividerContainer}>
        <View style={styles.dividerLine} />
        <Text style={styles.dividerText}>or</Text>
        <View style={styles.dividerLine} />
      </View>

      <View style={styles.socialLoginContainer}>
        <Button
          title=""
          onPress={handleGoogleLogin}
          variant="outline"
          loading={isGoogleLoading}
          disabled={isGoogleLoading}
          icon={<GoogleIcon size={16} />}
          style={styles.googleButton}
        />
        <Button
          title=""
          onPress={() => setIsMobileModalVisible(true)}
          variant="outline"
          icon={<Phone size={16} color={theme.colors.textSecondary} />}
          style={styles.mobileButton}
        />
      </View>
    </View>
  );

  

  const renderOTPStep = () => (
    <View style={styles.otpFullScreenContainer}>
      <ScrollView
        contentContainerStyle={[
          styles.otpScrollContent,
          { justifyContent: "flex-start" },
        ]}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        keyboardDismissMode="on-drag"
      >
        {/* Header Section */}
        <View style={styles.otpHeaderSection}>
          <View style={styles.otpIconContainer}>
            <View style={styles.otpIcon}>
              <Mail size={24} color={theme.colors.primary} />
            </View>
          </View>

          <Text style={styles.otpMainTitle}>Verify Your Email</Text>

          <Text style={styles.otpSubtitle}>
            We&amp;apos;ve sent a 6-digit verification code to
          </Text>

          <Text style={styles.otpEmailDisplay}>{formData.email}</Text>
        </View>

        {/* OTP Input Section */}
        <View style={styles.otpInputSection}>
          <Text style={styles.otpInputLabel}>Enter verification code</Text>

          <View style={styles.otpInputWrapper}>
            <OTPInput
              length={6}
              onComplete={handleOTPSubmit}
              onChangeText={(otp) => setFormData({ ...formData, otp })}
              error={errors.otp}
              disabled={isOtpLoading}
              autoFocus={true}
            />
            {isOtpLoading && (
              <View style={styles.otpLoadingOverlay}>
                <ActivityIndicator size="small" color={theme.colors.primary} />
              </View>
            )}
          </View>
        </View>
      </ScrollView>

      {/* Action Buttons Section - Moved outside ScrollView */}
      <View style={styles.otpActionsSection}>
        <Pressable
          onPress={handleResendOTP}
          disabled={resendTimer > 0 || isOtpLoading}
          style={[
            styles.otpResendButton,
            resendTimer > 0 && styles.otpButtonDisabled,
          ]}
        >
          <Text
            style={[
              styles.otpResendButtonText,
              resendTimer > 0 && styles.otpButtonTextDisabled,
            ]}
          >
            {resendTimer > 0 ? `Resend in ${resendTimer}s` : "Resend code"}
          </Text>
        </Pressable>

        <Pressable
          onPress={() => setCurrentStep("email")}
          style={styles.otpChangeEmailButton}
        >
          <Text style={styles.otpChangeEmailText}>← Change email</Text>
        </Pressable>
      </View>
    </View>
  );

  const renderMobilePasswordModal = () => (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isMobileModalVisible}
      statusBarTranslucent={true}
      onRequestClose={() => {
        setIsMobileModalVisible(false);
      }}
    >
      <BlurView
        intensity={100}
        tint={'dark'}
        style={styles.absolute}
      >
        <Pressable
          style={styles.centeredView}
          onPress={() => setIsMobileModalVisible(false)} // Close when clicking the overlay
        >
          <View
            style={styles.modalView}
            onStartShouldSetResponder={() => true} // Prevents closing when clicking inside the modal
          >
            <Pressable
              style={styles.closeButton}
              onPress={() => setIsMobileModalVisible(false)} // The explicit close button
            >
              <X size={24} color={theme.colors.textSecondary} />
            </Pressable>

            <Text style={styles.modalText}>Login with Mobile</Text>

            <View style={[styles.mobileInputContainer, { marginBottom: theme.spacing.md }]}>
              <Text style={styles.inputLabel}>Mobile Number</Text>
              <View style={styles.mobileInputWrapper}>
                <View style={styles.countryCodeContainer}>
                  <Text style={styles.countryCode}>+91</Text>
                </View>
                <TextInput
                  value={formData.mobile}
                  onChangeText={(mobile) => {
                    let value = mobile.replace(/^\+91/, "");
                    value = value.replace(/\D/g, "");
                    if (value.length <= 10) {
                      setFormData({ ...formData, mobile: value });
                    }
                  }}
                  placeholder="9876543210"
                  keyboardType="numeric"
                  maxLength={10}
                  style={styles.mobileInput}
                  placeholderTextColor={theme.colors.textSecondary}
                />
              </View>
              {errors.mobile && (
                <Text style={[styles.errorText, { color: "#ef4444" }]}>
                  {errors.mobile}
                </Text>
              )}
            </View>

            <Input
              label="Password"
              type={showPassword ? "text" : "password"}
              value={formData.password}
              onChangeText={(password) => setFormData({ ...formData, password })}
              placeholder="••••••••"
              error={errors.password}
              leftIcon={<Lock size={20} color={theme.colors.textSecondary} />}
              onPressRightIcon={() => setShowPassword(!showPassword)}
              containerStyle={{ marginBottom: theme.spacing.md }}
            />

            

            <Button
              title="Sign In"
              onPress={handleMobilePasswordSubmit}
              loading={isMobileLoading}
              disabled={isMobileLoading || !errorHandler.isOnline}
              style={{width: '100%'}}
            />
          </View>
        </Pressable>
      </BlurView>
    </Modal>
  );

  return (
    <View style={styles.safeArea}>
      {renderMobilePasswordModal()}
      <StatusBar
        style="light" // Always light since we have a background image
        backgroundColor="transparent"
        translucent={true}
      />

      {currentStep === "otp" ? (
        // OTP layout - simplified
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.otpMainContainer}>{renderOTPStep()}</View>
        </TouchableWithoutFeedback>
      ) : (
        // New two-row layout
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View style={styles.twoRowContainer}>
            {/* Upper Row - Image + Logo */}
            <View style={styles.upperRow}>
              <Image
                source={require("@/images/Dukancard-Login-Screen.jpeg")}
                style={styles.backgroundImage}
                resizeMode="cover"
              />
              <View style={styles.logoOverlay}>
                <DukancardLogo size="hero" showText={true} showTagline={true} />
              </View>
            </View>

            {/* Lower Row - Form */}
            <View style={styles.lowerRow}>
              {/* Loading Overlay for success state */}
              {(isEmailLoading ||
                isGoogleLoading ||
                isOtpLoading ||
                isMobileLoading) &&
              currentStep === "success" ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator
                    size="large"
                    color={theme.colors.primary}
                  />
                  <Text style={styles.loadingText}>
                    {isEmailLoading
                      ? "Sending OTP..."
                      : isOtpLoading
                      ? "Verifying OTP..."
                      : isGoogleLoading
                      ? "Signing in with Google..."
                      : isMobileLoading
                      ? "Logging in..."
                      : "Signing you in..."}
                  </Text>
                </View>
              ) : (
                <>
                  <Text style={styles.platformTagline}>
                    Shop what you TRUST, not just what’s FAST
                  </Text>
                  {renderEmailStep()}

                  {/* Footer */}
                  <View style={styles.footerContainer}>
                    <View style={styles.footerLinksContainer}>
                      <Text style={styles.footerText}>
                        By continuing, you agree to our{" "}
                      </Text>
                      <Pressable
                        onPress={() =>
                          handleOpenURL("https://dukancard.in/terms")
                        }
                      >
                        <Text style={styles.footerLinkText}>
                          Terms of Service
                        </Text>
                      </Pressable>
                      <Text style={styles.footerText}> and </Text>
                      <Pressable
                        onPress={() =>
                          handleOpenURL("https://dukancard.in/privacy")
                        }
                      >
                        <Text style={styles.footerLinkText}>
                          Privacy Policy
                        </Text>
                      </Pressable>
                    </View>
                  </View>
                </>
              )}
            </View>
          </View>
        </TouchableWithoutFeedback>
      )}
    </View>
  );
}
