'use client';

import { useMemo } from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Compass, Heart } from 'lucide-react';
import { LikeCard, LikeData } from '@/app/components/shared/likes';
import { BusinessLikeReceived } from '../actions';

interface BusinessLikesReceivedListProps {
  initialLikes: BusinessLikeReceived[];
}

export default function BusinessLikesReceivedList({ initialLikes }: BusinessLikesReceivedListProps) {
  // Transform the data to match the shared component interface
  const transformedLikes: LikeData[] = useMemo(() => {
    return initialLikes.map(like => {
      if (like.profile_type === 'customer' && like.customer_profiles) {
        return {
          id: like.id,
          profile: {
            id: like.customer_profiles.id,
            name: like.customer_profiles.name,
            slug: null, // Customers don't have slugs
            avatar_url: like.customer_profiles.avatar_url,
            city: null,
            state: null,
            pincode: null,
            address_line: null,
            type: 'customer' as const,
          }
        };
      } else if (like.profile_type === 'business' && like.business_profiles) {
        return {
          id: like.id,
          profile: {
            id: like.business_profiles.id,
            name: like.business_profiles.business_name,
            slug: like.business_profiles.business_slug,
            logo_url: like.business_profiles.logo_url,
            city: like.business_profiles.city,
            state: like.business_profiles.state,
            pincode: like.business_profiles.pincode,
            address_line: like.business_profiles.address_line,
            type: 'business' as const,
          }
        };
      }
      return null;
    }).filter(like => like !== null && like.profile !== null) as LikeData[];
  }, [initialLikes]);

  // Enhanced empty state for likes received
  if (transformedLikes.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-20 text-center">
        <div className="relative mb-8">
          <div className="absolute -inset-8 rounded-full bg-gradient-to-r from-primary/10 to-primary/5 animate-pulse blur-xl"></div>
          <div className="relative z-10 flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20 shadow-lg">
            <Heart className="w-10 h-10 text-primary" />
          </div>
        </div>
        <h3 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-3">
          No likes received yet
        </h3>
        <p className="text-lg text-neutral-600 dark:text-neutral-400 max-w-lg leading-relaxed mb-2">
          When customers and businesses appreciate your services, their likes will appear here.
        </p>
        <p className="text-sm text-neutral-500 dark:text-neutral-500 max-w-md leading-relaxed mb-8">
          Share your business card to start receiving likes and building your reputation.
        </p>
        <Button asChild variant="outline" className="gap-2 px-6 py-2.5 h-auto font-medium shadow-sm hover:shadow-md transition-all duration-200">
          <Link href="/businesses" target="_blank" rel="noopener noreferrer">
            <Compass className="w-4 h-4" />
            Discover Businesses
          </Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {transformedLikes.map((like, _index) => {
        const profile = like.profile;

        if (!profile) {
          return null; // Skip items with missing profiles
        }

        return (
          <div key={like.id} className="transform transition-all duration-200 hover:scale-[1.02]">
            <LikeCard
              likeId={like.id}
              profile={profile}
              showUnlike={false} // Don't show unlike for likes received
              variant="default"
              showVisitButton={false} // Don't show visit button for likes received
              showAddress={false} // Don't show address for likes received
              showRedirectIcon={true} // Show redirect icon for businesses
            />
          </div>
        );
      })}
    </div>
  );
}
