// Mock for use-intl
export const useTranslations = jest.fn(() => (key) => key);
export const useFormatter = jest.fn(() => ({
  dateTime: jest.fn((date) => date.toString()),
  number: jest.fn((num) => num.toString()),
  relativeTime: jest.fn((date) => date.toString()),
}));
export const useLocale = jest.fn(() => 'en');
export const useNow = jest.fn(() => new Date());
export const useTimeZone = jest.fn(() => 'UTC');
