/**
 * Compact Location Picker Component for Discovery Screen
 * Shows selected location in a compact view, clicking opens full-screen location selector
 */

import React, { useState, useRef } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  Modal,
} from "react-native";
import { MapPin, ChevronRight } from "lucide-react-native";
import { useColorScheme } from "@/src/hooks/useColorScheme";
import { LocationData } from "@/src/types/discovery";
import { createCompactLocationPickerStyles } from "./styles/CompactLocationPickerStyles";
import { FullScreenLocationSelector } from "./FullScreenLocationSelector";
import LocalityBottomSheetPicker, {
  LocalityBottomSheetPickerRef,
} from "@/src/components/pickers/LocalityBottomSheetPicker";

export interface CompactLocationPickerProps {
  location?: LocationData;
  onLocationChange: (location: LocationData) => void;
  onError?: (error: string) => void;
  disabled?: boolean;
  isLoading?: boolean;
  colors: any; // Add colors prop
}

export function CompactLocationPicker({
  location,
  onLocationChange,
  onError,
  disabled = false,
  isLoading = false,
  colors,
}: CompactLocationPickerProps) {
  const styles = createCompactLocationPickerStyles();
  const [showLocationSelector, setShowLocationSelector] = useState(false);

  // Theme colors
  const backgroundColor = colors.background;
  const borderColor = colors.border;
  const textColor = colors.textPrimary;
  const secondaryTextColor = colors.textSecondary;
  const primaryColor = colors.primary; // Gold brand color

  // Format location display text - locality first, then pincode, then city
  const getLocationDisplayText = () => {
    if (!location) {
      return "Select Location";
    }

    const parts = [];

    // Add locality first
    if (location.locality) {
      parts.push(location.locality);
    }

    // Add pincode second
    if (location.pincode) {
      parts.push(location.pincode);
    }

    // Add city last
    if (location.city) {
      parts.push(location.city);
    }

    return parts.length > 0 ? parts.join(", ") : "Select Location";
  };

  const getLocationSubtext = () => {
    if (!location) {
      return "Tap to choose your location";
    }

    if (location.city && location.pincode) {
      return "Tap to change location";
    }

    return "Tap to complete location details";
  };

  const handlePress = () => {
    if (disabled || isLoading) return;
    setShowLocationSelector(true);
  };

  const handleLocationSelect = (selectedLocation: LocationData) => {
    onLocationChange(selectedLocation);
  };

  const handleCloseModal = () => {
    setShowLocationSelector(false);
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        { backgroundColor, borderColor },
        disabled && styles.disabled,
      ]}
      onPress={handlePress}
      disabled={disabled || isLoading}
      activeOpacity={0.7}
    >
      <View style={styles.content}>
        {/* Location Icon */}
        <View
          style={[styles.iconContainer, { backgroundColor: colors.background }]}
        >
          {isLoading ? (
            <ActivityIndicator
              size="small"
              color={primaryColor}
              testID="activity-indicator"
            />
          ) : (
            <MapPin size={20} color={primaryColor} />
          )}
        </View>

        {/* Location Text */}
        <View style={styles.textContainer}>
          <Text
            style={[
              styles.locationText,
              { color: textColor },
              !location && styles.placeholderText,
            ]}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {getLocationDisplayText()}
          </Text>
          <Text
            style={[styles.subtextText, { color: secondaryTextColor }]}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {getLocationSubtext()}
          </Text>
        </View>

        {/* Chevron Icon */}
        <View style={styles.chevronContainer}>
          <ChevronRight size={18} color={secondaryTextColor} />
        </View>
      </View>

      {/* Full-Screen Location Selector Modal */}
      <FullScreenLocationSelector
        visible={showLocationSelector}
        onClose={handleCloseModal}
        onLocationSelect={handleLocationSelect}
        initialLocation={location}
      />
    </TouchableOpacity>
  );
}
