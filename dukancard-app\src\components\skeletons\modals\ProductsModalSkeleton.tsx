import React from "react";
import { View, StyleSheet } from "react-native";
import { useTheme } from "@/src/hooks/useTheme";

const ProductItemSkeleton = ({ theme }: { theme: ReturnType<typeof useTheme> }) => {
  const { isDark } = theme;

  const styles = StyleSheet.create({
    productItem: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      minHeight: 72,
      backgroundColor: "transparent",
    },
    imageSkeleton: {
      width: 50,
      height: 50,
      borderRadius: 25,
      backgroundColor: isDark ? "#333" : "#E5E5E5",
      marginRight: theme.spacing.sm,
    },
    contentSkeleton: {
      flex: 1,
      marginRight: theme.spacing.sm,
    },
    titleSkeleton: {
      height: 16,
      backgroundColor: isDark ? "#333" : "#E5E5E5",
      borderRadius: 4,
      marginBottom: 4,
      width: "80%",
    },
    descriptionSkeleton: {
      height: 12,
      backgroundColor: isDark ? "#333" : "#E5E5E5",
      borderRadius: 4,
      marginBottom: 6,
      width: "60%",
    },
    priceSkeleton: {
      height: 14,
      backgroundColor: isDark ? "#333" : "#E5E5E5",
      borderRadius: 4,
      marginBottom: 4,
      width: "40%",
    },
    metaSkeleton: {
      flexDirection: "row",
      gap: theme.spacing.sm,
    },
    tagSkeleton: {
      height: 12,
      width: 50,
      backgroundColor: isDark ? "#333" : "#E5E5E5",
      borderRadius: 4,
    },
    actionsSkeleton: {
      flexDirection: "row",
      gap: theme.spacing.sm,
      position: "absolute",
      bottom: theme.spacing.sm,
      right: theme.spacing.md,
    },
    actionButtonSkeleton: {
      width: 32,
      height: 32,
      backgroundColor: isDark ? "#333" : "#E5E5E5",
      borderRadius: theme.borderRadius.md,
    },
  });

  return (
    <View style={styles.productItem}>
      <View style={styles.imageSkeleton} />
      <View style={styles.contentSkeleton}>
        <View style={styles.titleSkeleton} />
        <View style={styles.descriptionSkeleton} />
        <View style={styles.priceSkeleton} />
        <View style={styles.metaSkeleton}>
          <View style={styles.tagSkeleton} />
          <View style={styles.tagSkeleton} />
        </View>
      </View>
      <View style={styles.actionsSkeleton}>
        <View style={styles.actionButtonSkeleton} />
        <View style={styles.actionButtonSkeleton} />
      </View>
    </View>
  );
};

export const ProductsModalSkeleton = () => {
  const theme = useTheme();
  const { isDark } = theme;

  const styles = StyleSheet.create({
    container: {
      flex: 1,
    },
    searchSkeleton: {
      height: 48,
      backgroundColor: isDark ? "#333" : "#E5E5E5",
      borderRadius: theme.borderRadius.md,
      marginHorizontal: theme.spacing.md,
      marginTop: theme.spacing.md,
      marginBottom: theme.spacing.md,
    },
    actionsSkeleton: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      paddingHorizontal: theme.spacing.md,
      marginBottom: theme.spacing.md,
      gap: theme.spacing.sm,
    },
    addButtonSkeleton: {
      flex: 1,
      height: 44,
      backgroundColor: isDark ? "#333" : "#E5E5E5",
      borderRadius: theme.borderRadius.lg,
    },
    sortButtonSkeleton: {
      width: 44,
      height: 44,
      backgroundColor: isDark ? "#333" : "#E5E5E5",
      borderRadius: theme.borderRadius.lg,
    },
    separator: {
      height: 1,
      backgroundColor: theme.colors.border,
      marginLeft: 62, // Align with text content (image width + margin)
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.searchSkeleton} />
      <View style={styles.actionsSkeleton}>
        <View style={styles.addButtonSkeleton} />
        <View style={styles.sortButtonSkeleton} />
      </View>
      {Array.from({ length: 8 }).map((_, index) => (
        <View key={index}>
          <ProductItemSkeleton theme={theme} />
          {index < 7 && <View style={styles.separator} />}
        </View>
      ))}
    </View>
  );
};
