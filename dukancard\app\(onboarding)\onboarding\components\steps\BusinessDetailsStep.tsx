import React from "react";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Building2, Mail } from "lucide-react";
import { StepComponentProps } from "../../types/onboarding";

export function BusinessDetailsStep({ form, isSubmitting, user }: StepComponentProps) {
  const isGoogleUser = user?.app_metadata?.provider === 'google';
  const isEmailUser = user?.app_metadata?.provider === 'email';
  const isMobileUser = user?.app_metadata?.provider === 'phone';

  return (
    <>
      <FormField
        control={form.control}
        name="businessName"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="text-foreground flex items-center gap-2">
              <Building2 className="w-4 h-4 text-primary dark:text-[var(--brand-gold)]" />
              Business Name
            </FormLabel>
            <FormControl>
              <div className="relative">
                <Input
                  placeholder="My Awesome Business"
                  {...field}
                  className="bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 pl-10 h-12 text-foreground transition-all rounded-lg"
                  disabled={isSubmitting}
                />
                <Building2 className="absolute left-3 top-3.5 w-5 h-5 text-muted-foreground dark:text-neutral-400" />
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="email"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="text-foreground flex items-center gap-2">
              <Mail className="w-4 h-4 text-primary dark:text-[var(--brand-gold)]" />
              Contact Email
              {isGoogleUser && (
                <span className="text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full">
                  Google Account
                </span>
              )}
              {isEmailUser && (
                <span className="text-xs bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-2 py-1 rounded-full">
                  Email Account
                </span>
              )}
              {isMobileUser && (
                <span className="text-xs bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 px-2 py-1 rounded-full">
                  Mobile Account
                </span>
              )}
            </FormLabel>
            <FormControl>
              <div className="relative">
                <Input
                  placeholder="<EMAIL>"
                  type="email"
                  {...field}
                  className="bg-background border-border focus-visible:ring-primary/20 dark:bg-neutral-800/50 dark:border-neutral-700 dark:focus-visible:ring-[var(--brand-gold)]/20 dark:focus-visible:border-[var(--brand-gold)]/60 pl-10 h-12 text-foreground transition-all rounded-lg"
                  disabled={isSubmitting}
                />
                <Mail className="absolute left-3 top-3.5 w-5 h-5 text-muted-foreground dark:text-neutral-400" />
              </div>
            </FormControl>

            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );
}
