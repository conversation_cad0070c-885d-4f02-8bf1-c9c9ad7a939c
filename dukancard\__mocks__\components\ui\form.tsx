import React from 'react';

export const FormControl = ({ children }: { children: React.ReactNode }) => <div data-testid="form-control">{children}</div>;
export const FormField = ({ render }: { render: ({ field }: any) => React.ReactNode }) => {
  const field = { value: '', onChange: jest.fn(), onBlur: jest.fn(), name: '' }; // Mock field object
  return <div data-testid="form-field">{render({ field })}</div>;
};
export const FormItem = ({ children }: { children: React.ReactNode }) => <div data-testid="form-item">{children}</div>;
export const FormLabel = ({ children }: { children: React.ReactNode }) => <label data-testid="form-label">{children}</label>;
export const FormMessage = ({ children }: { children: React.ReactNode }) => <div data-testid="form-message">{children}</div>;
