import React from "react";
import { View } from "react-native";
import { SkeletonLoader } from "@/src/components/ui/SkeletonLoader";
import { useTheme } from "@/src/hooks/useTheme";

export const ReviewsModalSkeleton = () => {
  const theme = useTheme();
  const { isDark } = theme;

  return (
    <View>
      {/* Review List Item Skeletons */}
      {Array.from({ length: 6 }).map((_, index) => (
        <View key={index}>
          {/* Business Header */}
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              paddingHorizontal: theme.spacing.md,
              paddingVertical: theme.spacing.sm,
              borderBottomWidth: 1,
              borderBottomColor: isDark ? "#333" : "#f0f0f0",
            }}
          >
            {/* Avatar Skeleton */}
            <SkeletonLoader height={44} width={44} borderRadius={22} />

            {/* Content Skeleton */}
            <View style={{ marginLeft: theme.spacing.md, flex: 1 }}>
              <SkeletonLoader
                height={18}
                width="60%"
                borderRadius={theme.borderRadius.sm}
                style={{ marginBottom: theme.spacing.xs }}
              />
              <SkeletonLoader
                height={14}
                width="45%"
                borderRadius={theme.borderRadius.sm}
              />
            </View>

            {/* Action Buttons Skeleton */}
            <View style={{ flexDirection: "row", gap: 8 }}>
              <SkeletonLoader height={34} width={34} borderRadius={17} />
              <SkeletonLoader height={34} width={34} borderRadius={17} />
            </View>
          </View>

          {/* Review Content */}
          <View style={{ paddingHorizontal: theme.spacing.md, paddingVertical: theme.spacing.sm }}>
            {/* Rating Skeleton */}
            <SkeletonLoader
              height={20}
              width="25%"
              borderRadius={theme.borderRadius.sm}
              style={{ marginBottom: theme.spacing.sm }}
            />
            {/* Review Text Skeleton */}
            <SkeletonLoader
              height={14}
              width="100%"
              borderRadius={theme.borderRadius.sm}
              style={{ marginBottom: theme.spacing.xs }}
            />
            <SkeletonLoader
              height={14}
              width="75%"
              borderRadius={theme.borderRadius.sm}
            />
          </View>

          {/* Separator */}
          {index < 5 && (
            <View
              style={{
                height: 1,
                backgroundColor: isDark ? "#333" : "#f0f0f0",
                marginLeft: 72, // Align with text content
              }}
            />
          )}
        </View>
      ))}
    </View>
  );
};
