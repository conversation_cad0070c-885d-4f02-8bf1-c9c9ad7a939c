import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from "react-native";
import { Controller, useFormContext } from "react-hook-form";
import { useTheme } from "@/src/hooks/useTheme";
import { FormField } from "@/src/components/ui/FormField";
import { useToast } from "@/src/components/ui/Toast";
import { updateContactLocation } from "@/backend/supabase/services/business/businessCardService";
import { ContactLocationData, contactLocationSchema } from "@/backend/supabase/services/business/schemas";
import { createContactLocationSectionStyles } from "@/styles/modals/business/sections/contact-location-section";

interface ContactLocationSectionProps {
  onBack: () => void;
}

export default function ContactLocationSection({ onBack }: ContactLocationSectionProps) {
  const theme = useTheme();
  const styles = createContactLocationSectionStyles(theme);
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const { control, handleSubmit } = useFormContext();

  const onSubmit = async (data: any) => {
    setIsLoading(true);
    
    // Extract only contact location fields
    const contactLocationData: ContactLocationData = {
      phone: data.phone,
      contact_email: data.contact_email,
      whatsapp_number: data.whatsapp_number,
      address_line: data.address_line,
      city: data.city,
      state: data.state,
      pincode: data.pincode,
      locality: data.locality,
    };

    // Validate the data
    const validation = contactLocationSchema.safeParse(contactLocationData);
    if (!validation.success) {
      toast.error("Validation Error", "Please check all required fields.");
      setIsLoading(false);
      return;
    }

    // Convert null values to undefined for API compatibility
    const apiData = {
      ...contactLocationData,
      whatsapp_number: contactLocationData.whatsapp_number || undefined,
    };

    const { success, error } = await updateContactLocation(apiData);
    if (success) {
      toast.success("Success", "Contact information updated successfully.");
      onBack();
    } else {
      toast.error("Error", error || "Failed to update contact information.");
    }
    setIsLoading(false);
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {/* Contact Information Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Contact Information</Text>
          
          <Controller
            control={control}
            name="phone"
            render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
              <FormField
                label="Phone Number *"
                value={value || ""}
                onChangeText={onChange}
                onBlur={onBlur}
                error={error?.message}
                type="phone"
                placeholder="Enter 10-digit phone number"
                maxLength={10}
              />
            )}
          />

          <Controller
            control={control}
            name="contact_email"
            render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
              <FormField
                label="Email Address *"
                value={value || ""}
                onChangeText={onChange}
                onBlur={onBlur}
                error={error?.message}
                type="email"
                placeholder="Enter your email address"
              />
            )}
          />

          <Controller
            control={control}
            name="whatsapp_number"
            render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
              <FormField
                label="WhatsApp Number"
                value={value || ""}
                onChangeText={onChange}
                onBlur={onBlur}
                error={error?.message}
                type="phone"
                placeholder="Enter 10-digit WhatsApp number"
                maxLength={10}
              />
            )}
          />
        </View>

        {/* Address Information Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Address Information</Text>
          
          <Controller
            control={control}
            name="address_line"
            render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
              <FormField
                label="Address Line *"
                value={value || ""}
                onChangeText={onChange}
                onBlur={onBlur}
                error={error?.message}
                placeholder="Enter your complete address"
                multiline
                numberOfLines={2}
              />
            )}
          />

          <Controller
            control={control}
            name="locality"
            render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
              <FormField
                label="Locality / Area *"
                value={value || ""}
                onChangeText={onChange}
                onBlur={onBlur}
                error={error?.message}
                placeholder="Enter locality or area name"
              />
            )}
          />

          <View style={styles.row}>
            <View style={styles.halfWidth}>
              <Controller
                control={control}
                name="city"
                render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
                  <FormField
                    label="City *"
                    value={value || ""}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    error={error?.message}
                    placeholder="Enter city"
                  />
                )}
              />
            </View>
            
            <View style={styles.halfWidth}>
              <Controller
                control={control}
                name="state"
                render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
                  <FormField
                    label="State *"
                    value={value || ""}
                    onChangeText={onChange}
                    onBlur={onBlur}
                    error={error?.message}
                    placeholder="Enter state"
                  />
                )}
              />
            </View>
          </View>

          <Controller
            control={control}
            name="pincode"
            render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
              <FormField
                label="Pincode *"
                value={value || ""}
                onChangeText={onChange}
                onBlur={onBlur}
                error={error?.message}
                type="number"
                placeholder="Enter 6-digit pincode"
                maxLength={6}
              />
            )}
          />
        </View>

        <View style={styles.infoBox}>
          <Text style={styles.infoText}>
            💡 Tip: Accurate address information helps customers find your business easily and improves your visibility in local searches.
          </Text>
        </View>
      </ScrollView>

      {/* Save Button */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.saveButton,
            { backgroundColor: theme.colors.primary },
            isLoading && styles.saveButtonDisabled,
          ]}
          onPress={handleSubmit(onSubmit)}
          disabled={isLoading}
        >
          {isLoading ? (
            <ActivityIndicator color="white" />
          ) : (
            <Text style={styles.saveButtonText}>Save Contact Information</Text>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
}
