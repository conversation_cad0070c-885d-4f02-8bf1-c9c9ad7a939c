import React, { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Animated,
  Easing,
  Modal,
} from "react-native";
import { Loader2, X } from "lucide-react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { useAuth } from "@/src/contexts/AuthContext";
import { useForm, FormProvider } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { customerProfileCompletionSchema } from "@/src/utils/validationSchemas";
import { useLocationPermission } from "@/src/hooks/useLocationPermission";
import { usePincodeDetails } from "@/src/hooks/usePincodeDetails";
import { useToast } from "@/src/components/ui/Toast";
import { useAlertDialog } from "@/src/components/providers/AlertProvider";
import { FormData, InitialFormData } from "@/src/types/profile";
import LoadingOverlay from "@/src/components/common/LoadingOverlay";
import AvatarUploadSection from "@/src/components/profile/AvatarUploadSection";
import PersonalInformationSection from "@/src/components/profile/PersonalInformationSection";
import AddressInformationSection from "@/src/components/profile/AddressInformationSection";
import ImagePickerBottomSheet, {
  ImagePickerBottomSheetRef,
} from "@/src/components/pickers/ImagePickerBottomSheet";
import LocalityBottomSheetPicker, {
  LocalityBottomSheetPickerRef,
} from "@/src/components/pickers/LocalityBottomSheetPicker";
import { createEditProfileModalStyles } from "@/styles/modals/customer/edit-profile-modal";
import {
  uploadAvatarImage,
  openCameraForAvatar,
  openGalleryForAvatar,
  deleteCustomerAvatar,
} from "@/backend/supabase/services/storage/avatarUploadService";
import { compressImageModerateClient } from "@/src/utils/client-image-compression";
import { cleanAddressData } from "@/backend/supabase/utils/addressValidation";
import { getCurrentUser } from "@/lib/auth/customerAuth";
import {
  getCustomerProfileForEdit,
  updateCustomerProfileWithCoordinates,
} from "@/backend/supabase/services/customer/customerProfileService";
import { supabase } from "@/lib/supabase";
import NetInfo from "@react-native-community/netinfo";

interface EditProfileModalProps {
  visible: boolean;
  onClose: () => void;
}

export default function EditProfileModal({
  visible,
  onClose,
}: EditProfileModalProps) {
  const theme = useTheme();
  const { user, refreshProfileStatus } = useAuth();
  const toast = useToast();
  const { confirm, error: showError } = useAlertDialog();
  const styles = createEditProfileModalStyles(theme);

  const [initialFormData, setInitialFormData] = useState<InitialFormData>({
    name: "",
    address: "",
    pincode: "",
    city: "",
    state: "",
    locality: "",
    latitude: undefined,
    longitude: undefined,
    avatarUri: "",
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [isPincodeLoading, setIsPincodeLoading] = useState(false);
  const [availableLocalities, setAvailableLocalities] = useState<string[]>([]);

  const imagePickerRef = useRef<ImagePickerBottomSheetRef | null>(null);
  const localityPickerRef = useRef<LocalityBottomSheetPickerRef | null>(null);
  const scrollViewRef = useRef<ScrollView>(null);

  const spinValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const spinAnimation = Animated.loop(
      Animated.timing(spinValue, {
        toValue: 1,
        duration: 1000,
        easing: Easing.linear,
        useNativeDriver: true,
      })
    );
    if (isLoading) {
      spinAnimation.start();
    } else {
      spinAnimation.stop();
    }
    return () => spinAnimation.stop();
  }, [isLoading, spinValue]);

  const spin = spinValue.interpolate({
    inputRange: [0, 1],
    outputRange: ["0deg", "360deg"],
  });

  const [gpsDetectedLocality, setGpsDetectedLocality] = useState<string | null>(
    null
  );
  const [hasGpsCoordinates, setHasGpsCoordinates] = useState(false);
  const { permission: locationPermission } = useLocationPermission();

  const formMethods = useForm<FormData>({
    resolver: yupResolver(customerProfileCompletionSchema) as any,
    defaultValues: {
      name: "",
      address: "",
      pincode: "",
      city: "",
      state: "",
      locality: "",
      avatarUri: "",
    },
    mode: "onChange",
    reValidateMode: "onChange",
  });

  const { control, handleSubmit, setValue, watch, trigger, formState, reset } =
    formMethods;

  const {
    handlePincodeChange: fetchPincodeDetails,
    isPincodeLoading: pincodeLoading,
    availableLocalities: localities,
  } = usePincodeDetails();

  useEffect(() => {
    setIsPincodeLoading(pincodeLoading);
    setAvailableLocalities(localities);
  }, [pincodeLoading, localities]);

  const loadExistingProfile = React.useCallback(async () => {
    try {
      const currentUser = await getCurrentUser();
      if (!currentUser) {
        onClose();
        return;
      }

      const { data: profile, error } = await getCustomerProfileForEdit(
        currentUser.id
      );
      if (error) {
        console.error("Error loading profile:", error);
        toast.error("Error", "Failed to load profile data");
        return;
      }

      if (profile) {
        const profileData = {
          name: profile.name || "",
          address: profile.address || "",
          pincode: profile.pincode || "",
          city: profile.city || "",
          state: profile.state || "",
          locality: profile.locality || "",
          latitude: profile.latitude || undefined,
          longitude: profile.longitude || undefined,
          avatarUri: profile.avatar_url || "",
        };

        setInitialFormData(profileData);
        reset(profileData);

        if (profile.latitude && profile.longitude) {
          setHasGpsCoordinates(true);
        }

        if (profile.pincode && profile.pincode.length === 6) {
          fetchPincodeDetails(profile.pincode);
        }
      } else {
        const userMetadata = user?.user_metadata;
        if (userMetadata?.full_name) {
          const newInitialData = {
            ...initialFormData,
            name: userMetadata.full_name,
          };
          setInitialFormData(newInitialData);
          reset(newInitialData);
        }
      }
    } catch (error) {
      console.error("Error loading existing profile:", error);
      toast.error("Error", "Failed to load profile data");
    } finally {
      setIsInitialLoading(false);
    }
  }, [reset, fetchPincodeDetails, toast, user, onClose, initialFormData]);

  useEffect(() => {
    if (visible) {
      loadExistingProfile();
    }
  }, [visible, loadExistingProfile]);

  useEffect(() => {
    if (gpsDetectedLocality && availableLocalities.length > 0) {
      setTimeout(() => {
        const matchingLocality = availableLocalities.find(
          (locality) =>
            locality.toLowerCase().trim() ===
            gpsDetectedLocality.toLowerCase().trim()
        );
        if (matchingLocality) {
          setValue("locality", matchingLocality);
          setGpsDetectedLocality(null);
          setTimeout(() => trigger(), 100);
        }
      }, 50);
    }
  }, [availableLocalities, gpsDetectedLocality, setValue, trigger]);

  const handlePincodeInputChange = (value: string) => {
    const cleanedPincode = value.replace(/\D/g, "").substring(0, 6);
    setValue("pincode", cleanedPincode);
    if (cleanedPincode.length === 6) {
      fetchPincodeDetails(cleanedPincode);
    }
  };

  const handleLocationDetected = (latitude: number, longitude: number) => {
    setValue("latitude", latitude);
    setValue("longitude", longitude);
    setHasGpsCoordinates(true);
    trigger(["latitude", "longitude"]);
  };

  const handleAddressDetected = (address: {
    pincode: string;
    city: string;
    state: string;
    locality: string;
  }) => {
    setValue("pincode", address.pincode);
    setValue("city", address.city);
    setValue("state", address.state);
    setGpsDetectedLocality(address.locality);
    setInitialFormData((prev) => ({
      ...prev,
      name: watch("name"), // Preserve the current name from the form
      pincode: address.pincode,
      address: watch("address"),
      city: address.city,
      state: address.state,
      locality: "",
      latitude: watch("latitude"),
      longitude: watch("longitude"),
    }));
    fetchPincodeDetails(address.pincode);
    setTimeout(() => trigger(), 200);
  };

  const handleLocalitySelect = (locality: string) => {
    setValue("locality", locality);
    trigger("locality");
  };

  const handleImageSelect = async (imageUri: string) => {
    try {
      setIsLoading(true);
      const currentUser = await getCurrentUser();
      if (!currentUser) {
        onClose();
        return;
      }
      const response = await fetch(imageUri);
      const blob = await response.blob();
      const originalSize = blob.size;
      const compressionResult = await compressImageModerateClient(
        imageUri,
        originalSize,
        {
          targetSizeKB: 45,
          maxDimension: 400,
          quality: 0.7,
          format: "webp",
        }
      );
      if (!compressionResult.uri) {
        throw new Error("Compression did not return a valid URI");
      }
      setValue("avatarUri", compressionResult.uri);
    } catch (error) {
      console.error("Image compression failed:", error);
      toast.error(
        "Compression Failed",
        "Failed to compress image. Please try again."
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleCameraSelection = async () => {
    imagePickerRef.current?.dismiss();
    try {
      const result = await openCameraForAvatar();
      if (result && !result.canceled && result.assets && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        const response = await fetch(imageUri);
        const blob = await response.blob();
        if (blob.size > 15 * 1024 * 1024) {
          toast.error("Error", "File size must be less than 15MB.");
          return;
        }
        await handleImageSelect(imageUri);
      }
    } catch (error) {
      console.error("Camera error:", error);
      toast.error("Error", "Failed to take photo. Please try again.");
    }
  };

  const handleGallerySelection = async () => {
    imagePickerRef.current?.dismiss();
    try {
      const result = await openGalleryForAvatar();
      if (result && !result.canceled && result.assets && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        const response = await fetch(imageUri);
        const blob = await response.blob();
        if (blob.size > 15 * 1024 * 1024) {
          toast.error("Error", "File size must be less than 15MB.");
          return;
        }
        await handleImageSelect(imageUri);
      }
    } catch (error) {
      console.error("Gallery error:", error);
      toast.error("Error", "Failed to select image. Please try again.");
    }
  };

  const onSubmit = async (values: FormData) => {
    const netInfo = await NetInfo.fetch();
    if (!netInfo.isConnected) {
      toast.error(
        "No Internet Connection",
        "Please check your internet connection and try again."
      );
      return;
    }
    if (!values.latitude || !values.longitude) {
      toast.error(
        "Location Required",
        "GPS coordinates are required. Please enable location services and try again."
      );
      return;
    }
    setIsLoading(true);
    try {
      const currentUser = await getCurrentUser();
      if (!currentUser) {
        onClose();
        return;
      }

      let avatarUrl = null;
      const currentAvatarUri = values.avatarUri;
      const previousAvatarUrl = initialFormData.avatarUri;

      if (currentAvatarUri && currentAvatarUri !== previousAvatarUrl) {
        const uploadResult = await uploadAvatarImage(
          currentAvatarUri,
          currentUser.id
        );
        if (uploadResult.success && uploadResult.url) {
          avatarUrl = uploadResult.url;
          if (previousAvatarUrl && previousAvatarUrl.startsWith("http")) {
            try {
              await deleteCustomerAvatar(previousAvatarUrl);
            } catch (error) {
              console.warn("Failed to delete old avatar:", error);
            }
          }
        } else {
          toast.warning(
            "Avatar Upload Failed",
            "Profile saved but avatar upload failed."
          );
        }
      } else if (!currentAvatarUri && previousAvatarUrl) {
        if (previousAvatarUrl.startsWith("http")) {
          try {
            await deleteCustomerAvatar(previousAvatarUrl);
          } catch (error) {
            console.warn("Error deleting avatar:", error);
          }
        }
        avatarUrl = null;
      } else if (currentAvatarUri === previousAvatarUrl) {
        avatarUrl = previousAvatarUrl;
      }

      // First update name in auth.users table (full_name in user_metadata)
      // The database trigger will automatically sync this to customer_profiles table
      const { error: authUpdateError } = await supabase.auth.updateUser({
        data: { full_name: values.name.trim() },
      });

      if (authUpdateError) {
        console.error("Error updating auth user metadata:", authUpdateError);
        toast.error(
          "Profile Update Failed",
          "Failed to update profile name. Please try again."
        );
        return;
      }

      const addressData = cleanAddressData({
        pincode: values.pincode,
        state: values.state,
        city: values.city,
        locality: values.locality,
        address: values.address,
      });

      const profileData = {
        // name field removed - it will be synced by database trigger
        ...addressData,
        latitude: values.latitude,
        longitude: values.longitude,
        updated_at: new Date().toISOString(),
        avatar_url: avatarUrl,
      };

      const { data, error } = await updateCustomerProfileWithCoordinates(
        currentUser.id,
        profileData
      );

      if (error) {
        console.error("Error updating profile:", error);
        toast.error(
          "Profile Update Failed",
          "Failed to update profile. Please try again."
        );
        return;
      }
      if (!data) {
        toast.error(
          "Profile Update Failed",
          "Profile update was not confirmed. Please try again."
        );
        return;
      }

      toast.success(
        "Profile Updated!",
        "Your profile has been updated successfully!"
      );
      await new Promise((resolve) => setTimeout(resolve, 1000));
      await refreshProfileStatus();
      onClose();
    } catch (error) {
      console.error("Error submitting profile:", error);
      const networkState = await NetInfo.fetch();
      if (!networkState.isConnected) {
        toast.error(
          "Connection Lost",
          "Your internet connection was lost. Please try again."
        );
      } else {
        toast.error(
          "Unexpected Error",
          "An unexpected error occurred. Please try again."
        );
      }
    } finally {
      setIsLoading(false);
    }
  };

  const goldColor = "#D4AF37";

  return (
    <Modal
      visible={visible}
      onRequestClose={onClose}
      presentationStyle="fullScreen"
      animationType="slide"
    >
      <SafeAreaView style={styles.safeArea}>
        <KeyboardAvoidingView
          style={styles.keyboardAvoidingView}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
        >
          <View style={styles.header}>
            <View style={{ width: 40 }} />
            <Text style={styles.headerTitle}>Edit Profile</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color={theme.isDark ? "#FFF" : "#000"} />
            </TouchableOpacity>
          </View>

          {isInitialLoading ? (
            <LoadingOverlay
              textColor={theme.isDark ? "#FFF" : "#000"}
              backgroundColor={theme.isDark ? "#000" : "#FFF"}
            />
          ) : (
            <>
              <ScrollView
                ref={scrollViewRef}
                style={styles.scrollView}
                contentContainerStyle={styles.scrollContent}
                showsVerticalScrollIndicator={false}
                keyboardShouldPersistTaps="handled"
                nestedScrollEnabled={true}
              >
                <FormProvider {...formMethods}>
                  <View style={styles.form}>
                    <AvatarUploadSection
                      avatarUri={watch("avatarUri") || ""}
                      isLoading={isLoading}
                      theme={theme}
                      imagePickerRef={imagePickerRef}
                    />
                    <PersonalInformationSection
                      control={control}
                      textColor={theme.isDark ? "#FFF" : "#000"}
                      borderColor={theme.colors.border}
                      styles={styles}
                    />
                    <AddressInformationSection
                      control={control}
                      textColor={theme.isDark ? "#FFF" : "#000"}
                      borderColor={theme.colors.border}
                      styles={styles}
                      isDark={theme.isDark}
                      isPincodeLoading={isPincodeLoading}
                      availableLocalities={availableLocalities}
                      locationPermission={locationPermission}
                      hasGpsCoordinates={hasGpsCoordinates}
                      handlePincodeInputChange={handlePincodeInputChange}
                      handleLocationDetected={handleLocationDetected}
                      handleAddressDetected={handleAddressDetected}
                      handleLocalitySelect={handleLocalitySelect}
                      toast={toast}
                      trigger={trigger}
                      localityPickerRef={localityPickerRef}
                    />
                  </View>
                </FormProvider>
              </ScrollView>

              <View style={styles.footer}>
                <TouchableOpacity
                  style={[
                    styles.submitButton,
                    { backgroundColor: goldColor },
                    isLoading && styles.submitButtonDisabled,
                  ]}
                  onPress={async () => {
                    const isFormValid = await trigger();
                    if (isFormValid) {
                      handleSubmit(onSubmit)();
                    } else {
                      const errors = formState.errors;
                      const errorFields = Object.keys(errors);
                      if (errorFields.length > 0) {
                        const firstError =
                          errors[errorFields[0] as keyof typeof errors];
                        toast.error(
                          "Please fix the errors:",
                          firstError?.message || `${errorFields[0]} is required`
                        );
                      }
                    }
                  }}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <View
                      style={{ flexDirection: "row", alignItems: "center" }}
                    >
                      <Animated.View style={{ transform: [{ rotate: spin }] }}>
                        <Loader2
                          size={20}
                          color="white"
                          style={{ marginRight: 8 }}
                        />
                      </Animated.View>
                      <Text style={styles.submitButtonText}>Updating...</Text>
                    </View>
                  ) : (
                    <Text style={styles.submitButtonText}>Update Profile</Text>
                  )}
                </TouchableOpacity>
              </View>
            </>
          )}

          {isLoading && !isInitialLoading && (
            <LoadingOverlay
              textColor={theme.isDark ? "#FFF" : "#000"}
              backgroundColor={theme.isDark ? "#000" : "#FFF"}
            />
          )}

          <ImagePickerBottomSheet
            ref={imagePickerRef}
            onCameraPress={handleCameraSelection}
            onGalleryPress={handleGallerySelection}
            title="Select Profile Picture"
            cameraLabel="Take Photo"
            galleryLabel="Choose from Gallery"
          />

          <LocalityBottomSheetPicker
            ref={localityPickerRef}
            localities={availableLocalities}
            selectedLocality={watch("locality") || ""}
            onLocalitySelect={handleLocalitySelect}
            placeholder="Select your locality"
          />
        </KeyboardAvoidingView>
      </SafeAreaView>
    </Modal>
  );
}
