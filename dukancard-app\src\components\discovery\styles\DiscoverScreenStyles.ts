/**
 * Styles for Discovery Screen Components
 */

import { StyleSheet } from "react-native";

export const createDiscoverScreenStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    contentContainer: {
      paddingTop: 16,
      backgroundColor: colors.background,
      // Remove flex: 1 to allow proper scrolling
      // Add flexGrow: 1 to ensure content takes full height when needed
      flexGrow: 1,
    },
    clearSearchContainer: {
      paddingHorizontal: 20,
      paddingVertical: 8,
      alignItems: "center",
    },
    clearSearchButton: {
      paddingHorizontal: 16,
      paddingVertical: 8,
      borderRadius: 8,
      backgroundColor: colors.cardBackground,
      borderWidth: 1,
      borderColor: colors.border,
    },
    clearSearchText: {
      fontSize: 14,
      fontWeight: "500",
    },
  });
