import React from "react";
import {
  Modal,
  View,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Text,
  TouchableOpacity,
} from "react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { createSettingsModalStyles } from "@/styles/modals/customer/settings-modal";
import PhoneLinkingForm from "./components/PhoneLinkingForm";
import { X } from "lucide-react-native";

interface PhoneLinkingModalProps {
  isVisible: boolean;
  onClose: () => void;
  currentPhone?: string | null;
}

const PhoneLinkingModal: React.FC<PhoneLinkingModalProps> = ({
  isVisible,
  onClose,
  currentPhone,
}) => {
  const theme = useTheme();
  const styles = createSettingsModalStyles(theme);

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={onClose}
    >
      <SafeAreaView
        style={{ flex: 1, backgroundColor: theme.colors.background }}
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === "ios" ? "padding" : "height"}
          style={{ flex: 1 }}
        >
          <View
            style={{
              flexDirection: "row",
              alignItems: "center",
              justifyContent: "space-between",
              padding: theme.spacing.md,
              borderBottomWidth: 1,
              borderBottomColor: theme.colors.border,
            }}
          >
            <View style={{ width: 40 }} />
            <Text
              style={{
                fontSize: theme.typography.fontSize.lg,
                fontWeight: "600",
                color: theme.colors.textPrimary,
              }}
            >
              Link Phone
            </Text>
            <TouchableOpacity onPress={onClose}>
              <X size={24} color={theme.colors.textPrimary} />
            </TouchableOpacity>
          </View>
          <View style={styles.formContainer}>
            <PhoneLinkingForm currentPhone={currentPhone} />
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </Modal>
  );
};

export default PhoneLinkingModal;
