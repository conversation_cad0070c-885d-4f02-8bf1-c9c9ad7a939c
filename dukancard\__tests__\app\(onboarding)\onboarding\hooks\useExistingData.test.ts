import { renderHook, waitFor } from '@testing-library/react';
import { useExistingData } from '@/app/(onboarding)/onboarding/hooks/useExistingData';
import { toast } from 'sonner';
import { getExistingBusinessProfileData } from '@/app/(onboarding)/onboarding/actions';
import { onboardingPlans } from '@/lib/PricingPlans';

// Mock external modules
jest.mock('sonner', () => ({
  toast: {
    error: jest.fn(),
  },
}));
jest.mock('@/app/(onboarding)/onboarding/actions', () => ({
  getExistingBusinessProfileData: jest.fn(),
}));
jest.mock('@/lib/PricingPlans', () => ({
  onboardingPlans: [
    { id: 'free', name: 'Free Plan', price: 'Free', available: true, recommended: false },
    { id: 'pro', name: 'Pro Plan', price: '$10/month', available: true, recommended: true },
  ],
}));

describe('useExistingData', () => {
  const mockSetSelectedPlan = jest.fn();
  const mockFormSetValue = jest.fn();

  const mockForm = {
    setValue: mockFormSetValue,
  } as any; // Mock only necessary parts of UseFormReturn

  beforeEach(() => {
    jest.clearAllMocks();
    // Default mock for successful data fetch
    (getExistingBusinessProfileData as jest.Mock).mockResolvedValue({
      data: null,
      error: null,
    });
  });

  it('should be in a loading state initially when user is provided', () => {
    const { result } = renderHook(() => useExistingData({
      user: { id: 'user-123' } as any,
      setSelectedPlan: mockSetSelectedPlan,
    }));

    expect(result.current.isLoadingExistingData).toBe(true);
  });

  it('should set existingData and pre-fill form fields on successful fetch with form', async () => {
    const mockUserData = { id: 'user-123' } as any;
    const mockExistingProfileData = {
      businessName: 'Test Business',
      email: '<EMAIL>',
      memberName: 'Test Member',
      phone: '1234567890',
      hasExistingSubscription: false,
    };
    (getExistingBusinessProfileData as jest.Mock).mockResolvedValueOnce({
      data: mockExistingProfileData,
      error: null,
    });

    const { result } = renderHook(() => useExistingData({
      user: mockUserData,
      form: mockForm,
      setSelectedPlan: mockSetSelectedPlan,
    }));

    await waitFor(() => {
      expect(result.current.isLoadingExistingData).toBe(false);
      expect(result.current.existingData).toEqual(mockExistingProfileData);
      expect(mockFormSetValue).toHaveBeenCalledWith('businessName', 'Test Business', { shouldValidate: false });
      expect(mockFormSetValue).toHaveBeenCalledWith('email', '<EMAIL>', { shouldValidate: false });
      expect(mockFormSetValue).toHaveBeenCalledWith('memberName', 'Test Member', { shouldValidate: false });
      expect(mockFormSetValue).toHaveBeenCalledWith('phone', '1234567890', { shouldValidate: false });
      expect(mockSetSelectedPlan).not.toHaveBeenCalled();
    });
  });

  it('should set existingData on successful fetch without form', async () => {
    const mockUserData = { id: 'user-123' } as any;
    const mockExistingProfileData = {
      businessName: 'Test Business',
      email: '<EMAIL>',
      hasExistingSubscription: false,
    };
    (getExistingBusinessProfileData as jest.Mock).mockResolvedValueOnce({
      data: mockExistingProfileData,
      error: null,
    });

    const { result } = renderHook(() => useExistingData({
      user: mockUserData,
      form: null, // No form provided
      setSelectedPlan: mockSetSelectedPlan,
    }));

    await waitFor(() => {
      expect(result.current.isLoadingExistingData).toBe(false);
      expect(result.current.existingData).toEqual(mockExistingProfileData);
      expect(mockFormSetValue).not.toHaveBeenCalled(); // setValue should not be called without a form
      expect(mockSetSelectedPlan).not.toHaveBeenCalled();
    });
  });

  it('should handle existing subscription and pre-select plan', async () => {
    const mockUserData = { id: 'user-123' } as any;
    const mockExistingProfileData = {
      businessName: 'Test Business',
      email: '<EMAIL>',
      hasExistingSubscription: true,
      planId: 'pro',
    };
    (getExistingBusinessProfileData as jest.Mock).mockResolvedValueOnce({
      data: mockExistingProfileData,
      error: null,
    });

    const { result } = renderHook(() => useExistingData({
      user: mockUserData,
      form: mockForm,
      setSelectedPlan: mockSetSelectedPlan,
    }));

    await waitFor(() => {
      expect(result.current.isLoadingExistingData).toBe(false);
      expect(result.current.existingData).toEqual(mockExistingProfileData);
      expect(mockSetSelectedPlan).toHaveBeenCalledWith(expect.objectContaining({ id: 'pro' }));
      expect(mockFormSetValue).toHaveBeenCalledWith('planId', 'pro', { shouldValidate: false });
    });
  });

  it('should call toast.error and set loading to false on fetch error', async () => {
    const mockUserData = { id: 'user-123' } as any;
    (getExistingBusinessProfileData as jest.Mock).mockResolvedValueOnce({
      data: null,
      error: 'Failed to fetch',
    });

    const { result } = renderHook(() => useExistingData({
      user: mockUserData,
      form: mockForm,
      setSelectedPlan: mockSetSelectedPlan,
    }));

    await waitFor(() => {
      expect(result.current.isLoadingExistingData).toBe(false);
      expect(result.current.existingData).toBeNull();
      expect(toast.error).toHaveBeenCalledWith('Failed to load existing profile data');
    });
  });

  it('should not fetch data if user is null', async () => {
    const { result } = renderHook(() => useExistingData({
      user: null,
      form: mockForm,
      setSelectedPlan: mockSetSelectedPlan,
    }));

    // Wait for any potential async operations to complete (should be none)
    await waitFor(() => {
      expect(result.current.isLoadingExistingData).toBe(true); // Still true as no fetch initiated
      expect(result.current.existingData).toBeNull();
      expect(getExistingBusinessProfileData).not.toHaveBeenCalled();
    });
  });
});
