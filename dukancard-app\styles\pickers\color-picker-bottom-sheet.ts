import { StyleSheet } from "react-native";
import { useTheme } from "@/src/hooks/useTheme";

export const createColorPickerBottomSheetStyles = (
  theme: ReturnType<typeof useTheme>
) => {
  return StyleSheet.create({
    safeArea: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    headerTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: "600",
      color: theme.colors.foreground,
    },
    closeButton: {
      padding: theme.spacing.xs,
    },
    searchContainer: {
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.md,
    },
    searchInputContainer: {
      flexDirection: "row",
      alignItems: "center",
      backgroundColor: theme.colors.muted,
      borderRadius: theme.borderRadius.md,
      paddingHorizontal: theme.spacing.md,
      height: 48,
      gap: theme.spacing.sm,
    },
    searchInput: {
      flex: 1,
      fontSize: theme.typography.fontSize.base,
      color: theme.colors.foreground,
    },
    clearButton: {
      padding: theme.spacing.xs,
    },
    list: {
      flex: 1,
    },
    listContent: {
      paddingBottom: theme.spacing.xl,
    },
    colorItem: {
      flexDirection: "row",
      alignItems: "center",
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.md,
      backgroundColor: "transparent",
    },
    colorItemSelected: {
      backgroundColor: theme.colors.primary + "10",
    },
    colorInfo: {
      flex: 1,
      flexDirection: "row",
      alignItems: "center",
      marginRight: theme.spacing.sm,
    },
    colorPreview: {
      marginRight: theme.spacing.md,
    },
    colorSwatch: {
      width: 32,
      height: 32,
      borderRadius: 16,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    whiteColorBorder: {
      borderColor: theme.colors.mutedForeground,
    },
    colorSwatchPlaceholder: {
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: theme.colors.muted,
      borderWidth: 1,
      borderColor: theme.colors.border,
    },
    colorDetails: {
      flex: 1,
    },
    colorName: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "600",
      color: theme.colors.foreground,
      marginBottom: 2,
    },
    colorNameSelected: {
      color: theme.colors.primary,
    },
    colorValue: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.mutedForeground,
    },
    colorValueSelected: {
      color: theme.colors.primary + "CC",
    },
    separator: {
      height: 1,
      backgroundColor: theme.colors.border,
      marginLeft: theme.spacing.md + 32 + theme.spacing.md, // Account for color swatch
    },
    emptyState: {
      flex: 1,
      alignItems: "center",
      justifyContent: "center",
      paddingHorizontal: theme.spacing.xl,
    },
    emptyStateText: {
      fontSize: theme.typography.fontSize.base,
      color: theme.colors.mutedForeground,
      textAlign: "center",
    },
  });
};
