import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { Input } from '../../../../src/components/ui/Input';
import { Mail, Eye } from 'lucide-react-native';

describe('<Input />', () => {
  const mockOnChangeText = jest.fn();
  const mockOnPressRightIcon = jest.fn();

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly with basic props', () => {
    const { toJSON, getByPlaceholderText } = render(
      <Input placeholder="Enter text" onChangeText={mockOnChangeText} />
    );
    expect(getByPlaceholderText('Enter text')).toBeDefined();
    expect(toJSON()).toMatchSnapshot();
  });

  it('handles onChangeText correctly', () => {
    const { getByPlaceholderText } = render(
      <Input placeholder="Enter text" onChangeText={mockOnChangeText} />
    );
    fireEvent.changeText(getByPlaceholderText('Enter text'), 'new value');
    expect(mockOnChangeText).toHaveBeenCalledWith('new value');
  });

  it('displays label and placeholder', () => {
    const { getByText, getByPlaceholderText } = render(
      <Input label="Username" placeholder="Enter username" onChangeText={mockOnChangeText} />
    );
    expect(getByText('Username')).toBeDefined();
    expect(getByPlaceholderText('Enter username')).toBeDefined();
  });

  it('shows error message', () => {
    const { getByText } = render(
      <Input error="Invalid input" onChangeText={mockOnChangeText} />
    );
    expect(getByText('Invalid input')).toBeDefined();
  });

  it('renders leftIcon', () => {
    const { toJSON } = render(
      <Input leftIcon={<Mail testID="mail-icon" />} onChangeText={mockOnChangeText} />
    );
    expect(toJSON()).toMatchSnapshot();
  });

  it('renders rightIcon', () => {
    const { toJSON } = render(
      <Input rightIcon={<Eye testID="eye-icon" />} onChangeText={mockOnChangeText} />
    );
    expect(toJSON()).toMatchSnapshot();
  });

  it('toggles password visibility when type is "password"', async () => {
    const { getByPlaceholderText, queryByTestId } = render(
      <Input type="password" placeholder="Password" onChangeText={mockOnChangeText} />
    );
    const passwordInput = getByPlaceholderText('Password');
    expect(passwordInput.props.secureTextEntry).toBe(true);

    // Initially, Eye should be visible (password is hidden, so we show Eye to reveal it)
    expect(queryByTestId('eye-icon')).toBeDefined();
    expect(queryByTestId('eyeoff-icon')).toBeNull();

    // Click Eye to show password
    fireEvent.press(queryByTestId('eye-icon'));
    expect(passwordInput.props.secureTextEntry).toBe(false);
    expect(queryByTestId('eyeoff-icon')).toBeDefined();
    expect(queryByTestId('eye-icon')).toBeNull();

    // Click EyeOff to hide password again
    fireEvent.press(queryByTestId('eyeoff-icon'));
    expect(passwordInput.props.secureTextEntry).toBe(true);
    expect(queryByTestId('eye-icon')).toBeDefined();
    expect(queryByTestId('eyeoff-icon')).toBeNull();
  });

  it('calls onPressRightIcon when right icon is pressed', () => {
    const { getByTestId } = render(
      <Input
        rightIcon={<Eye testID="custom-right-icon" />}
        onPressRightIcon={mockOnPressRightIcon}
        onChangeText={mockOnChangeText}
      />
    );
    fireEvent.press(getByTestId('custom-right-icon'));
    expect(mockOnPressRightIcon).toHaveBeenCalledTimes(1);
  });

  it('applies custom containerStyle and style', () => {
    const customContainerStyle = { borderWidth: 2, borderColor: 'red' };
    const customInputStyle = { fontSize: 20 };
    const { toJSON } = render(
      <Input
        containerStyle={customContainerStyle}
        style={customInputStyle}
        onChangeText={mockOnChangeText}
      />
    );
    expect(toJSON()).toMatchSnapshot();
  });

  it('handles keyboardType and autoCapitalize', () => {
    const { getByPlaceholderText } = render(
      <Input
        placeholder="Test"
        keyboardType="numeric"
        autoCapitalize="words"
        onChangeText={mockOnChangeText}
      />
    );
    const input = getByPlaceholderText('Test');
    expect(input.props.keyboardType).toBe('numeric');
    expect(input.props.autoCapitalize).toBe('words');
  });
});
