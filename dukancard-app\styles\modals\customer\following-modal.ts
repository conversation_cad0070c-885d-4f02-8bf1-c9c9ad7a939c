import { StyleSheet } from "react-native";
import { useTheme } from "@/src/hooks/useTheme";

export const createFollowingModalStyles = (
  theme: ReturnType<typeof useTheme>
) => {
  const { colors, spacing, borderRadius, typography, isDark } = theme;

  return StyleSheet.create({
    // Modal container
    modalContainer: {
      flex: 1,
      backgroundColor: colors.background,
    },
    safeArea: {
      flex: 1,
    },
    keyboardAvoidingView: {
      flex: 1,
    },
    contentContainer: {
      flex: 1,
    },

    // Header
    header: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      padding: spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: colors.border,
    },
    headerTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: "600",
      color: colors.foreground,
    },
    closeButton: {
      padding: spacing.xs,
    },

    // Search
    searchContainer: {
      padding: spacing.md,
    },

    // List
    listContainer: {
      flex: 1,
    },
    listContentContainer: {
      paddingHorizontal: spacing.md,
      paddingBottom: spacing.lg,
    },

    // Empty State
    emptyContainer: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      padding: spacing.lg,
    },
    emptyIcon: {
      marginBottom: spacing.md,
    },
    emptyTitle: {
      fontSize: typography.fontSize.lg,
      fontWeight: "600",
      color: colors.foreground,
      textAlign: "center",
      marginBottom: spacing.sm,
    },
    emptyDescription: {
      fontSize: typography.fontSize.base,
      color: colors.mutedForeground,
      textAlign: "center",
    },

    // Skeleton styles
    skeletonContainer: {
      flex: 1,
      padding: spacing.md,
    },
    skeletonSearch: {
      height: 48,
      borderRadius: borderRadius.lg,
      marginBottom: spacing.lg,
    },
    skeletonList: {
      flex: 1,
    },
    skeletonCardContainer: {
      flexDirection: "row",
      alignItems: "center",
      marginBottom: spacing.md,
      padding: spacing.md,
      backgroundColor: colors.card,
      borderRadius: borderRadius.lg,
    },
    skeletonAvatar: {
      width: 50,
      height: 50,
      borderRadius: 25,
      marginRight: spacing.md,
    },
    skeletonCardContent: {
      flex: 1,
    },
    skeletonText: {
      height: 16,
      borderRadius: borderRadius.sm,
      marginBottom: spacing.sm,
    },
  });
};
