import React, { useState } from "react";
import { View, Text, TouchableOpacity } from "react-native";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as Yup from "yup";
import { Input } from "@/src/components/ui/Input";
import { Button } from "@/src/components/ui/Button";
import { useTheme } from "@/src/hooks/useTheme";
import { createSettingsModalStyles } from "@/styles/modals/customer/settings-modal";
import { supabase } from "@/lib/supabase";
import { useToast } from "@/src/components/ui/Toast";
import { OTPInput } from "@/src/components/ui/OTPInput";

const EmailSchema = Yup.object().shape({
  email: Yup.string().email("Invalid email").required("Email is required"),
});

const OTPSchema = Yup.object().shape({
  otp: Yup.string().min(6).max(6).required(),
});

type EmailFormData = Yup.InferType<typeof EmailSchema>;
type OTPFormData = Yup.InferType<typeof OTPSchema>;

interface EmailLinkingFormProps {
  onEmailLinked: () => void;
  currentEmail?: string | null;
}

const EmailLinkingForm: React.FC<EmailLinkingFormProps> = ({
  onEmailLinked,
  currentEmail,
}) => {
  const theme = useTheme();
  const styles = createSettingsModalStyles(theme);
  const [isPending, setIsPending] = useState(false);
  const [step, setStep] = useState<"email" | "otp">("email");
  const [emailForOTP, setEmailForOTP] = useState("");
  const toast = useToast();

  const emailForm = useForm<EmailFormData>({
    resolver: yupResolver(EmailSchema),
    defaultValues: { email: currentEmail || "" },
  });

  const otpForm = useForm<OTPFormData>({
    resolver: yupResolver(OTPSchema),
  });

  const onEmailSubmit = async (data: EmailFormData) => {
    setIsPending(true);
    const { error } = await supabase.auth.updateUser({ email: data.email });
    setIsPending(false);

    if (error) {
      toast.error("Error", error.message);
    } else {
      setEmailForOTP(data.email);
      setStep("otp");
      toast.success("Verification code sent!");
    }
  };

  const onOTPSubmit = async (data: OTPFormData) => {
    setIsPending(true);
    const { error } = await supabase.auth.verifyOtp({
      email: emailForOTP,
      token: data.otp,
      type: "email_change",
    });
    setIsPending(false);

    if (error) {
      toast.error("Error", error.message);
    } else {
      toast.success("Email linked successfully!");
      onEmailLinked();
    }
  };

  const onBackToEmail = () => {
    setStep("email");
    setEmailForOTP("");
    otpForm.reset();
  };

  if (step === "otp") {
    return (
      <View style={styles.formContainer}>
        <Text style={styles.settingSubtitle}>
          Enter the 6-digit code sent to {emailForOTP}
        </Text>
        <Controller
          control={otpForm.control}
          name="otp"
          render={({ field: { onChange, value } }) => (
            <OTPInput
              length={6}
              onChangeText={onChange}
              onComplete={(otp) => {
                onChange(otp);
                otpForm.handleSubmit(onOTPSubmit)();
              }}
              error={otpForm.formState.errors.otp?.message}
            />
          )}
        />
        <Button
          title="Verify & Link Email"
          onPress={otpForm.handleSubmit(onOTPSubmit)}
          loading={isPending}
          style={styles.button}
        />
        <TouchableOpacity onPress={onBackToEmail}>
          <Text
            style={{
              color: theme.colors.primary,
              textAlign: "center",
              marginTop: theme.spacing.md,
            }}
          >
            Back to Email
          </Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.formContainer}>
      <Controller
        control={emailForm.control}
        name="email"
        render={({ field: { onChange, onBlur, value } }) => (
          <Input
            label="Email Address"
            onBlur={onBlur}
            onChangeText={onChange}
            value={value}
            error={emailForm.formState.errors.email?.message}
            editable={!isPending}
            style={styles.input}
            keyboardType="email-address"
          />
        )}
      />
      <Button
        title="Link Email"
        onPress={emailForm.handleSubmit(onEmailSubmit)}
        loading={isPending}
        style={styles.button}
      />
    </View>
  );
};

export default EmailLinkingForm;
