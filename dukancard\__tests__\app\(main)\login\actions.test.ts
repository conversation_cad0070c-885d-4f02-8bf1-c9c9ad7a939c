import { sendOTP, verifyOTP, loginWithMobilePassword } from '@/app/(main)/login/actions';
import { createClient } from '@/utils/supabase/server';
import { handleSupabaseAuthError, isEmailRateLimitError } from '@/lib/utils/supabaseErrorHandler';

// Mock the Supabase server client
jest.mock('@/utils/supabase/server', () => ({
  createClient: jest.fn(() => ({
    auth: {
      signInWithOtp: jest.fn(),
      verifyOtp: jest.fn(),
      signInWithPassword: jest.fn(),
    },
  })),
}));

// Mock the error handler utilities
jest.mock('@/lib/utils/supabaseErrorHandler', () => ({
  handleSupabaseAuthError: jest.fn((error) => `Handled Error: ${error.message}`),
  isEmailRateLimitError: jest.fn((error) => error.message.includes('magic link once every 60 seconds')),
}));

const mockCreateClient = createClient as jest.Mock;
const mockHandleSupabaseAuthError = handleSupabaseAuthError as jest.Mock;
const mockIsEmailRateLimitError = isEmailRateLimitError as jest.Mock;

describe('Login Actions', () => {
  let mockSupabaseAuth: any;

  beforeEach(() => {
    jest.clearAllMocks();
    mockSupabaseAuth = {
      signInWithOtp: jest.fn(),
      verifyOtp: jest.fn(),
      signInWithPassword: jest.fn(),
    };
    mockCreateClient.mockReturnValue({
      auth: mockSupabaseAuth,
    });
  });

  describe('sendOTP', () => {
    it('should send OTP successfully', async () => {
      mockSupabaseAuth.signInWithOtp.mockResolvedValueOnce({ error: null });

      const result = await sendOTP({ email: '<EMAIL>' });

      expect(mockSupabaseAuth.signInWithOtp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        options: {
          shouldCreateUser: true,
          data: {
            auth_type: "email",
          },
        },
      });
      expect(result).toEqual({ success: true, message: 'OTP sent to your email address. Please check your inbox.' });
    });

    it('should return error if OTP sending fails', async () => {
      const supabaseError = { message: 'Some generic error' };
      mockSupabaseAuth.signInWithOtp.mockResolvedValueOnce({ error: supabaseError });
      mockIsEmailRateLimitError.mockReturnValueOnce(false);
      mockHandleSupabaseAuthError.mockReturnValueOnce(`Handled Error: ${supabaseError.message}`);

      const result = await sendOTP({ email: '<EMAIL>' });

      expect(result).toEqual({ success: false, error: `Handled Error: ${supabaseError.message}` });
    });

    it('should return error for invalid email format (zod validation)', async () => {
      const result = await sendOTP({ email: 'invalid-email' });
      expect(result).toEqual({ success: false, error: 'Invalid email address' });
    });

    it('should handle thrown exceptions during OTP sending', async () => {
      const thrownError = new Error('Unexpected error');
      mockSupabaseAuth.signInWithOtp.mockRejectedValueOnce(thrownError);
      mockHandleSupabaseAuthError.mockReturnValueOnce(`Handled Error: ${thrownError.message}`);

      const result = await sendOTP({ email: '<EMAIL>' });

      expect(result).toEqual({ success: false, error: `Handled Error: ${thrownError.message}` });
    });
  });

  describe('verifyOTP', () => {
    it('should verify OTP successfully', async () => {
      mockSupabaseAuth.verifyOtp.mockResolvedValueOnce({ data: { user: { id: 'user-123' } }, error: null });

      const result = await verifyOTP({ email: '<EMAIL>', otp: '123456' });

      expect(mockSupabaseAuth.verifyOtp).toHaveBeenCalledWith({
        email: '<EMAIL>',
        token: '123456',
        type: 'email',
      });
      expect(result).toEqual({ success: true, data: { user: { id: 'user-123' } }, message: 'Successfully signed in!' });
    });

    it('should return error if OTP verification fails due to Supabase error', async () => {
      const supabaseError = { message: 'Invalid OTP' };
      mockSupabaseAuth.verifyOtp.mockResolvedValueOnce({ error: supabaseError });
      mockHandleSupabaseAuthError.mockReturnValueOnce(`Handled Error: ${supabaseError.message}`);

      const result = await verifyOTP({ email: '<EMAIL>', otp: '123456' }); // Use valid otp format

      expect(result).toEqual({ success: false, error: `Handled Error: ${supabaseError.message}` });
    });

    it('should return error for invalid OTP format (zod validation)', async () => {
      const result = await verifyOTP({ email: '<EMAIL>', otp: '123' }); // Invalid otp length
      expect(result).toEqual({ success: false, error: 'Invalid OTP format' });
    });

    it('should handle thrown exceptions during OTP verification', async () => {
      const thrownError = new Error('Unexpected error');
      mockSupabaseAuth.verifyOtp.mockRejectedValueOnce(thrownError);
      mockHandleSupabaseAuthError.mockReturnValueOnce(`Handled Error: ${thrownError.message}`);

      const result = await verifyOTP({ email: '<EMAIL>', otp: '123456' });

      expect(result).toEqual({ success: false, error: `Handled Error: ${thrownError.message}` });
    });
  });

  describe('loginWithMobilePassword', () => {
    it('should login with mobile and password successfully', async () => {
      mockSupabaseAuth.signInWithPassword.mockResolvedValueOnce({ data: { user: { id: 'user-123' } }, error: null });

      const result = await loginWithMobilePassword({ mobile: '9876543210', password: 'password123' });

      expect(mockSupabaseAuth.signInWithPassword).toHaveBeenCalledWith({
        phone: '+919876543210',
        password: 'password123',
      });
      expect(result).toEqual({ success: true, data: { user: { id: 'user-123' } }, message: 'Successfully signed in!' });
    });

    it('should return error if mobile/password login fails due to Supabase error', async () => {
      const supabaseError = { message: 'Invalid credentials' };
      mockSupabaseAuth.signInWithPassword.mockResolvedValueOnce({ error: supabaseError });
      mockHandleSupabaseAuthError.mockReturnValueOnce(`Handled Error: ${supabaseError.message}`);

      const result = await loginWithMobilePassword({ mobile: '9876543210', password: 'wrongpass' });

      expect(result).toEqual({ success: false, error: `Handled Error: ${supabaseError.message}` });
    });

    it('should return error for invalid mobile/password format (zod validation)', async () => {
      const result = await loginWithMobilePassword({ mobile: '123', password: 'short' }); // Invalid format
      expect(result).toEqual({ success: false, error: 'Invalid mobile number or password format' });
    });

    it('should handle thrown exceptions during mobile/password login', async () => {
      const thrownError = new Error('Unexpected error');
      mockSupabaseAuth.signInWithPassword.mockRejectedValueOnce(thrownError);
      mockHandleSupabaseAuthError.mockReturnValueOnce(`Handled Error: ${thrownError.message}`);

      const result = await loginWithMobilePassword({ mobile: '9876543210', password: 'password123' });

      expect(result).toEqual({ success: false, error: `Handled Error: ${thrownError.message}` });
    });
  });
});
