import React from 'react';
import { render, screen } from '@testing-library/react';
import OnboardingClient from '@/app/(onboarding)/onboarding/OnboardingClient';

// Mock UI components
jest.mock('@/components/ui/card', () => ({
  Card: ({ children }: { children: React.ReactNode }) => <div data-testid="card-mock">{children}</div>,
  CardContent: ({ children }: { children: React.ReactNode }) => <div data-testid="card-content-mock">{children}</div>,
}));
jest.mock('@/components/ui/separator', () => ({
  Separator: ({ className }: { className: string }) => <hr data-testid="separator-mock" className={className} />,
}));
jest.mock('@/components/ui/form', () => ({
  Form: ({ children }: { children: React.ReactNode }) => <form data-testid="form-mock">{children}</form>,
}));

// Mock custom hooks
const mockUseUserData = jest.fn(() => ({ user: { id: 'test-user', email: '<EMAIL>' } }));
const mockUseExistingData = jest.fn(() => ({ isLoadingExistingData: false, existingData: null }));
const mockUseOnboardingForm = jest.fn(() => ({
  form: { control: {}, handleSubmit: jest.fn(), setValue: jest.fn(), formState: { errors: {} } },
  isSubmitting: false,
  currentStep: 1,
  handleNextStep: jest.fn(),
  handlePreviousStep: jest.fn(),
  onSubmitHandler: jest.fn(),
  setIsSubmitIntended: jest.fn(),
}));
const mockUseSlugAvailability = jest.fn(() => ({ isCheckingSlug: false }));
const mockUsePincodeDetails = jest.fn(() => ({ isPincodeLoading: false, availableLocalities: [], handlePincodeChange: jest.fn() }));

jest.mock('./hooks/useUserData', () => ({ useUserData: mockUseUserData }));
jest.mock('./hooks/useExistingData', () => ({ useExistingData: mockUseExistingData }));
jest.mock('./hooks/useOnboardingForm', () => ({ useOnboardingForm: mockUseOnboardingForm }));
jest.mock('./hooks/useSlugAvailability', () => ({ useSlugAvailability: mockUseSlugAvailability }));
jest.mock('./hooks/usePincodeDetails', () => ({ usePincodeDetails: mockUsePincodeDetails }));

// Mock step components
jest.mock('./components/StepProgress', () => ({
  StepProgress: ({ currentStep, existingData, _isLoadingExistingData }: any) => (
    <div data-testid="step-progress-mock">StepProgress Mock: {currentStep}</div>
  ),
}));
jest.mock('./components/NavigationButtons', () => ({
  NavigationButtons: ({ currentStep, isSubmitting, isCheckingSlug, slugAvailable, selectedPlan, existingData, onNextStep, onPreviousStep, onSubmitIntended }: any) => (
    <div data-testid="navigation-buttons-mock">NavigationButtons Mock: {currentStep}</div>
  ),
}));
jest.mock('./components/LoadingOverlay', () => ({
  LoadingOverlay: ({ isLoading, message }: any) => (
    <div data-testid="loading-overlay-mock">LoadingOverlay Mock: {isLoading ? 'Loading' : 'Not Loading'}</div>
  ),
}));
jest.mock('./components/steps/BusinessDetailsStep', () => ({
  BusinessDetailsStep: ({ form, isSubmitting, user, existingData }: any) => (
    <div data-testid="business-details-step-mock">BusinessDetailsStep Mock</div>
  ),
}));
jest.mock('./components/steps/CardInformationStep', () => ({
  CardInformationStep: ({ form, isSubmitting, user, existingData, slugAvailable, isCheckingSlug, setSlugToCheck, setSlugAvailable }: any) => (
    <div data-testid="card-information-step-mock">CardInformationStep Mock</div>
  ),
}));
jest.mock('./components/steps/AddressStep', () => ({
  AddressStep: ({ form, isSubmitting, user, existingData, availableLocalities, isPincodeLoading, handlePincodeChange }: any) => (
    <div data-testid="address-step-mock">AddressStep Mock</div>
  ),
}));
jest.mock('./components/steps/PlanSelectionStep', () => ({
  PlanSelectionStep: ({ form, isSubmitting, user, existingData, selectedPlan, setSelectedPlan, showPlans, setShowPlans }: any) => (
    <div data-testid="plan-selection-step-mock">PlanSelectionStep Mock</div>
  ),
}));

describe('OnboardingClient', () => {
  it('renders initial components and step 1 content', () => {
    render(<OnboardingClient />);

    // Check for main wrapper components
    expect(screen.getByTestId('card-mock')).toBeInTheDocument();
    expect(screen.getByTestId('card-content-mock')).toBeInTheDocument();
    expect(screen.getByTestId('form-mock')).toBeInTheDocument();

    // Check for core sub-components
    expect(screen.getByTestId('loading-overlay-mock')).toBeInTheDocument();
    expect(screen.getByTestId('step-progress-mock')).toBeInTheDocument();
    expect(screen.getByTestId('navigation-buttons-mock')).toBeInTheDocument();

    // Check that BusinessDetailsStep (step 1) is rendered
    expect(screen.getByTestId('business-details-step-mock')).toBeInTheDocument();

    // Ensure other step components are NOT rendered initially
    expect(screen.queryByTestId('card-information-step-mock')).not.toBeInTheDocument();
    expect(screen.queryByTestId('address-step-mock')).not.toBeInTheDocument();
    expect(screen.queryByTestId('plan-selection-step-mock')).not.toBeInTheDocument();
  });
});
