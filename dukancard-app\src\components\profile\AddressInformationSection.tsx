import React, { useRef } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
} from "react-native";
import { Control, Controller, useFormContext } from "react-hook-form";
import { Loader2, MapPin } from "lucide-react-native";
import { LocationPicker } from "@/src/components/ui/LocationPicker";
import LocalityBottomSheetPicker, {
  LocalityBottomSheetPickerRef,
} from "@/src/components/pickers/LocalityBottomSheetPicker";
import { FormData } from "@/src/types/profile";

interface AddressInformationSectionProps {
  control: Control<FormData>;
  textColor: string;
  borderColor: string;
  styles: any; // TODO: Define a proper style type
  isDark: boolean;
  isPincodeLoading: boolean;
  availableLocalities: string[];
  locationPermission: any; // TODO: Define a proper type for locationPermission
  hasGpsCoordinates: boolean;
  handlePincodeInputChange: (value: string) => void;
  handleLocationDetected: (latitude: number, longitude: number) => void;
  handleAddressDetected: (address: {
    pincode: string;
    city: string;
    state: string;
    locality: string;
  }) => void;
  handleLocalitySelect: (locality: string) => void;
  toast: any; // TODO: Define a proper toast type
  trigger: (name?: keyof FormData | (keyof FormData)[]) => Promise<boolean>;
  localityPickerRef: React.RefObject<LocalityBottomSheetPickerRef | null>;
}

const AddressInformationSection: React.FC<AddressInformationSectionProps> = ({
  control,
  textColor,
  borderColor,
  styles,
  isDark,
  isPincodeLoading,
  availableLocalities,
  locationPermission,
  hasGpsCoordinates,
  handlePincodeInputChange,
  handleLocationDetected,
  handleAddressDetected,
  handleLocalitySelect,
  toast,
  trigger,
  localityPickerRef,
}) => {
  const { watch } = useFormContext();

  return (
    <View style={styles.section}>
      <Text style={[styles.sectionTitle, { color: textColor }]}>
        Address Information
      </Text>

      {/* GPS Location Picker - Moved above pincode field */}
      <View style={{ marginBottom: 16 }}>
        <LocationPicker
          onLocationDetected={(latitude, longitude) =>
            handleLocationDetected(latitude, longitude)
          }
          onAddressDetected={(address) => handleAddressDetected(address)}
          onError={(error) => {
            toast.error("Location Error", error);
          }}
          disabled={isPincodeLoading}
        />

        {/* Location Permission Message */}
        {locationPermission &&
          !locationPermission.granted &&
          !hasGpsCoordinates && (
            <View
              style={{
                marginTop: 8,
                padding: 12,
                backgroundColor: isDark ? "#FEF3C7" : "#FEF3C7",
                borderRadius: 8,
                borderLeftWidth: 4,
                borderLeftColor: "#F59E0B",
              }}
            >
              <View style={{ flexDirection: "row", alignItems: "center" }}>
                <MapPin size={16} color="#92400E" style={{ marginRight: 6 }} />
                <Text
                  style={{
                    fontSize: 14,
                    color: "#92400E",
                    fontWeight: "500",
                  }}
                >
                  Location permission is required
                </Text>
              </View>
              <Text
                style={{
                  fontSize: 12,
                  color: "#92400E",
                  marginTop: 4,
                  lineHeight: 16,
                }}
              >
                Tap &quot;Use Current Location&quot; above and grant location
                permission to get your exact GPS coordinates.
              </Text>
            </View>
          )}
      </View>

      <View style={styles.inputGroup}>
        <Text style={[styles.label, { color: textColor }]}>Pincode *</Text>
        <Controller
          control={control}
          name="pincode"
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <>
              <View style={{ position: "relative" }}>
                <TextInput
                  style={[
                    styles.input,
                    { borderColor, color: textColor, paddingLeft: 45 },
                    error && styles.inputError,
                  ]}
                  value={value || ""}
                  onChangeText={(text: string) => {
                    onChange(text);
                    handlePincodeInputChange(text);
                  }}
                  placeholder="Enter 6-digit pincode"
                  placeholderTextColor={isDark ? "#9CA3AF" : "#6B7280"}
                  keyboardType="numeric"
                  maxLength={6}
                />
                <View
                  style={{
                    position: "absolute",
                    left: 16,
                    top: 14,
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <Text style={{ color: isDark ? "#9CA3AF" : "#6B7280" }}>
                    📍
                  </Text>
                </View>
                {isPincodeLoading && (
                  <View
                    style={{
                      position: "absolute",
                      right: 16,
                      top: 14,
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <Loader2 size={20} color="#D4AF37" />
                  </View>
                )}
              </View>
              {error && <Text style={styles.errorText}>{error.message}</Text>}
            </>
          )}
        />
      </View>

      <View style={styles.row}>
        <View style={[styles.inputGroup, styles.halfWidth]}>
          <Text style={[styles.label, { color: textColor }]}>City *</Text>
          <Controller
            control={control}
            name="city"
            render={({ field: { value }, fieldState: { error } }) => (
              <>
                <TextInput
                  style={[
                    styles.input,
                    {
                      borderColor,
                      color: textColor,
                      backgroundColor: isDark ? "#1F2937" : "#F9FAFB",
                    },
                    error && styles.inputError,
                  ]}
                  value={value || ""}
                  placeholder="Auto-filled from pincode"
                  placeholderTextColor={isDark ? "#9CA3AF" : "#6B7280"}
                  editable={false}
                />
                {error && <Text style={styles.errorText}>{error.message}</Text>}
              </>
            )}
          />
        </View>

        <View style={[styles.inputGroup, styles.halfWidth]}>
          <Text style={[styles.label, { color: textColor }]}>State *</Text>
          <Controller
            control={control}
            name="state"
            render={({ field: { value }, fieldState: { error } }) => (
              <>
                <TextInput
                  style={[
                    styles.input,
                    {
                      borderColor,
                      color: textColor,
                      backgroundColor: isDark ? "#1F2937" : "#F9FAFB",
                    },
                    error && styles.inputError,
                  ]}
                  value={value || ""}
                  placeholder="Auto-filled from pincode"
                  placeholderTextColor={isDark ? "#9CA3AF" : "#6B7280"}
                  editable={false}
                />
                {error && <Text style={styles.errorText}>{error.message}</Text>}
              </>
            )}
          />
        </View>
      </View>

      <View style={styles.inputGroup}>
        <Text style={[styles.label, { color: textColor }]}>
          Locality/Area *
        </Text>
        <Controller
          control={control}
          name="locality"
          render={({ field: { value }, fieldState: { error } }) => (
            <>
              <TouchableOpacity
                style={[
                  styles.input,
                  {
                    borderColor,
                    backgroundColor: isDark ? "#000000" : "#FFFFFF",
                  },
                  error && styles.inputError,
                ]}
                onPress={() => {
                  if (availableLocalities.length > 0) {
                    localityPickerRef.current?.present();
                  } else {
                    toast.warning(
                      "No Localities Available",
                      "Please enter a valid pincode first to see available localities."
                    );
                  }
                }}
                disabled={availableLocalities.length === 0}
              >
                <Text
                  style={[
                    {
                      color: value ? textColor : isDark ? "#9CA3AF" : "#6B7280",
                      fontSize: 16,
                      paddingVertical: 2,
                    },
                  ]}
                >
                  {value || "Select locality"}
                </Text>
              </TouchableOpacity>
              {error && <Text style={styles.errorText}>{error.message}</Text>}
              {availableLocalities.length === 0 &&
                watch("pincode")?.length === 6 && (
                  <Text style={[styles.errorText, { color: "#F59E0B" }]}>
                    Enter a valid pincode to see available localities
                  </Text>
                )}
            </>
          )}
        />
      </View>

      <View style={styles.inputGroup}>
        <Text style={[styles.label, { color: textColor }]}>
          Address Line (Optional)
        </Text>
        <Controller
          control={control}
          name="address"
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <>
              <TextInput
                style={[
                  styles.input,
                  { borderColor, color: textColor },
                  error && styles.inputError,
                ]}
                value={value || ""}
                onChangeText={onChange}
                placeholder="Enter full address"
                placeholderTextColor={isDark ? "#9CA3AF" : "#6B7280"}
                multiline
                numberOfLines={3}
              />
              {error && <Text style={styles.errorText}>{error.message}</Text>}
            </>
          )}
        />
      </View>
    </View>
  );
};

export default AddressInformationSection;
