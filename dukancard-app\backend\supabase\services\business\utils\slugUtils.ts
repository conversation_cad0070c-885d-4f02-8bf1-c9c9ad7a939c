import { supabase } from "../../../../../src/config/supabase";

function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/&/g, "and")
    .replace(/[^a-z0-9]+/g, "-")
    .replace(/^-+|-+$/g, "");
}

export async function generateUniqueSlug(
  name: string,
  currentSlug: string,
  userId: string
): Promise<{ success: boolean; slug?: string; error?: string }> {
  if (currentSlug) {
    const { data, error } = await supabase
      .from("business_profiles")
      .select("id")
      .eq("business_slug", currentSlug)
      .not("id", "eq", userId)
      .single();

    if (error && error.code !== "PGRST116") {
      return { success: false, error: "Failed to validate slug." };
    }

    if (!data) {
      return { success: true, slug: currentSlug };
    }
  }

  let newSlug = generateSlug(name);
  let counter = 1;

  while (true) {
    const { data, error } = await supabase
      .from("business_profiles")
      .select("id")
      .eq("business_slug", newSlug)
      .single();

    if (error && error.code !== "PGRST116") {
      return { success: false, error: "Failed to validate slug." };
    }

    if (!data) {
      return { success: true, slug: newSlug };
    }

    newSlug = `${generateSlug(name)}-${counter}`;
    counter++;
  }
}
