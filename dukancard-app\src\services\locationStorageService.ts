/**
 * Location Storage Service for React Native
 * Handles persisting and retrieving location preferences using AsyncStorage
 * Based on dukancard-app/src/contexts/ThemeContext.tsx AsyncStorage patterns
 */

import AsyncStorage from "@react-native-async-storage/async-storage";
import { LocationData } from "@/src/types/discovery"; // Removed LocationStorageData
import {
  getCurrentLocation,
  reverseGeocodeCoordinates,
  requestLocationPermission,
} from "@/backend/supabase/services/location/locationService";

// Define LocationStorageData interface locally
export interface LocationStorageData {
  pincode?: string;
  city?: string;
  state?: string;
  locality?: string;
  latitude?: number;
  longitude?: number;
  lastUpdated: string;
}

// Storage keys
const LOCATION_STORAGE_KEY = "@dukancard_location_preferences";

/**
 * Save location data to AsyncStorage
 */
export async function saveLocationData(
  locationData: LocationData
): Promise<{ success: boolean; error?: string }> {
  try {
    const storageData: LocationStorageData = {
      ...locationData,
      lastUpdated: new Date().toISOString(),
    };

    await AsyncStorage.setItem(
      LOCATION_STORAGE_KEY,
      JSON.stringify(storageData)
    );
    return { success: true };
  } catch (error) {
    console.error("Error saving location data:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to save location data",
    };
  }
}

/**
 * Load location data from AsyncStorage
 */
export async function loadLocationData(): Promise<{
  success: boolean;
  data?: LocationData;
  error?: string;
}> {
  try {
    const savedData = await AsyncStorage.getItem(LOCATION_STORAGE_KEY);

    if (!savedData) {
      return { success: true, data: undefined };
    }

    const parsedData: LocationStorageData = JSON.parse(savedData);

    // Validate the data structure
    if (!isValidLocationStorageData(parsedData)) {
      console.warn("Invalid location data found in storage, clearing...");
      await clearLocationData();
      return { success: true, data: undefined };
    }

    // Extract location data (excluding lastUpdated)
    const { lastUpdated, ...locationData } = parsedData;

    return { success: true, data: locationData };
  } catch (error) {
    console.error("Error loading location data:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to load location data",
    };
  }
}

/**
 * Clear location data from AsyncStorage
 */
export async function clearLocationData(): Promise<{
  success: boolean;
  error?: string;
}> {
  try {
    await AsyncStorage.removeItem(LOCATION_STORAGE_KEY);
    return { success: true };
  } catch (error) {
    console.error("Error clearing location data:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to clear location data",
    };
  }
}

/**
 * Save pincode to storage
 */
export async function savePincode(
  pincode: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const existingData = await loadLocationData();
    const currentLocation = existingData.data || {};

    return await saveLocationData({
      ...currentLocation,
      pincode,
    });
  } catch (error) {
    console.error("Error saving pincode:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to save pincode",
    };
  }
}

/**
 * Save city to storage
 */
export async function saveCity(
  city: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const existingData = await loadLocationData();
    const currentLocation = existingData.data || {};

    return await saveLocationData({
      ...currentLocation,
      city,
    });
  } catch (error) {
    console.error("Error saving city:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to save city",
    };
  }
}

/**
 * Save locality to storage
 */
export async function saveLocality(
  locality: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const existingData = await loadLocationData();
    const currentLocation = existingData.data || {};

    return await saveLocationData({
      ...currentLocation,
      locality,
    });
  } catch (error) {
    console.error("Error saving locality:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to save locality",
    };
  }
}

/**
 * Save GPS coordinates to storage
 */
export async function saveGPSCoordinates(
  latitude: number,
  longitude: number
): Promise<{ success: boolean; error?: string }> {
  try {
    const existingData = await loadLocationData();
    const currentLocation = existingData.data || {};

    return await saveLocationData({
      ...currentLocation,
      latitude,
      longitude,
    });
  } catch (error) {
    console.error("Error saving GPS coordinates:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to save GPS coordinates",
    };
  }
}

/**
 * Get stored pincode
 */
export async function getStoredPincode(): Promise<{
  success: boolean;
  pincode?: string;
  error?: string;
}> {
  try {
    const result = await loadLocationData();
    if (!result.success) {
      return { success: false, error: result.error };
    }

    return { success: true, pincode: result.data?.pincode };
  } catch (error) {
    console.error("Error getting stored pincode:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to get stored pincode",
    };
  }
}

/**
 * Get stored city
 */
export async function getStoredCity(): Promise<{
  success: boolean;
  city?: string;
  error?: string;
}> {
  try {
    const result = await loadLocationData();
    if (!result.success) {
      return { success: false, error: result.error };
    }

    return { success: true, city: result.data?.city };
  } catch (error) {
    console.error("Error getting stored city:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to get stored city",
    };
  }
}

/**
 * Get stored locality
 */
export async function getStoredLocality(): Promise<{
  success: boolean;
  locality?: string;
  error?: string;
}> {
  try {
    const result = await loadLocationData();
    if (!result.success) {
      return { success: false, error: result.error };
    }

    return { success: true, locality: result.data?.locality };
  } catch (error) {
    console.error("Error getting stored locality:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to get stored locality",
    };
  }
}

/**
 * Get stored GPS coordinates
 */
export async function getStoredGPSCoordinates(): Promise<{
  success: boolean;
  coordinates?: { latitude: number; longitude: number };
  error?: string;
}> {
  try {
    const result = await loadLocationData();
    if (!result.success) {
      return { success: false, error: result.error };
    }

    const { latitude, longitude } = result.data || {};

    if (latitude !== undefined && longitude !== undefined) {
      return { success: true, coordinates: { latitude, longitude } };
    }

    return { success: true, coordinates: undefined };
  } catch (error) {
    console.error("Error getting stored GPS coordinates:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to get stored GPS coordinates",
    };
  }
}

/**
 * Check if location data exists in storage
 */
export async function hasStoredLocationData(): Promise<{
  success: boolean;
  hasData?: boolean;
  error?: string;
}> {
  try {
    const result = await loadLocationData();
    if (!result.success) {
      return { success: false, error: result.error };
    }

    return { success: true, hasData: !!result.data };
  } catch (error) {
    console.error("Error checking stored location data:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to check stored location data",
    };
  }
}

/**
 * Automatic location detection on first app launch
 * Based on dukancard-app/backend/supabase/services/location/locationService.ts GPS functions
 */
export async function detectAndSaveCurrentLocation(): Promise<{
  success: boolean;
  location?: LocationData;
  error?: string;
}> {
  try {
    // Check if location data already exists
    const existingData = await hasStoredLocationData();
    if (existingData.success && existingData.hasData) {
      // Location already exists, load and return it
      const loadResult = await loadLocationData();
      if (loadResult.success && loadResult.data) {
        return { success: true, location: loadResult.data };
      }
    }

    // Request location permission
    const permissionResult = await requestLocationPermission();
    if (!permissionResult.granted) {
      return {
        success: false,
        error:
          "Location permission denied. Please enable location access in settings.",
      };
    }

    // Get current GPS location
    const currentLocation = await getCurrentLocation();
    if (!currentLocation) {
      return {
        success: false,
        error:
          "Failed to get current location. Please check if location services are enabled.",
      };
    }

    const { latitude, longitude } = currentLocation.coords;

    // Reverse geocode to get address details
    const geocodeResult = await reverseGeocodeCoordinates(latitude, longitude);

    let locationData: LocationData = {
      latitude,
      longitude,
    };

    if (geocodeResult.success) {
      locationData = {
        ...locationData,
        pincode: geocodeResult.pincode,
        city: geocodeResult.city,
        state: geocodeResult.state,
        locality: geocodeResult.locality,
      };
    }

    // Save the detected location
    const saveResult = await saveLocationData(locationData);
    if (!saveResult.success) {
      return {
        success: false,
        error: saveResult.error || "Failed to save location data",
      };
    }

    return { success: true, location: locationData };
  } catch (error) {
    console.error("Error detecting and saving current location:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to detect current location",
    };
  }
}

/**
 * Check if this is the first app launch (no location data exists)
 */
export async function isFirstAppLaunch(): Promise<{
  success: boolean;
  isFirst?: boolean;
  error?: string;
}> {
  try {
    const result = await hasStoredLocationData();
    if (!result.success) {
      return { success: false, error: result.error };
    }

    return { success: true, isFirst: !result.hasData };
  } catch (error) {
    console.error("Error checking first app launch:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to check first app launch",
    };
  }
}

/**
 * Validate location storage data structure
 */
function isValidLocationStorageData(data: any): data is LocationStorageData {
  if (!data || typeof data !== "object") {
    return false;
  }

  // Check if lastUpdated exists and is a valid date string
  if (!data.lastUpdated || typeof data.lastUpdated !== "string") {
    return false;
  }

  // Validate date format
  const date = new Date(data.lastUpdated);
  if (isNaN(date.getTime())) {
    return false;
  }

  // Check optional fields have correct types if they exist
  if (data.pincode !== undefined && typeof data.pincode !== "string") {
    return false;
  }

  if (data.city !== undefined && typeof data.city !== "string") {
    return false;
  }

  if (data.state !== undefined && typeof data.state !== "string") {
    return false;
  }

  if (data.locality !== undefined && typeof data.locality !== "string") {
    return false;
  }

  if (data.latitude !== undefined && typeof data.latitude !== "number") {
    return false;
  }

  if (data.longitude !== undefined && typeof data.longitude !== "number") {
    return false;
  }

  return true;
}
