// Mock for @supabase/realtime-js
export default class RealtimeClient {
  constructor() {
    this.channels = [];
    this.connected = false;
  }

  connect() {
    this.connected = true;
    return Promise.resolve();
  }

  disconnect() {
    this.connected = false;
    return Promise.resolve();
  }

  channel(topic) {
    const mockChannel = {
      topic,
      subscribe: jest.fn().mockReturnValue(Promise.resolve()),
      unsubscribe: jest.fn().mockReturnValue(Promise.resolve()),
      on: jest.fn().mockReturnThis(),
      off: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnValue(Promise.resolve()),
    };
    this.channels.push(mockChannel);
    return mockChannel;
  }

  removeChannel(channel) {
    const index = this.channels.indexOf(channel);
    if (index > -1) {
      this.channels.splice(index, 1);
    }
  }
}

export const REALTIME_LISTEN_TYPES = {
  BROADCAST: 'broadcast',
  PRESENCE: 'presence',
  POSTGRES_CHANGES: 'postgres_changes',
};

export const REALTIME_SUBSCRIBE_STATES = {
  SUBSCRIBED: 'SUBSCRIBED',
  TIMED_OUT: 'TIMED_OUT',
  CLOSED: 'CLOSED',
  CHANNEL_ERROR: 'CHANNEL_ERROR',
};
