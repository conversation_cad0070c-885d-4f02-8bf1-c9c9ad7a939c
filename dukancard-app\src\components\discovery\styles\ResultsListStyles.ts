/**
 * Styles for ResultsList Component
 */

import { StyleSheet } from "react-native";

export const createResultsListStyles = (colors: any) =>
  StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    listContent: {
      paddingBottom: 120, // Increased padding to ensure bottom items are accessible and clear of the nav bar
    },
    emptyListContent: {
      flexGrow: 1,
      justifyContent: "center",
    },
    productContainer: {
      flex: 1,
      paddingHorizontal: 10,
      paddingVertical: 8,
    },
    footerLoader: {
      paddingHorizontal: 16,
      paddingVertical: 20,
    },
    footerText: {
      fontSize: 14,
    },
    productSkeletonRow: {
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: 16,
    },
    productSkeletonItem: {
      width: "48%",
    },
  });
