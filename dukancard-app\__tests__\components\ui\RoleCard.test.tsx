import React from 'react';
import { render, screen } from '@testing-library/react-native';
import { RoleCard } from '@/src/components/ui/RoleCard';
import type { RoleOption } from '@/src/types/auth';
import { useTheme } from '@/src/hooks/useTheme';

// Note: useTheme is already mocked globally in jest.setup.js

describe('RoleCard Component', () => {
  const mockOnPress = jest.fn();

  const mockCustomerRole: RoleOption = {
    id: 'customer',
    title: 'Customer',
    description: 'Browse and connect with businesses',
    icon: 'user',
    features: [
      'Discover local businesses',
      'View business cards and products',
      'Contact businesses directly',
      'Save favorite businesses',
    ],
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Import Test', () => {
    it('should import RoleCard component successfully', () => {
      expect(RoleCard).toBeDefined();
      expect(typeof RoleCard).toBe('function');
    });
  });

  describe('Basic Rendering', () => {
    it('should render customer role card correctly', () => {
      render(
        <RoleCard
          role={mockCustomerRole}
          selected={false}
          onPress={mockOnPress}
        />
      );

      expect(screen.getByText('Customer')).toBeTruthy();
      expect(screen.getByText('Browse and connect with businesses')).toBeTruthy();
    });
  });
});
