import { createServerClient } from "@supabase/ssr";
import { cookies, headers } from "next/headers";

export async function createClient() {
  // Check if we're in test environment
  const headersList = await headers();
  const isTestEnvironment = process.env.NODE_ENV === 'test' ||
                           process.env.PLAYWRIGHT_TESTING === 'true' ||
                           headersList.get('x-playwright-testing') === 'true';

  if (isTestEnvironment) {
    // Return a mocked Supabase client for testing
    return createMockSupabaseClient(headersList);
  }

  const cookieStore = await cookies();

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            );
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  );
}

/**
 * Create a mocked Supabase client for testing
 * This returns the same interface as the real client but with mocked responses
 */
function createMockSupabaseClient(headersList: Headers) {
  // Get test state from headers
  const testAuthState = headersList.get('x-test-auth-state');
  const testUserType = headersList.get('x-test-user-type');
  const testHasProfile = headersList.get('x-test-has-profile') === 'true';
  const testBusinessSlug = headersList.get('x-test-business-slug');
  const testPlanId = headersList.get('x-test-plan-id') || 'free';

  const mockUser = testAuthState === 'authenticated' ? {
    id: 'test-user-id',
    aud: 'authenticated',
    role: 'authenticated',
    email: '<EMAIL>',
    user_metadata: { name: 'Test User' },
    created_at: '2024-01-01T00:00:00Z'
  } : null;

  return {
    auth: {
      getUser: async () => ({
        data: { user: mockUser },
        error: null
      })
    },
    from: (table: string) => createMockQueryBuilder(table, testUserType, testHasProfile, testBusinessSlug, testPlanId)
  } as any; // Type assertion since we're mocking the interface
}

/**
 * Create a comprehensive mock query builder for Supabase
 * This covers all the methods used throughout the application
 */
function createMockQueryBuilder(
  table: string,
  testUserType: string | null,
  testHasProfile: boolean,
  testBusinessSlug: string | null,
  testPlanId: string
) {
  const getMockData = () => getMockTableData(table, testUserType, testHasProfile, testBusinessSlug, testPlanId);

  // Create a chainable mock that supports all Supabase query methods
  const createChainableMock = (data?: any): any => ({
    // SELECT methods
    select: (_columns?: string) => createChainableMock(data),

    // FILTER methods
    eq: (_column: string, _value: any) => createChainableMock(data),
    neq: (_column: string, _value: any) => createChainableMock(data),
    gt: (_column: string, _value: any) => createChainableMock(data),
    gte: (_column: string, _value: any) => createChainableMock(data),
    lt: (_column: string, _value: any) => createChainableMock(data),
    lte: (_column: string, _value: any) => createChainableMock(data),
    like: (_column: string, _pattern: string) => createChainableMock(data),
    ilike: (_column: string, _pattern: string) => createChainableMock(data),
    is: (_column: string, _value: any) => createChainableMock(data),
    in: (_column: string, _values: any[]) => createChainableMock(data),
    contains: (_column: string, _value: any) => createChainableMock(data),
    containedBy: (_column: string, _value: any) => createChainableMock(data),
    rangeGt: (_column: string, _value: any) => createChainableMock(data),
    rangeGte: (_column: string, _value: any) => createChainableMock(data),
    rangeLt: (_column: string, _value: any) => createChainableMock(data),
    rangeLte: (_column: string, _value: any) => createChainableMock(data),
    rangeAdjacent: (_column: string, _value: any) => createChainableMock(data),
    overlaps: (_column: string, _value: any) => createChainableMock(data),
    textSearch: (_column: string, _query: string) => createChainableMock(data),
    match: (_query: Record<string, any>) => createChainableMock(data),
    not: (_column: string, _operator: string, _value: any) => createChainableMock(data),
    or: (_filters: string) => createChainableMock(data),
    filter: (_column: string, _operator: string, _value: any) => createChainableMock(data),

    // MODIFIER methods
    order: (_column: string, _options?: { ascending?: boolean; nullsFirst?: boolean }) => createChainableMock(data),
    limit: (_count: number, _options?: { foreignTable?: string }) => createChainableMock(data),
    range: (_from: number, _to: number, _options?: { foreignTable?: string }) => createChainableMock(data),
    abortSignal: (_signal: AbortSignal) => createChainableMock(data),

    // EXECUTION methods
    single: async () => getMockData(),
    maybeSingle: async () => getMockData(),
    then: async (callback?: any) => {
      const result = getMockData();
      return callback ? callback(result) : result;
    },

    // For array results
    data: data || [],
    error: null,
    count: data ? data.length : 0,
    status: 200,
    statusText: 'OK'
  });

  return {
    // SELECT
    select: (_columns?: string) => createChainableMock(),

    // INSERT
    insert: (data: any | any[]) => ({
      select: (_columns?: string) => ({
        single: async () => ({
          data: Array.isArray(data) ? data[0] : data,
          error: null
        }),
        maybeSingle: async () => ({
          data: Array.isArray(data) ? data[0] : data,
          error: null
        }),
        then: async (_callback?: any) => {
          const result = { data: Array.isArray(data) ? data : [data], error: null };
          return _callback ? _callback(result) : result;
        }
      }),
      then: async (_callback?: any) => {
        const result = { data: Array.isArray(data) ? data : [data], error: null };
        return _callback ? _callback(result) : result;
      }
    }),

    // UPDATE
    update: (data: any) => createChainableMock(data),

    // UPSERT
    upsert: (data: any | any[]) => createChainableMock(data),

    // DELETE
    delete: () => createChainableMock(),

    // RPC (Remote Procedure Call)
    rpc: (_functionName: string, _params?: any) => createChainableMock()
  };
}

/**
 * Helper function to get mock table data based on test state
 */
function getMockTableData(
  table: string,
  testUserType: string | null,
  testHasProfile: boolean,
  testBusinessSlug: string | null,
  testPlanId: string
) {
  if (table === 'customer_profiles') {
    const hasCustomerProfile = testHasProfile && testUserType === 'customer';
    return {
      data: hasCustomerProfile ? {
        id: 'test-user-id',
        name: 'Test Customer',
        avatar_url: null,
        phone: '+1234567890',
        email: '<EMAIL>',
        address: 'Test Address',
        city: 'Test City',
        state: 'Test State',
        pincode: '123456'
      } : null,
      error: null
    };
  }

  if (table === 'business_profiles') {
    const hasBusinessProfile = testHasProfile && testUserType === 'business';
    return {
      data: hasBusinessProfile ? {
        id: 'test-user-id',
        business_slug: testBusinessSlug || null,
        trial_end_date: null,
        has_active_subscription: true,
        business_name: 'Test Business',
        city_slug: 'test-city',
        state_slug: 'test-state',
        locality_slug: 'test-locality',
        pincode: '123456',
        business_description: 'Test business description',
        business_category: 'retail',
        phone: '+1234567890',
        email: '<EMAIL>',
        website: 'https://testbusiness.com'
      } : null,
      error: null
    };
  }

  if (table === 'payment_subscriptions') {
    return {
      data: testUserType === 'business' ? {
        id: 'test-subscription-id',
        plan_id: testPlanId,
        business_profile_id: 'test-user-id',
        status: 'active',
        created_at: '2024-01-01T00:00:00Z'
      } : null,
      error: null
    };
  }

  if (table === 'products') {
    return {
      data: testUserType === 'business' ? [
        {
          id: 'test-product-1',
          name: 'Test Product 1',
          price: 100,
          business_profile_id: 'test-user-id',
          available: true
        },
        {
          id: 'test-product-2',
          name: 'Test Product 2',
          price: 200,
          business_profile_id: 'test-user-id',
          available: false
        }
      ] : [],
      error: null
    };
  }

  // Default return for unknown tables
  return { data: null, error: null };
}
