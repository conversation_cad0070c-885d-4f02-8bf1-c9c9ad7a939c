import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { PlanSelectionStep } from '@/app/(onboarding)/onboarding/components/steps/PlanSelectionStep';

// Mock lucide-react icons
jest.mock('lucide-react', () => ({
  CreditCard: ({ className }: { className: string }) => <svg data-testid="credit-card-icon" className={className} />,
  ChevronDown: ({ className }: { className: string }) => <svg data-testid="chevron-down-icon" className={className} />,
  Check: ({ className }: { className: string }) => <svg data-testid="check-icon" className={className} />,
  CheckCircle2: ({ className }: { className: string }) => <svg data-testid="check-circle-icon" className={className} />,
}));

// Explicitly mock react-hook-form
jest.mock('react-hook-form', () => ({
  useForm: () => ({
    control: {},
    handleSubmit: jest.fn(),
    register: jest.fn(),
    setValue: jest.fn(),
    formState: { errors: {} },
    trigger: jest.fn(),
    setError: jest.fn(),
    clearErrors: jest.fn(),
    getValues: jest.fn(),
  }),
  FormProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>, // Simple passthrough
}));

// Mock UI components
jest.mock('@/components/ui/form', () => ({
  FormControl: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  FormItem: ({ children }: { children: React.ReactNode }) => <div data-testid="form-item">{children}</div>,
  FormLabel: ({ children, htmlFor, formLabelHtmlFor }: { children: React.ReactNode, htmlFor?: string, formLabelHtmlFor?: string }) => <label data-testid="form-label" htmlFor={formLabelHtmlFor || htmlFor}>{children}</label>,
  FormMessage: ({ children }: { children: React.ReactNode }) => <div data-testid="form-message">{children}</div>,
  FormField: ({ render, name }: { render: ({ field }: any) => React.ReactNode, name: string }) => {
    const mockField = {
      name: name,
      value: '',
      onChange: jest.fn(),
      onBlur: jest.fn(),
    };
    return <div data-testid="form-field">{render({ field: mockField, formLabelHtmlFor: name })}</div>;
  },
}));

jest.mock('@/components/ui/input', () => ({
  Input: ({ className, placeholder, type, onChange, disabled, name, ...props }: any) => (
    <input
      className={className}
      placeholder={placeholder}
      type={type}
      onChange={onChange}
      disabled={disabled}
      name={name}
      id={name}
      {...props}
    />
  ),
}));

jest.mock('@/components/ui/separator', () => ({
  Separator: ({ className }: { className: string }) => <hr data-testid="separator-mock" className={className} />,
}));

// Mock PricingPlans
jest.mock('@/lib/PricingPlans', () => ({
  onboardingPlans: [
    { id: 'free', name: 'Free Plan', price: 'Free', available: true, recommended: false },
    { id: 'pro', name: 'Pro Plan', price: '$10/month', available: true, recommended: true },
    { id: 'enterprise', name: 'Enterprise Plan', price: 'Custom', available: false, recommended: false },
  ],
}));

describe('PlanSelectionStep', () => {
  const mockSetSelectedPlan = jest.fn();
  const mockSetShowPlans = jest.fn();

  const mockForm = {
    control: {},
    handleSubmit: jest.fn(),
    register: jest.fn(),
    setValue: jest.fn(),
    formState: { errors: {} },
    trigger: jest.fn(),
    setError: jest.fn(),
    clearErrors: jest.fn(),
    getValues: jest.fn(),
  };

  const defaultProps = {
    form: mockForm,
    isSubmitting: false,
    existingData: null,
    selectedPlan: null,
    setSelectedPlan: mockSetSelectedPlan,
    showPlans: false,
    setShowPlans: mockSetShowPlans,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders the plan selection input correctly', () => {
    render(<PlanSelectionStep {...defaultProps} />);

    expect(screen.getByRole('button', { name: /Select a plan/i })).toBeInTheDocument();
    expect(screen.getByText('Select a plan')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Select a plan/i }).querySelector('[data-testid="credit-card-icon"]')).toBeInTheDocument();
    expect(screen.getByTestId('chevron-down-icon')).toBeInTheDocument();
  });

  it('toggles plan options visibility when the select button is clicked', () => {
    render(<PlanSelectionStep {...defaultProps} />);

    const selectButton = screen.getByRole('button', { name: /Select a plan/i });
    expect(screen.queryByText('Free Plan')).not.toBeInTheDocument();

    fireEvent.click(selectButton);
    expect(mockSetShowPlans).toHaveBeenCalledWith(true);

    // Simulate plans becoming visible (as setShowPlans is mocked)
    render(<PlanSelectionStep {...defaultProps} showPlans={true} />);
    expect(screen.getByText('Free Plan')).toBeInTheDocument();
    expect(screen.getByText('Pro Plan')).toBeInTheDocument();
  });

  it('selects a plan and updates the form when a plan option is clicked', () => {
    render(<PlanSelectionStep {...defaultProps} showPlans={true} />); // Render with plans visible

    const proPlanButton = screen.getByText('Pro Plan');
    fireEvent.click(proPlanButton);

    expect(mockSetSelectedPlan).toHaveBeenCalledWith(expect.objectContaining({ id: 'pro' }));
    expect(mockForm.setValue).toHaveBeenCalledWith('planId', 'pro', { shouldValidate: true });
    expect(mockSetShowPlans).toHaveBeenCalledWith(false);
  });

  it('disables the plan selection button when isSubmitting is true', () => {
    render(<PlanSelectionStep {...defaultProps} isSubmitting={true} />);
    expect(screen.getByRole('button', { name: /Select a plan/i })).toBeDisabled();
  });

  it('disables the plan selection button when existingData has existing subscription', () => {
    const existingDataWithSubscription = { hasExistingSubscription: true };
    render(<PlanSelectionStep {...defaultProps} existingData={existingDataWithSubscription} />);
    expect(screen.getByRole('button', { name: /Select a plan/i })).toBeDisabled();
  });

  it('disables "Coming Soon" plans and prevents selection', () => {
    render(<PlanSelectionStep {...defaultProps} showPlans={true} />);

    const enterprisePlanElement = screen.getByTestId('plan-item-enterprise');
    expect(enterprisePlanElement).toHaveAttribute('tabIndex', '-1');
    expect(enterprisePlanElement).toHaveClass('opacity-60');
    expect(screen.getByText('(Coming Soon)')).toBeInTheDocument();

    fireEvent.click(enterprisePlanElement);
    expect(mockSetSelectedPlan).not.toHaveBeenCalled();
    expect(mockForm.setValue).not.toHaveBeenCalled();
    expect(mockSetShowPlans).not.toHaveBeenCalled();
  });

  it('displays existing subscription notification when existingData has existing subscription', () => {
    const existingDataWithSubscription = { hasExistingSubscription: true };
    const selectedPlanWithSubscription = { id: 'pro', name: 'Pro Plan', price: '$10/month', available: true, recommended: true };
    render(<PlanSelectionStep {...defaultProps} existingData={existingDataWithSubscription} selectedPlan={selectedPlanWithSubscription} />);

    expect(screen.getByText(/You're already subscribed to Pro Plan/i)).toBeInTheDocument();
    expect(screen.getByText(/Your current plan will continue as usual./i)).toBeInTheDocument();
  });

  it('displays "Free Forever Plan" message when selected plan is free', () => {
    const freePlan = { id: 'free', name: 'Free Plan', price: 'Free', available: true, recommended: false };
    render(<PlanSelectionStep {...defaultProps} selectedPlan={freePlan} />);

    expect(screen.getByText('Free Forever Plan')).toBeInTheDocument();
    expect(screen.getByText(/Get your business online instantly with our free plan./i)).toBeInTheDocument();
  });

  it('displays "1 Month Free Trial" message when selected plan is not free', () => {
    const proPlan = { id: 'pro', name: 'Pro Plan', price: '$10/month', available: true, recommended: true };
    render(<PlanSelectionStep {...defaultProps} selectedPlan={proPlan} />);

    expect(screen.getByText('1 Month Free Trial')).toBeInTheDocument();
    expect(screen.getByText(/Start your 30-day free trial of premium features by completing the setup./i)).toBeInTheDocument();
  });
});
