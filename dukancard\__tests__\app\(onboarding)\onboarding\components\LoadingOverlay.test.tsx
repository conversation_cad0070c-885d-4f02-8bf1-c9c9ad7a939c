import React from 'react';
import { render, screen } from '@testing-library/react';
import { LoadingOverlay } from '../../../../../app/(onboarding)/onboarding/components/LoadingOverlay';

describe('LoadingOverlay', () => {
  it('should not render when isLoading is false', () => {
    render(<LoadingOverlay isLoading={false} />);
    expect(screen.queryByText('Loading your profile data...')).not.toBeInTheDocument();
    expect(screen.queryByRole('status')).not.toBeInTheDocument(); // Assuming Loader2 has role="status" or similar
  });

  it('should render with default message when isLoading is true', () => {
    render(<LoadingOverlay isLoading={true} />);
    expect(screen.getByText('Loading your profile data...')).toBeInTheDocument();
    expect(screen.getByRole('status')).toBeInTheDocument(); // Check for the loader icon
  });

  it('should render with custom message when isLoading is true and message is provided', () => {
    const customMessage = 'Fetching data...';
    render(<LoadingOverlay isLoading={true} message={customMessage} />);
    expect(screen.getByText(customMessage)).toBeInTheDocument();
    expect(screen.getByRole('status')).toBeInTheDocument();
    expect(screen.queryByText('Loading your profile data...')).not.toBeInTheDocument();
  });

  it('should apply correct CSS classes for styling', () => {
    const { container } = render(<LoadingOverlay isLoading={true} />);
    const overlayDiv = container.firstChild as HTMLElement;

    expect(overlayDiv).toHaveClass('absolute', 'inset-0', 'bg-background/80', 'backdrop-blur-sm', 'z-50', 'flex', 'items-center', 'justify-center');

    const messageParagraph = screen.getByText('Loading your profile data...');
    expect(messageParagraph).toHaveClass('text-sm', 'text-muted-foreground');
  });
});