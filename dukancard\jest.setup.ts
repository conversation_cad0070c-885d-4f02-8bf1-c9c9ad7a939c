// Informs Je<PERSON> to load the jest-dom matchers.
import '@testing-library/jest-dom';
import { TextEncoder, TextDecoder } from 'util';

global.TextEncoder = TextEncoder as any;
global.TextDecoder = TextDecoder as any;

global.Request = global.Request || (class Request {} as any);
global.Response = global.Response || (class Response {} as any);
global.fetch = global.fetch || (jest.fn() as any);

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
}));

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: jest.fn(), // deprecated
        removeListener: jest.fn(), // deprecated
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
    })),
});

// Mock next/navigation
jest.mock('next/navigation', () => ({
    useRouter: () => ({
        push: jest.fn(),
        replace: jest.fn(),
        back: jest.fn(),
        forward: jest.fn(),
        refresh: jest.fn(),
        prefetch: jest.fn(),
    }),
    useSearchParams: () => ({
        get: jest.fn(),
        has: jest.fn(),
        getAll: jest.fn(),
        keys: jest.fn(),
        values: jest.fn(),
        entries: jest.fn(),
        forEach: jest.fn(),
        toString: jest.fn(),
    }),
    usePathname: () => '/test-path',
}));

// Mock Next.js server functions
jest.mock('next/headers', () => ({
    headers: jest.fn(() => ({
        get: jest.fn(),
        has: jest.fn(),
        set: jest.fn(),
        delete: jest.fn(),
        append: jest.fn(),
        getSetCookie: jest.fn(),
        forEach: jest.fn(),
        entries: jest.fn(),
        keys: jest.fn(),
        values: jest.fn(),
    })),
    cookies: jest.fn(() => ({
        get: jest.fn(),
        has: jest.fn(),
        set: jest.fn(),
        delete: jest.fn(),
        clear: jest.fn(),
        getAll: jest.fn(),
        toString: jest.fn(),
    })),
}));

jest.mock('next/cache', () => ({
    revalidatePath: jest.fn(),
    revalidateTag: jest.fn(),
    unstable_cache: jest.fn(),
    unstable_noStore: jest.fn(),
}));

// Mock NextResponse for middleware tests
jest.mock('next/server', () => ({
    NextRequest: jest.fn(),
    NextResponse: {
        next: jest.fn((options) => ({
            headers: {
                getSetCookie: jest.fn(() => []),
                get: jest.fn(),
                has: jest.fn(),
                set: jest.fn(),
                delete: jest.fn(),
                append: jest.fn(),
                forEach: jest.fn(),
                entries: jest.fn(),
                keys: jest.fn(),
                values: jest.fn(),
            },
            cookies: {
                get: jest.fn(),
                has: jest.fn(),
                set: jest.fn(),
                delete: jest.fn(),
                clear: jest.fn(),
                getAll: jest.fn(),
                toString: jest.fn(),
            },
            ...options,
        })),
        redirect: jest.fn((url) => ({
            headers: {
                getSetCookie: jest.fn(() => []),
                get: jest.fn(),
                has: jest.fn(),
                set: jest.fn(),
                delete: jest.fn(),
                append: jest.fn(),
                forEach: jest.fn(),
                entries: jest.fn(),
                keys: jest.fn(),
                values: jest.fn(),
            },
            cookies: {
                get: jest.fn(),
                has: jest.fn(),
                set: jest.fn(),
                delete: jest.fn(),
                clear: jest.fn(),
                getAll: jest.fn(),
                toString: jest.fn(),
            },
            url,
        })),
        json: jest.fn((data) => ({ json: data })),
    },
}));

// Mock Supabase - using global mock factory
// The actual mocking is handled by the __mocks__ directory structure

// Mock Supabase SSR
jest.mock('@supabase/ssr', () => ({
    createServerClient: jest.fn(() => ({
        auth: {
            getUser: jest.fn().mockResolvedValue({ data: { user: null }, error: null }),
            getSession: jest.fn().mockResolvedValue({ data: { session: null }, error: null }),
        },
        from: jest.fn(() => ({
            select: jest.fn().mockReturnThis(),
            eq: jest.fn().mockReturnThis(),
            maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
        })),
    })),
}));

// Mock framer-motion
jest.mock('framer-motion', () => {
    const React = require('react');
    const original = jest.requireActual('framer-motion');

    return {
        ...original,
        motion: new Proxy(original.motion, {
            get: (target, prop) => {
                if (typeof prop === 'string') {
                    // eslint-disable-next-line react/display-name
                    return React.forwardRef((props: any, ref: any) => {
                        const {
                            whileHover,
                            whileTap,
                            animate,
                            initial,
                            variants,
                            transition,
                            ...rest
                        } = props;
                        return React.createElement(prop, { ...rest, ref });
                    });
                }
                return target[prop];
            },
        }),
        AnimatePresence: ({ children }: { children: any }) => children,
    };
});

// Mock sonner (toast notifications)
jest.mock('sonner', () => ({
    toast: {
        success: jest.fn(),
        error: jest.fn(),
        info: jest.fn(),
        warning: jest.fn(),
        loading: jest.fn(),
        dismiss: jest.fn(),
    },
    Toaster: () => null,
}));

// Mock react-hook-form's useForm
jest.mock('react-hook-form', () => ({
  useForm: jest.fn(() => ({
    handleSubmit: jest.fn((cb) => cb),
    register: jest.fn(),
    formState: { errors: {} },
    reset: jest.fn(),
    setError: jest.fn(),
    clearErrors: jest.fn(),
    setValue: jest.fn(),
    getValues: jest.fn(),
    trigger: jest.fn(),
  })),
}));