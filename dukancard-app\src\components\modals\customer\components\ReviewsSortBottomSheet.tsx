/**
 * ReviewsSortBottomSheet Component
 * A bottom sheet dialog for selecting sort options for reviews
 * Based on SortBottomSheet pattern from discovery screen
 */

import React, {
  forwardRef,
  useImperativeHandle,
  useRef,
  useMemo,
  useCallback,
} from "react";
import { View, Text, TouchableOpacity } from "react-native";
import BottomSheet, {
  BottomSheetView,
  BottomSheetFlatList,
} from "@gorhom/bottom-sheet";
import { Ionicons } from "@expo/vector-icons";
import { useTheme } from "@/src/hooks/useTheme";
import { createReviewsModalStyles } from "@/styles/modals/customer/reviews-modal";

export type ReviewSortOption = "newest" | "oldest" | "rating_high" | "rating_low";

interface ReviewsSortBottomSheetProps {
  sortBy: ReviewSortOption;
  onSortSelect: (sort: ReviewSortOption) => void;
}

export interface ReviewsSortBottomSheetRef {
  present: () => void;
  dismiss: () => void;
}

// Sort options for reviews
const REVIEW_SORT_OPTIONS: {
  value: ReviewSortOption;
  label: string;
  group: string;
}[] = [
  { value: "newest", label: "Newest First", group: "Date" },
  { value: "oldest", label: "Oldest First", group: "Date" },
  { value: "rating_high", label: "Highest Rating", group: "Rating" },
  { value: "rating_low", label: "Lowest Rating", group: "Rating" },
];

const ReviewsSortBottomSheet = forwardRef<ReviewsSortBottomSheetRef, ReviewsSortBottomSheetProps>(
  ({ sortBy, onSortSelect }, ref) => {
    const theme = useTheme();
    const { colors, isDark } = theme;
    const bottomSheetRef = useRef<BottomSheet>(null);
    const styles = createReviewsModalStyles(theme);

    // Snap points for the bottom sheet
    const snapPoints = useMemo(() => ["50%"], []);

    useImperativeHandle(ref, () => ({
      present: () => {
        bottomSheetRef.current?.expand();
      },
      dismiss: () => {
        bottomSheetRef.current?.close();
      },
    }));

    const handleSheetChanges = useCallback((index: number) => {
      // Handle sheet changes if needed
    }, []);

    const handleSortSelect = useCallback(
      (sortValue: ReviewSortOption) => {
        onSortSelect(sortValue);
        bottomSheetRef.current?.close();
      },
      [onSortSelect]
    );

    // Flatten options for FlatList with group headers
    const flattenedOptions = useMemo(() => {
      const groupedOptions = REVIEW_SORT_OPTIONS.reduce((acc, option) => {
        if (!acc[option.group]) {
          acc[option.group] = [];
        }
        acc[option.group].push(option);
        return acc;
      }, {} as Record<string, { value: ReviewSortOption; label: string; group: string }[]>);

      const flattened: { type: "header" | "option"; data: any }[] = [];
      Object.entries(groupedOptions).forEach(([groupName, options]) => {
        flattened.push({ type: "header", data: { groupName } });
        options.forEach((option) => {
          flattened.push({ type: "option", data: option });
        });
      });
      return flattened;
    }, []);

    // Render item function for FlatList
    const renderItem = useCallback(
      ({ item }: { item: { type: "header" | "option"; data: any } }) => {
        if (item.type === "header") {
          return (
            <Text style={styles.sortGroupTitle}>{item.data.groupName}</Text>
          );
        }

        const option = item.data;
        const isSelected = sortBy === option.value;

        return (
          <TouchableOpacity
            style={[styles.sortOption, isSelected && styles.sortSelectedOption]}
            onPress={() => handleSortSelect(option.value)}
          >
            <Text
              style={[
                styles.sortOptionText,
                isSelected && styles.sortSelectedOptionText,
              ]}
            >
              {option.label}
            </Text>
            {isSelected && (
              <Ionicons name="checkmark" size={20} color="#C29D5B" />
            )}
          </TouchableOpacity>
        );
      },
      [sortBy, handleSortSelect, styles]
    );

    return (
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        onChange={handleSheetChanges}
        enablePanDownToClose
        enableDynamicSizing={false}
        enableContentPanningGesture={false}
        enableHandlePanningGesture={true}
        backgroundStyle={{
          backgroundColor: isDark ? "#000000" : "#ffffff",
        }}
        handleIndicatorStyle={{
          backgroundColor: colors.textSecondary,
        }}
      >
        <BottomSheetView style={styles.sortBottomSheetContainer}>
          {/* Header */}
          <View style={styles.sortBottomSheetHeader}>
            <Text style={styles.sortBottomSheetTitle}>Sort Reviews</Text>
            <TouchableOpacity
              onPress={() => bottomSheetRef.current?.close()}
              style={styles.sortBottomSheetCloseButton}
            >
              <Ionicons name="close" size={24} color={colors.textPrimary} />
            </TouchableOpacity>
          </View>

          {/* Sort Options with FlatList */}
          <BottomSheetFlatList
            data={flattenedOptions}
            renderItem={renderItem}
            keyExtractor={(item, index) =>
              item.type === "header"
                ? `header-${item.data.groupName}`
                : `option-${item.data.value}-${index}`
            }
            showsVerticalScrollIndicator={true}
            contentContainerStyle={styles.sortBottomSheetListContent}
            keyboardShouldPersistTaps="handled"
          />
        </BottomSheetView>
      </BottomSheet>
    );
  }
);

ReviewsSortBottomSheet.displayName = "ReviewsSortBottomSheet";

export default ReviewsSortBottomSheet;
