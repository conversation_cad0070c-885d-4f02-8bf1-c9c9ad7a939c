import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  RefreshControl,
} from "react-native";
import {
  reviewsService,
  ReviewData,
} from "@/backend/supabase/services/posts/socialService";
import { useAuth } from "@/src/contexts/AuthContext";
import { ReviewCard } from "@/src/components/social/ReviewCard";
import { ReviewsModalSkeleton } from "@/src/components/skeletons/modals/ReviewsModalSkeleton";
import { createReviewsModalStyles } from "@/styles/modals/customer/reviews-modal";
import { useTheme } from "@/src/hooks/useTheme";

type SortByType = "newest" | "oldest" | "rating_high" | "rating_low";

interface ReviewsListProps {
  sortBy: SortByType;
  searchTerm?: string;
  onReviewCountChange: (count: number) => void;
}

const ReviewsList: React.FC<ReviewsListProps> = ({ sortBy, searchTerm = "", onReviewCountChange }) => {
  const { user } = useAuth();
  const theme = useTheme();
  const styles = createReviewsModalStyles(theme);

  const [reviews, setReviews] = useState<ReviewData[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const fetchReviews = useCallback(
    async (isRefreshing = false) => {
      if (!user) return;

      const currentPage = isRefreshing ? 1 : page;

      if (currentPage === 1 && !isRefreshing) {
        setLoading(true);
      } else if (currentPage > 1) {
        setLoadingMore(true);
      }
      if (isRefreshing) {
        setRefreshing(true);
      }

      try {
        const result = await reviewsService.fetchReviews(
          user.id,
          currentPage,
          20,
          sortBy,
          searchTerm
        );
        if (isRefreshing) {
          setReviews(result.items);
        } else {
          setReviews((prev) =>
            currentPage === 1 ? result.items : [...prev, ...result.items]
          );
        }
        setHasMore(result.hasMore);

        // Update review count in parent component
        if (currentPage === 1) {
          onReviewCountChange(result.totalCount);
        }
      } catch (error) {
        console.error("Failed to fetch reviews:", error);
      } finally {
        setLoading(false);
        setLoadingMore(false);
        setRefreshing(false);
      }
    },
    [user, page, sortBy, searchTerm, onReviewCountChange]
  );

  useEffect(() => {
    setPage(1);
    fetchReviews(true);
  }, [sortBy, searchTerm, fetchReviews]);

  useEffect(() => {
    if (page > 1) {
      fetchReviews();
    }
  }, [page, fetchReviews]);

  const handleRefresh = () => {
    setPage(1);
    fetchReviews(true);
  };

  const handleLoadMore = () => {
    if (hasMore && !loadingMore) {
      setPage((prevPage) => prevPage + 1);
    }
  };

  const handleDelete = async (reviewId: string) => {
    try {
      await reviewsService.deleteReview(reviewId);
      setReviews((prev) => {
        const newReviews = prev.filter((r) => r.id !== reviewId);
        // Update review count when a review is deleted
        onReviewCountChange(newReviews.length);
        return newReviews;
      });
    } catch (error) {
      console.error("Failed to delete review:", error);
      // Optionally show an error message to the user
    }
  };

  const handleUpdate = async (
    reviewId: string,
    rating: number,
    reviewText: string
  ) => {
    try {
      await reviewsService.updateReview(reviewId, rating, reviewText);
      setReviews((prev) =>
        prev.map((r) =>
          r.id === reviewId ? { ...r, rating, review_text: reviewText } : r
        )
      );
    } catch (error) {
      console.error("Failed to update review:", error);
      // Optionally show an error message to the user
    }
  };



  if (loading) {
    return <ReviewsModalSkeleton />;
  }

  return (
    <View style={{ flex: 1 }}>
      <FlatList
        data={reviews}
        renderItem={({ item }) => (
          <ReviewCard
            review={item}
            onDelete={handleDelete}
            onUpdate={handleUpdate}
          />
        )}
        keyExtractor={(item) => item.id}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
              You haven&apos;t written any reviews yet.
            </Text>
          </View>
        }
        ListFooterComponent={
          loadingMore ? (
            <ActivityIndicator style={styles.footerLoadingContainer} />
          ) : null
        }
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      />
    </View>
  );
};

export default ReviewsList;
