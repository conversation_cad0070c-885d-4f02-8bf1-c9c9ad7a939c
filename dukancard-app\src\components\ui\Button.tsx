import { useColorScheme } from '@/src/hooks/useColorScheme';
import React from 'react';
import {
    ActivityIndicator,
    Text,
    TouchableOpacity,
    ViewStyle,
} from 'react-native';

export interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  style?: ViewStyle;
  icon?: React.ReactNode;
}

export function Button({
  title,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  style,
  icon,
}: ButtonProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Clean button styles without the problematic design
  const getButtonStyle = () => {
    const baseStyle = {
      flexDirection: 'row' as const,
      alignItems: 'center' as const,
      justifyContent: 'center' as const,
      borderRadius: 12,
      paddingHorizontal: size === 'small' ? 16 : size === 'medium' ? 20 : 24,
      paddingVertical: size === 'small' ? 10 : size === 'medium' ? 14 : 18,
      minHeight: size === 'small' ? 40 : size === 'medium' ? 48 : 56,
      gap: 8,
    };

    if (variant === 'primary') {
      return {
        ...baseStyle,
        backgroundColor: isDark ? '#404040' : '#000000',
      };
    } else if (variant === 'secondary') {
      return {
        ...baseStyle,
        backgroundColor: isDark ? '#2a1a1a' : '#f8f9fa',
        borderWidth: 1,
        borderColor: '#C29D5B',
      };
    } else { // outline
      return {
        ...baseStyle,
        backgroundColor: 'transparent',
        borderWidth: 1,
        borderColor: isDark ? '#333333' : '#CCCCCC',
      };
    }
  };

  const getTextStyle = () => {
    const baseTextStyle = {
      fontSize: size === 'small' ? 14 : size === 'medium' ? 16 : 18,
      fontWeight: '600' as const,
    };

    if (variant === 'primary') {
      return {
        ...baseTextStyle,
        color: isDark ? '#FFFFFF' : '#FFFFFF',
      };
    } else {
      return {
        ...baseTextStyle,
        color: '#C29D5B',
      };
    }
  };

  return (
    <TouchableOpacity
      style={[getButtonStyle(), style, (disabled || loading) && { opacity: 0.6 }]}
      onPress={disabled || loading ? undefined : onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      {loading && (
        <ActivityIndicator
          size="small"
          color={variant === 'primary' ? '#FFFFFF' : '#C29D5B'}
        />
      )}
      {icon && !loading && icon}
      {title && (
        <Text style={getTextStyle()}>
          {title}
        </Text>
      )}
    </TouchableOpacity>
  );
}
