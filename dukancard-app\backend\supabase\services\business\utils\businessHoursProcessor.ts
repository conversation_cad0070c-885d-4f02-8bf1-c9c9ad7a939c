/**
 * Processes business hours data for database storage
 * Ensures proper JSONB formatting and validation
 */
export function processBusinessHours(
  businessHours: unknown
): Record<string, unknown> | null {
  if (!businessHours) {
    return null;
  }

  try {
    let businessHoursData: unknown = null;

    // If it's already a string, parse it to ensure it's valid JSON
    if (typeof businessHours === "string") {
      businessHoursData = JSON.parse(businessHours);
    } else {
      // If it's an object, use it directly
      businessHoursData = businessHours;
    }

    // Validate that it has the expected structure
    if (typeof businessHoursData !== "object" || businessHoursData === null) {
      console.warn("Invalid business_hours format, setting to null");
      return null;
    }

    return businessHoursData as Record<string, unknown>;
  } catch (error) {
    console.error("Error processing business_hours data:", error);
    return null;
  }
}
