"use client";

/**
 * Realtime Subscription Notes
 *
 * This component uses Supabase Realtime to listen for new activities.
 *
 * Important: You need to enable realtime for the business_activities table in Supabase:
 * 1. Go to Supabase Dashboard > Database > Replication
 * 2. Find the "business_activities" table in the list
 * 3. Enable realtime by toggling it on
 *
 * The component subscribes to INSERT events on the business_activities table
 * with a filter for the specific business_profile_id:
 *
 * ```javascript
 * supabase
 *   .channel('business-activities-changes')
 *   .on(
 *     'postgres_changes',
 *     {
 *       event: 'INSERT',
 *       schema: 'public',
 *       table: 'business_activities',
 *       filter: `business_profile_id=eq.${businessProfileId}`,
 *     },
 *     async (payload) => {
 *       // Handle new activity
 *     }
 *   )
 *   .subscribe();
 * ```
 */

import { useState, useEffect, useRef, useCallback } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Bell, Heart, Star, Users, RefreshCw, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import {
  BusinessActivity,
  getBusinessActivities,
  getUnreadActivitiesCount,
} from "@/lib/actions/activities";
import ActivityItem from "./ActivityItem";
import { toast } from "sonner";

import { realtimeService } from "@/lib/services/realtimeService";

interface ActivitiesPageClientProps {
  initialActivities: BusinessActivity[];
  totalCount: number;
  unreadCount: number;
  businessProfileId: string;
}

export default function ActivitiesPageClient({
  initialActivities,
  totalCount,
  unreadCount: initialUnreadCount,
  businessProfileId,
}: ActivitiesPageClientProps) {
  const [activities, setActivities] =
    useState<BusinessActivity[]>(initialActivities);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(totalCount > initialActivities.length);
  const [sortBy, setSortBy] = useState<"newest" | "oldest" | "unread_first">(
    "newest"
  );
  const [filterBy, setFilterBy] = useState<
    "all" | "like" | "subscribe" | "rating" | "unread"
  >("all");
  const [activeTab, setActiveTab] = useState<
    "all" | "likes" | "subscriptions" | "ratings"
  >("all");
  const [unreadCount, setUnreadCount] = useState(initialUnreadCount);

  // Track which unread notifications have been viewed in this session
  const [viewedUnreadIds, setViewedUnreadIds] = useState<Set<string>>(
    new Set()
  );
  // Snapshot of activities when page first loads (for section determination)
  const [activitiesSnapshot, _setActivitiesSnapshot] =
    useState<BusinessActivity[]>(initialActivities);
  const loadMoreRef = useRef<HTMLDivElement>(null);

  // Function to mark a notification as viewed (when it comes into view)
  const markAsViewed = useCallback(
    (activityId: string) => {
      // Only track activities that were initially unread when page loaded
      const wasInitiallyUnread =
        activitiesSnapshot.find((a) => a.id === activityId)?.is_read === false;
      if (wasInitiallyUnread) {
        setViewedUnreadIds((prev) => new Set([...prev, activityId]));
      }
    },
    [activitiesSnapshot]
  );

  // Check if all initially unread activities have been viewed and mark them as read
  useEffect(() => {
    // Get activities that were unread when page loaded (from snapshot)
    const initiallyUnreadActivities = activitiesSnapshot.filter(
      (a) => !a.is_read
    );
    const initiallyUnreadIds = initiallyUnreadActivities.map((a) => a.id);

    // If we have initially unread activities and all of them have been viewed
    if (
      initiallyUnreadIds.length > 0 &&
      initiallyUnreadIds.every((id) => viewedUnreadIds.has(id))
    ) {
      // Mark all as read after a short delay to ensure user has actually seen them
      const timer = setTimeout(async () => {
        try {
          const { markActivitiesAsRead } = await import(
            "@/lib/actions/activities"
          );
          await markActivitiesAsRead({
            businessProfileId,
            activityIds: "all",
          });

          // Update local state to reflect they're now read
          setActivities((prev) =>
            prev.map((activity) => ({ ...activity, is_read: true }))
          );
          setUnreadCount(0);
        } catch (error) {
          console.error("Error marking activities as read:", error);
        }
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [viewedUnreadIds, activitiesSnapshot, businessProfileId]);

  // Refresh activities
  const refreshActivities = useCallback(
    async (
      newPage: number,
      newFilter: "all" | "like" | "subscribe" | "rating" | "unread",
      newSort: "newest" | "oldest" | "unread_first"
    ) => {
      // Only show loading state if it's a user-initiated refresh or first page load
      // For real-time updates, we don't want to show loading state
      const isUserInitiated = newPage === 1 && activities.length > 0;

      if (isUserInitiated) {
        // Clear activities to show loading state for user-initiated refreshes
        setActivities([]);
      }

      setLoading(true);

      try {
        const pageSize = 15; // Increased page size for better user experience

        const { activities: newActivities, count } =
          await getBusinessActivities({
            businessProfileId,
            page: newPage,
            pageSize: pageSize,
            sortBy: newSort,
            filterBy: newFilter,
            autoMarkAsRead: false, // Don't auto-mark as read - let user view them first
          });

        // If it's the first page, replace all activities
        // Otherwise, append the new activities to the existing list
        if (newPage === 1) {
          // Ensure we have valid activities array
          const validActivities = newActivities || [];
          console.log("Setting activities:", validActivities.length, "items");
          setActivities(validActivities);
        } else {
          setActivities((prev) => [...prev, ...(newActivities || [])]);
        }

        // Update hasMore flag based on the total count
        setHasMore(count > newPage * pageSize);

        // Update unread count if we've marked activities as read
        // We'll fetch the latest unread count
        const { count: newUnreadCount } = await getUnreadActivitiesCount(
          businessProfileId
        );
        setUnreadCount(newUnreadCount);
      } catch (error) {
        console.error("Error refreshing activities:", error);
        toast.error("Failed to load activities");
      } finally {
        setLoading(false);
      }
    },
    [businessProfileId, activities.length]
  );

  // Set up initial loading effect
  useEffect(() => {
    // If we have initial activities, no need to show loading state
    if (initialActivities.length > 0) return;

    // Otherwise, refresh activities to show loading state
    refreshActivities(1, filterBy, sortBy);
  }, [filterBy, initialActivities.length, sortBy, refreshActivities]);

  // Set up real-time subscription for new activities
  useEffect(() => {
    if (!businessProfileId) return;

    const insertSubscription = realtimeService.subscribeToBusinessActivities(
      businessProfileId,
      async (payload) => {
        console.log("Received realtime activity insert:", payload);

        try {
          // Fetch the new activity with user profile
          const { activities: newActivities } = await getBusinessActivities({
            businessProfileId,
            page: 1,
            pageSize: 1,
            sortBy: "newest",
            filterBy: "all",
            autoMarkAsRead: false, // Don't auto-mark as read for realtime notifications
          });

          if (newActivities && newActivities.length > 0) {
            console.log("New activity fetched:", newActivities[0]);

            // Make sure the activity has all required fields for rendering
            const newActivity = {
              ...newActivities[0],
              // Ensure user_profile exists
              user_profile: newActivities[0].user_profile || {
                name: "Anonymous User",
                is_business: false,
              },
            };

            // Increment unread count
            setUnreadCount((prev) => prev + 1);

            // Show a toast notification
            const activity = newActivity;
            const userName = activity.user_profile?.is_business
              ? activity.user_profile?.business_name
              : activity.user_profile?.name;

            let message = "";
            switch (activity.activity_type) {
              case "like":
                message = `${userName || "Someone"} liked your business`;
                break;
              case "subscribe":
                message = `${
                  userName || "Someone"
                } subscribed to your business`;
                break;
              case "rating":
                message = `${userName || "Someone"} rated your business ${
                  activity.rating_value
                }/5`;
                break;
            }

            toast.info("New Activity", {
              description: message,
              duration: 5000,
            });

            // Instead of just adding to state, do a full refresh immediately
            // This ensures the activity is properly rendered with all data
            refreshActivities(1, filterBy, sortBy);
          }
        } catch (error) {
          console.error("Error handling new activity:", error);
        }
      },
      "activities-page"
    );
    const deleteSubscription = realtimeService.subscribeToTable(
      "business_activities",
      (payload) => {
        console.log("Received realtime activity delete:", payload);

        // Remove the deleted activity from the list
        if (payload.old && "id" in payload.old && payload.old.id) {
          const deletedActivity = payload.old as unknown as BusinessActivity;
          // Use a timeout to ensure the animation completes before removing the item
          setTimeout(() => {
            setActivities((prev) =>
              prev.filter((a) => a.id !== deletedActivity.id)
            );

            // Update unread count if the deleted activity was unread
            if (deletedActivity.is_read === false) {
              setUnreadCount((prev) => Math.max(0, prev - 1));
            }
          }, 300); // Match the animation duration in the motion.div
        }
      },
      {
        event: "DELETE",
        filter: `business_profile_id=eq.${businessProfileId}`,
      },
      `activities-page-delete-${businessProfileId}`
    );

    return () => {
      insertSubscription.unsubscribe();
      deleteSubscription.unsubscribe();
    };
  }, [businessProfileId, filterBy, refreshActivities, sortBy]);

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value as "all" | "likes" | "subscriptions" | "ratings");

    // Update filter based on tab
    let newFilter: "all" | "like" | "subscribe" | "rating" | "unread" = "all";
    switch (value) {
      case "likes":
        newFilter = "like";
        break;
      case "subscriptions":
        newFilter = "subscribe";
        break;
      case "ratings":
        newFilter = "rating";
        break;
      default:
        newFilter = "all";
    }

    // Set the new filter and page
    setFilterBy(newFilter);
    setPage(1);
    // refreshActivities will handle clearing activities and loading state
    refreshActivities(1, newFilter, sortBy);
  };

  // Handle sort change
  const handleSortChange = (value: string) => {
    const newSortBy = value as "newest" | "oldest" | "unread_first";

    // Set the new sort and page
    setSortBy(newSortBy);
    setPage(1);
    // refreshActivities will handle clearing activities and loading state
    refreshActivities(1, filterBy, newSortBy);
  };

  // Load more activities
  const loadMore = useCallback(() => {
    const nextPage = page + 1;
    setPage(nextPage);
    refreshActivities(nextPage, filterBy, sortBy);
  }, [page, filterBy, sortBy, refreshActivities]);

  // We've removed the mark all as read function
  // as activities are now auto-marked as read when the page is viewed

  // Container animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  // Set up intersection observer for infinite scrolling
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !loading) {
          loadMore();
        }
      },
      { threshold: 0.1 }
    );

    const currentRef = loadMoreRef.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [hasMore, loading, loadMore]);

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-8"
    >
      {/* Modern SaaS Header - Full Width */}
      <motion.div
        className="flex flex-col lg:flex-row lg:items-center justify-between gap-6 py-8 border-b border-neutral-200/60 dark:border-neutral-800/60"
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.1 }}
      >
        <div className="space-y-1">
          <div className="flex items-center gap-3 mb-2">
            <div className="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20">
              <Bell className="w-5 h-5 text-primary" />
            </div>
            <div className="h-8 w-px bg-gradient-to-b from-neutral-200 to-transparent dark:from-neutral-700" />
            <div className="text-sm font-medium text-neutral-500 dark:text-neutral-400 tracking-wide uppercase">
              Activity Management
            </div>
          </div>
          <h1 className="text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-neutral-50 tracking-tight">
            Business Activities
          </h1>
          <p className="text-lg text-neutral-600 dark:text-neutral-400 max-w-2xl leading-relaxed">
            Monitor and track all customer interactions, engagement metrics, and business activity in real-time.
          </p>
        </div>

        <div className="flex items-center gap-3">
          {unreadCount > 0 && (
            <Badge
              variant="secondary"
              className="bg-primary/10 text-primary border border-primary/20 px-3 py-1.5 font-medium shadow-sm"
            >
              {unreadCount} unread
            </Badge>
          )}
        </div>
      </motion.div>

      {/* Enhanced Tabs and Filters Section - Full Width */}
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2 }}
      >
        <Tabs
          defaultValue="all"
          value={activeTab}
          onValueChange={handleTabChange}
          className="w-full"
        >
          {/* Modern Tabs and Filters Layout */}
          <div className="flex flex-col gap-8 mb-8">
            {/* Enhanced Tabs */}
            <div className="w-full flex justify-center">
              <TabsList className="grid grid-cols-4 w-full max-w-2xl p-1.5 rounded-2xl bg-neutral-100/80 dark:bg-neutral-800/80 backdrop-blur-sm border border-neutral-200/50 dark:border-neutral-700/50">
                <TabsTrigger
                  value="all"
                  className="rounded-xl py-3 px-4 font-medium flex items-center justify-center data-[state=active]:bg-white data-[state=active]:text-neutral-900 data-[state=active]:shadow-lg data-[state=active]:border-primary/20 dark:data-[state=active]:bg-neutral-900 dark:data-[state=active]:text-neutral-50 transition-all duration-200"
                >
                  All Activities
                </TabsTrigger>
                <TabsTrigger
                  value="likes"
                  className="flex items-center justify-center gap-2 rounded-xl py-3 px-4 font-medium data-[state=active]:bg-white data-[state=active]:text-neutral-900 data-[state=active]:shadow-lg data-[state=active]:border-primary/20 dark:data-[state=active]:bg-neutral-900 dark:data-[state=active]:text-neutral-50 transition-all duration-200"
                >
                  <Heart className="w-4 h-4 text-rose-500" />
                  <span className="hidden sm:inline">Likes</span>
                </TabsTrigger>
                <TabsTrigger
                  value="subscriptions"
                  className="flex items-center justify-center gap-2 rounded-xl py-3 px-4 font-medium data-[state=active]:bg-white data-[state=active]:text-neutral-900 data-[state=active]:shadow-lg data-[state=active]:border-primary/20 dark:data-[state=active]:bg-neutral-900 dark:data-[state=active]:text-neutral-50 transition-all duration-200"
                >
                  <Users className="w-4 h-4 text-blue-500" />
                  <span className="hidden sm:inline">Subscriptions</span>
                </TabsTrigger>
                <TabsTrigger
                  value="ratings"
                  className="flex items-center justify-center gap-2 rounded-xl py-3 px-4 font-medium data-[state=active]:bg-white data-[state=active]:text-neutral-900 data-[state=active]:shadow-lg data-[state=active]:border-primary/20 dark:data-[state=active]:bg-neutral-900 dark:data-[state=active]:text-neutral-50 transition-all duration-200"
                >
                  <Star className="w-4 h-4 text-amber-500" />
                  <span className="hidden sm:inline">Ratings</span>
                </TabsTrigger>
              </TabsList>
            </div>

            {/* Enhanced Filters and Actions */}
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 p-6 bg-neutral-50/50 dark:bg-neutral-900/20 rounded-2xl border border-neutral-200/50 dark:border-neutral-700/50">
              {/* Sort Controls */}
              <div className="flex items-center gap-3">
                <span className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                  Sort by:
                </span>
                <Select value={sortBy} onValueChange={handleSortChange}>
                  <SelectTrigger className="w-[180px] bg-white dark:bg-neutral-800 border-neutral-200 dark:border-neutral-700 rounded-xl shadow-sm hover:shadow-md transition-all duration-200">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent className="rounded-xl border-neutral-200 dark:border-neutral-700">
                    <SelectItem value="newest" className="rounded-lg">Newest First</SelectItem>
                    <SelectItem value="oldest" className="rounded-lg">Oldest First</SelectItem>
                    <SelectItem value="unread_first" className="rounded-lg">Unread First</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center gap-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setPage(1);
                    refreshActivities(1, filterBy, sortBy);
                  }}
                  disabled={loading}
                  className="border-neutral-200 dark:border-neutral-700 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 px-4 py-2"
                >
                  <RefreshCw
                    className={cn("w-4 h-4 mr-2", loading && "animate-spin")}
                  />
                  <span>Refresh</span>
                </Button>
              </div>
            </div>
          </div>

          {/* Activities List */}
          <TabsContent value="all" className="mt-0">
            <ActivityList
              activities={activities}
              loading={loading}
              hasMore={hasMore}
              loadMoreRef={loadMoreRef}
              onActivityViewed={markAsViewed}
              activitiesSnapshot={activitiesSnapshot}
            />
          </TabsContent>

          <TabsContent value="likes" className="mt-0">
            <ActivityList
              activities={activities}
              loading={loading}
              hasMore={hasMore}
              loadMoreRef={loadMoreRef}
              onActivityViewed={markAsViewed}
              activitiesSnapshot={activitiesSnapshot}
            />
          </TabsContent>

          <TabsContent value="subscriptions" className="mt-0">
            <ActivityList
              activities={activities}
              loading={loading}
              hasMore={hasMore}
              loadMoreRef={loadMoreRef}
              onActivityViewed={markAsViewed}
              activitiesSnapshot={activitiesSnapshot}
            />
          </TabsContent>

          <TabsContent value="ratings" className="mt-0">
            <ActivityList
              activities={activities}
              loading={loading}
              hasMore={hasMore}
              loadMoreRef={loadMoreRef}
              onActivityViewed={markAsViewed}
              activitiesSnapshot={activitiesSnapshot}
            />
          </TabsContent>
        </Tabs>
      </motion.div>
    </motion.div>
  );
}

// Enhanced Activity Skeleton Component
function ActivitySkeleton() {
  return (
    <div className="border border-neutral-200/60 dark:border-neutral-700/60 rounded-2xl p-6 bg-white dark:bg-neutral-900/50 backdrop-blur-sm shadow-sm transition-all duration-300 overflow-hidden">
      <div className="flex gap-5 items-start">
        {/* Enhanced Avatar Skeleton */}
        <div className="shrink-0">
          <Skeleton className="h-12 w-12 rounded-xl flex-shrink-0" />
        </div>

        <div className="flex-1 min-w-0">
          {/* Enhanced User Info */}
          <div className="flex flex-col space-y-2 mb-3">
            <div className="flex items-center gap-2 flex-wrap">
              <Skeleton className="h-6 w-36" />
              <Skeleton className="h-6 w-20 rounded-lg" />
            </div>

            <Skeleton className="h-4 w-4/5" />
          </div>

          {/* Enhanced Footer */}
          <div className="flex flex-wrap items-center justify-between mt-4 gap-3">
            <div className="flex items-center flex-wrap gap-3">
              <Skeleton className="h-7 w-24 rounded-lg" />
              <Skeleton className="h-4 w-20" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Activity List Component
function ActivityList({
  activities,
  loading,
  hasMore,
  loadMoreRef,
  onActivityViewed,
  activitiesSnapshot,
}: {
  activities: BusinessActivity[];
  loading: boolean;
  hasMore: boolean;
  loadMoreRef: React.RefObject<HTMLDivElement | null>;
  onActivityViewed?: (_activityId: string) => void;
  activitiesSnapshot: BusinessActivity[];
}) {
  // Separate activities into new and old based on INITIAL read status when page loaded
  const newActivities = activities.filter((activity) => {
    const snapshotActivity = activitiesSnapshot.find(
      (a) => a.id === activity.id
    );
    return snapshotActivity?.is_read === false;
  });
  const oldActivities = activities.filter((activity) => {
    const snapshotActivity = activitiesSnapshot.find(
      (a) => a.id === activity.id
    );
    return snapshotActivity?.is_read === true;
  });

  // Enhanced section header
  const renderSectionHeader = (title: string) => (
    <div className="px-6 py-4 bg-gradient-to-r from-neutral-50 to-neutral-100/50 dark:from-neutral-800/50 dark:to-neutral-900/50 border-b border-neutral-200/60 dark:border-neutral-700/60 rounded-t-2xl">
      <h3 className="text-sm font-bold text-neutral-800 dark:text-neutral-200 tracking-wide uppercase">
        {title}
      </h3>
    </div>
  );

  return (
    <div className="space-y-4">
      {/* Loading Skeletons - Show when no activities and loading */}
      {activities.length === 0 && loading ? (
        <div className="space-y-4">
          <ActivitySkeleton />
          <ActivitySkeleton />
          <ActivitySkeleton />
          <ActivitySkeleton />
        </div>
      ) : activities.length === 0 && !loading ? (
        // Enhanced Empty state - No activities and not loading
        <div className="flex flex-col items-center justify-center py-20 text-center">
          <div className="relative mb-8">
            <div className="absolute -inset-8 rounded-full bg-gradient-to-r from-primary/10 to-primary/5 animate-pulse blur-xl"></div>
            <div className="relative z-10 flex items-center justify-center w-20 h-20 rounded-2xl bg-gradient-to-br from-primary/10 to-primary/5 border border-primary/20 shadow-lg">
              <Bell className="w-10 h-10 text-primary" />
            </div>
          </div>
          <h3 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100 mb-3">
            No activities yet
          </h3>
          <p className="text-lg text-neutral-600 dark:text-neutral-400 max-w-lg leading-relaxed mb-2">
            When customers interact with your business, their activity will appear here.
          </p>
          <p className="text-sm text-neutral-500 dark:text-neutral-500 max-w-md leading-relaxed">
            Share your business card to start receiving likes, subscriptions, and ratings from customers.
          </p>
        </div>
      ) : (
        // Activity Items with Sections - Show when we have activities
        <>
          <AnimatePresence initial={false} mode="wait">
            {/* New Activities Section */}
            {newActivities.length > 0 && (
              <div key="new-section">
                {renderSectionHeader("New")}
                {newActivities.map((activity) =>
                  activity && activity.id ? (
                    <motion.div
                      key={activity.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, height: 0, overflow: "hidden" }}
                      transition={{ duration: 0.3 }}
                      className="mb-4 last:mb-0"
                    >
                      <ActivityItem
                        activity={activity}
                        onView={onActivityViewed}
                      />
                    </motion.div>
                  ) : null
                )}
              </div>
            )}

            {/* Old Activities Section */}
            {oldActivities.length > 0 && (
              <div key="old-section">
                {renderSectionHeader("Earlier")}
                {oldActivities.map((activity) =>
                  activity && activity.id ? (
                    <motion.div
                      key={activity.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, height: 0, overflow: "hidden" }}
                      transition={{ duration: 0.3 }}
                      className="mb-4 last:mb-0"
                    >
                      <ActivityItem
                        activity={activity}
                        onView={onActivityViewed}
                      />
                    </motion.div>
                  ) : null
                )}
              </div>
            )}
          </AnimatePresence>

          {/* Enhanced Infinite Scroll Trigger */}
          {hasMore && (
            <div
              ref={loadMoreRef}
              className="flex justify-center items-center py-8"
            >
              {loading && (
                <div className="flex flex-col items-center">
                  <div className="relative">
                    <div className="absolute -inset-2 rounded-full bg-primary/20 animate-pulse blur-md"></div>
                    <Loader2 className="h-10 w-10 animate-spin text-primary relative z-10" />
                  </div>
                  <span className="text-sm text-neutral-700 dark:text-neutral-300 mt-4 font-medium">
                    Loading more activities...
                  </span>
                </div>
              )}
            </div>
          )}

          {/* Enhanced End of List Message */}
          {!hasMore && activities.length > 0 && (
            <div className="text-center py-12 border-t border-neutral-200/60 dark:border-neutral-700/60 mt-8 bg-gradient-to-b from-transparent to-neutral-50/50 dark:to-neutral-900/20 rounded-2xl">
              <div className="flex items-center justify-center mb-3">
                <div className="w-2 h-2 rounded-full bg-neutral-300 dark:bg-neutral-600 mx-1"></div>
                <div className="w-2 h-2 rounded-full bg-neutral-300 dark:bg-neutral-600 mx-1"></div>
                <div className="w-2 h-2 rounded-full bg-neutral-300 dark:bg-neutral-600 mx-1"></div>
              </div>
              <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                You&apos;ve reached the end of your activities
              </p>
              <p className="text-xs text-neutral-500 dark:text-neutral-500 mt-2 max-w-sm mx-auto leading-relaxed">
                New activities will appear here when customers interact with your business
              </p>
            </div>
          )}
        </>
      )}
    </div>
  );
}
