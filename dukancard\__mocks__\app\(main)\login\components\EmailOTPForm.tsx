import React from 'react';

export function EmailOTPForm({ step, email, countdown, isPending, onEmailSubmit, onOTPSubmit, onResendOTP, onBackToEmail }: any) {
  return (
    <div data-testid="email-otp-form">
      {step === 'email' ? (
        <button onClick={() => onEmailSubmit({ email: '<EMAIL>' })}>Submit Email</button>
      ) : (
        <div>
          <span>OTP for {email}</span>
          <button onClick={() => onOTPSubmit({ email, otp: '123456' })}>Submit OTP</button>
          <button
            onClick={onResendOTP}
            disabled={countdown > 0}
            data-testid="resend-otp-button"
          >
            {countdown > 0 ? `Resend OTP in ${countdown}s` : 'Resend OTP'}
          </button>
          <button onClick={onBackToEmail}>Back to Email</button>
        </div>
      )}
      {isPending && <span>Loading...</span>}
    </div>
  );
}