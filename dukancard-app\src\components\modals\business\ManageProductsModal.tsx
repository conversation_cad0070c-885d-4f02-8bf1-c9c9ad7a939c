import React, { useState, useRef } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Modal,
  TextInput,
  Animated,
} from "react-native";
import {
  X,
  Search,
  Plus,
  SortAsc,
  Filter,
  ArrowLeft,
} from "lucide-react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { useAuth } from "@/src/contexts/AuthContext";
import { useToast } from "@/src/components/ui/Toast";
import LoadingOverlay from "@/src/components/common/LoadingOverlay";
import {
  addProductService,
  updateProductService,
} from "@/backend/supabase/services/business/businessProductsService";
import { getProductVariants } from "@/backend/supabase/services/business/variantService";
import {
  getUserPlanLimits,
  PlanLimitInfo
} from "@/backend/supabase/services/business/planLimitService";
import { ProductsServices, ProductVariants } from "@/src/types/database";
import { createManageProductsModalStyles } from "@/styles/modals/business/manage-products-modal";
import ProductsList from "./components/ProductsList";
import ProductForm from "./components/ProductForm";
import VariantForm from "./components/VariantForm";
import ProductsSortBottomSheet, {
  ProductsSortBottomSheetRef,
  ProductSortOption,
} from "./components/ProductsSortBottomSheet";
import { logError, handleNetworkError } from "@/src/utils/errorHandling";

// Modal view types
type ModalView = "list" | "add" | "edit";

interface ManageProductsModalProps {
  visible: boolean;
  onClose: () => void;
}

export default function ManageProductsModal({
  visible,
  onClose,
}: ManageProductsModalProps) {
  const theme = useTheme();
  const { user } = useAuth();
  const toast = useToast();
  const styles = createManageProductsModalStyles(theme);

  // Refs
  const sortBottomSheetRef = useRef<ProductsSortBottomSheetRef>(null);

  // State management
  const [currentView, setCurrentView] = useState<ModalView>("list");
  const [selectedProduct, setSelectedProduct] =
    useState<ProductsServices | null>(null);
  const [selectedProductVariants, setSelectedProductVariants] =
    useState<ProductVariants[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<ProductSortOption>("newest");
  const [loading, setLoading] = useState(false);
  const [loadingVariants, setLoadingVariants] = useState(false);
  const [planLimitInfo, setPlanLimitInfo] = useState<PlanLimitInfo | null>(null);
  const [loadingPlanLimits, setLoadingPlanLimits] = useState(false);

  // Load plan limits when modal opens
  const loadPlanLimits = async () => {
    try {
      setLoadingPlanLimits(true);
      const result = await getUserPlanLimits();

      if (result.success && result.data) {
        setPlanLimitInfo(result.data);
      } else {
        console.warn("Failed to load plan limits:", result.error);
        setPlanLimitInfo(null);
      }
    } catch (error) {
      console.error("Error loading plan limits:", error);
      setPlanLimitInfo(null);
    } finally {
      setLoadingPlanLimits(false);
    }
  };

  // Reset state when modal opens/closes
  React.useEffect(() => {
    if (visible) {
      setCurrentView("list");
      setSelectedProduct(null);
      setSelectedProductVariants([]);
      setSearchTerm("");
      setSortBy("newest");
      setPlanLimitInfo(null);
      loadPlanLimits();
    }
  }, [visible]);

  // Load variants when editing a product
  const loadProductVariants = async (productId: string) => {
    try {
      setLoadingVariants(true);
      const result = await getProductVariants(productId);

      if (result.success && result.data) {
        setSelectedProductVariants(result.data);
      } else {
        setSelectedProductVariants([]);
        if (result.error) {
          console.warn("Failed to load variants:", result.error);
        }
      }
    } catch (error) {
      console.error("Error loading variants:", error);
      setSelectedProductVariants([]);
    } finally {
      setLoadingVariants(false);
    }
  };

  // Handler functions
  const handleAdd = () => {
    setSelectedProduct(null);
    setCurrentView("add");
  };

  const handleEdit = async (product: ProductsServices) => {
    setSelectedProduct(product);
    setCurrentView("edit");

    // Load variants for this product
    await loadProductVariants(product.id);
  };

  const handleDelete = (productId: string) => {
    // This will be handled by the ProductsList component
    console.log("Delete product:", productId);
  };

  const handleSortPress = () => {
    sortBottomSheetRef.current?.present();
  };

  const handleSortSelect = (newSortBy: ProductSortOption) => {
    setSortBy(newSortBy);
  };

  const handleSearch = () => {
    // Manual search trigger - will be handled by ProductsList component
    // The search will be triggered when user presses enter or clicks search icon
  };

  const handleClearSearch = () => {
    setSearchTerm("");
  };

  const handleFormSubmit = async (formData: any) => {
    if (!user) return;

    setLoading(true);
    try {
      let result;

      // Extract variants from formData if present
      const { variants, ...productData } = formData;

      if (selectedProduct) {
        // For updates, include variants in the data
        result = await updateProductService(selectedProduct.id, {
          ...productData,
          variants: variants || [],
        });
      } else {
        // For new products, include variants in the data
        result = await addProductService({
          ...productData,
          variants: variants || [],
        });
      }

      if (result.success) {
        toast.success(
          "Success",
          `Product ${selectedProduct ? "updated" : "added"} successfully`
        );
        setCurrentView("list");
        setSelectedProduct(null);
        setSelectedProductVariants([]);
        // Refresh plan limits after successful operation
        loadPlanLimits();
      } else {
        const errorMessage = result.error || "Failed to save product";

        // Enhanced error handling similar to Next.js
        if (errorMessage.includes("Image exceeds 15MB limit")) {
          toast.error("Image too large", "Please select images smaller than 15MB each");
        } else if (errorMessage.includes("Invalid file type")) {
          toast.error("Invalid file type", "Please select JPG, PNG, WebP, or GIF images");
        } else if (errorMessage.includes("Body exceeded")) {
          toast.error("Upload size limit exceeded", "Please try uploading fewer images or smaller file sizes");
        } else if (errorMessage.includes("reached the limit")) {
          toast.error("Plan Limit Reached", errorMessage);
        } else if (errorMessage.includes("already exists")) {
          toast.error("Duplicate Product", errorMessage);
        } else {
          toast.error("Error", errorMessage);
        }
      }
    } catch (error) {
      const appError = handleNetworkError(error);
      toast.error("Error", appError.message);
      logError(appError, "ManageProductsModal.handleFormSubmit");
    } finally {
      setLoading(false);
    }
  };

  const handleFormCancel = () => {
    setCurrentView("list");
    setSelectedProduct(null);
    setSelectedProductVariants([]);
  };

  return (
    <Modal
      visible={visible}
      onRequestClose={onClose}
      presentationStyle="fullScreen"
      animationType="slide"
    >
      <SafeAreaView style={styles.safeArea}>
        <KeyboardAvoidingView
          style={styles.keyboardAvoidingView}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
        >
          {/* Enhanced Header with Back Navigation */}
          <View style={styles.header}>
            {(currentView === "add" || currentView === "edit") ? (
              <TouchableOpacity onPress={handleFormCancel} style={styles.backButton}>
                <ArrowLeft size={24} color={theme.isDark ? "#FFF" : "#000"} />
              </TouchableOpacity>
            ) : (
              <View style={{ width: 40 }} />
            )}
            <Text style={styles.headerTitle}>
              {currentView === "add"
                ? "Add Product"
                : currentView === "edit"
                ? "Edit Product"
                : "Manage Products"}
            </Text>
            {(currentView === "add" || currentView === "edit") ? (
              <View style={{ width: 40 }} />
            ) : (
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <X size={24} color={theme.isDark ? "#FFF" : "#000"} />
              </TouchableOpacity>
            )}
          </View>

          {currentView === "list" && (
            <ProductsList
              searchTerm={searchTerm}
              sortBy={sortBy}
              onEdit={handleEdit}
              onDelete={handleDelete}
              onSearchChange={setSearchTerm}
              onSearch={handleSearch}
              onClearSearch={handleClearSearch}
              onSortPress={handleSortPress}
              onAddPress={handleAdd}
            />
          )}

          {(currentView === "add" || currentView === "edit") && (
            <ProductForm
              product={selectedProduct}
              variants={selectedProductVariants}
              onSubmit={handleFormSubmit}
              onCancel={handleFormCancel}
              loading={loading || loadingVariants}
              planLimitInfo={planLimitInfo}
            />
          )}

          {(loading || loadingPlanLimits) && (
            <LoadingOverlay
              textColor={theme.isDark ? "#FFF" : "#000"}
              backgroundColor="transparent"
            />
          )}
        </KeyboardAvoidingView>
      </SafeAreaView>

      {/* Sort Bottom Sheet */}
      <ProductsSortBottomSheet
        ref={sortBottomSheetRef}
        sortBy={sortBy}
        onSortSelect={handleSortSelect}
      />
    </Modal>
  );
}
