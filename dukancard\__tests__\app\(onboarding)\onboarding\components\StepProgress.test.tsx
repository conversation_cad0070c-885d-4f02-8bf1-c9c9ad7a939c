import React from 'react';
import { render, screen } from '@testing-library/react';
import { StepProgress } from '../../../../../app/(onboarding)/onboarding/components/StepProgress';
import { STEP_TITLES, getStepDescription } from '../../../../../app/(onboarding)/onboarding/constants/onboardingSteps';

// Mock the constants and utility function
jest.mock('../../../../../app/(onboarding)/onboarding/constants/onboardingSteps', () => ({
  STEP_TITLES: [
    "Business Details",
    "Card Information",
    "Address & Status",
    "Choose Your Plan",
  ],
  getStepDescription: jest.fn((stepIndex, hasExistingSubscription) => {
    if (stepIndex === 3 && hasExistingSubscription) {
      return "Review your current plan and complete setup";
    }
    const descriptions = [
      "Let's set up your business profile",
      "Let's create your unique digital business card",
      "Complete your business address and status",
      "Select the perfect plan for your business",
    ];
    return descriptions[stepIndex] || "";
  }),
}));

describe('StepProgress', () => {
  const defaultProps = {
    existingData: null,
    _isLoadingExistingData: false,
  };

  beforeEach(() => {
    // Clear mock calls before each test
    (getStepDescription as jest.Mock).mockClear();
  });

  it('renders correctly for step 1', () => {
    render(<StepProgress {...defaultProps} currentStep={1} />);

    expect(screen.getByText('Step 1 of 4')).toBeInTheDocument();
    expect(screen.getByText('Business Details')).toBeInTheDocument();
    expect(screen.getByText("Let's set up your business profile")).toBeInTheDocument();

    // Check step circles
    expect(screen.getByText('1').parentElement).toHaveClass('bg-primary/20'); // Current step
    expect(screen.getByText('2').parentElement).toHaveClass('bg-muted'); // Future step
    expect(screen.getByText('3').parentElement).toHaveClass('bg-muted');
    expect(screen.getByText('4').parentElement).toHaveClass('bg-muted');

    expect(getStepDescription).toHaveBeenCalledWith(0, undefined); // stepIndex is 0-based
  });

  it('renders correctly for step 2', () => {
    render(<StepProgress {...defaultProps} currentStep={2} />);

    expect(screen.getByText('Step 2 of 4')).toBeInTheDocument();
    expect(screen.getByText('Card Information')).toBeInTheDocument();
    expect(screen.getByText("Let's create your unique digital business card")).toBeInTheDocument();

    // Check step circles
    expect(screen.getByLabelText('Check')).toBeInTheDocument(); // Step 1 is completed
    expect(screen.getByText('2')).toHaveClass('bg-primary/20'); // Current step
    expect(screen.getByText('3')).toHaveClass('bg-muted');
    expect(screen.getByText('4')).toHaveClass('bg-muted');

    expect(getStepDescription).toHaveBeenCalledWith(1, undefined);
  });

  it('renders correctly for step 4', () => {
    render(<StepProgress {...defaultProps} currentStep={4} />);

    expect(screen.getByText('Step 4 of 4')).toBeInTheDocument();
    expect(screen.getByText('Choose Your Plan')).toBeInTheDocument();
    expect(screen.getByText("Select the perfect plan for your business")).toBeInTheDocument();

    // Check step circles
    expect(screen.getAllByLabelText('Check').length).toBe(3); // Steps 1, 2, 3 are completed
    expect(screen.getByText('4')).toHaveClass('bg-primary/20'); // Current step

    expect(getStepDescription).toHaveBeenCalledWith(3, undefined);
  });

  it('displays correct description when existingData.hasExistingSubscription is true for step 4', () => {
    const props = {
      ...defaultProps,
      currentStep: 4,
      existingData: { hasExistingSubscription: true } as any, // Cast to any to simplify mock
    };
    render(<StepProgress {...props} />);

    expect(screen.getByText('Review your current plan and complete setup')).toBeInTheDocument();
    expect(getStepDescription).toHaveBeenCalledWith(3, true);
  });

  it('does not render the progress line for the last step', () => {
    const { container } = render(<StepProgress {...defaultProps} currentStep={4} />);
    expect(screen.queryByTestId('progress-line')).not.toBeInTheDocument();
  });
});