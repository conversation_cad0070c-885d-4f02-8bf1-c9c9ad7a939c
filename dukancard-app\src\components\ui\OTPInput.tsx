import { useTheme } from '@/src/hooks/useTheme';
import { useScreenDimensions } from '@/src/hooks/use-mobile';
import React, { useEffect, useRef, useState } from 'react';
import {
    Pressable,
    StyleSheet,
    Text,
    TextInput,
    View,
} from 'react-native';

interface OTPInputProps {
  length?: number;
  onComplete: (otp: string) => void;
  onChangeText?: (otp: string) => void;
  error?: string;
  disabled?: boolean;
  autoFocus?: boolean;
}

export function OTPInput({
  length = 6,
  onComplete,
  onChangeText,
  error,
  disabled = false,
  autoFocus = true,
}: OTPInputProps) {
  const [otp, setOtp] = useState<string[]>(new Array(length).fill(''));
  const [activeIndex, setActiveIndex] = useState(0);
  const inputRefs = useRef<(TextInput | null)[]>([]);
  const theme = useTheme();
  const { width } = useScreenDimensions();

  useEffect(() => {
    if (autoFocus && inputRefs.current[0]) {
      inputRefs.current[0].focus();
    }
  }, [autoFocus]);

  const responsiveFontSize = width < 375 ? 18 : width < 768 ? 20 : 24;

  const handleChange = (text: string, index: number) => {
    if (disabled) return;

    const newOtp = [...otp];
    
    // Handle paste operation
    if (text.length > 1) {
      const pastedText = text.slice(0, length);
      const pastedArray = pastedText.split('');
      
      for (let i = 0; i < length; i++) {
        newOtp[i] = pastedArray[i] || '';
      }
      
      setOtp(newOtp);
      const otpString = newOtp.join('');
      onChangeText?.(otpString);
      
      if (otpString.length === length) {
        onComplete(otpString);
      } else {
        // Focus on the next empty input
        const nextIndex = pastedArray.length < length ? pastedArray.length : length - 1;
        inputRefs.current[nextIndex]?.focus();
        setActiveIndex(nextIndex);
      }
      return;
    }

    // Handle single character input
    if (text.length <= 1 && /^\d*$/.test(text)) {
      newOtp[index] = text;
      setOtp(newOtp);
      
      const otpString = newOtp.join('');
      onChangeText?.(otpString);

      if (text && index < length - 1) {
        inputRefs.current[index + 1]?.focus();
        setActiveIndex(index + 1);
      }

      if (otpString.length === length) {
        onComplete(otpString);
      }
    }
  };

  const handleKeyPress = (key: string, index: number) => {
    if (disabled) return;

    if (key === 'Backspace') {
      const newOtp = [...otp];
      
      if (otp[index]) {
        newOtp[index] = '';
        setOtp(newOtp);
        onChangeText?.(newOtp.join(''));
      } else if (index > 0) {
        newOtp[index - 1] = '';
        setOtp(newOtp);
        onChangeText?.(newOtp.join(''));
        inputRefs.current[index - 1]?.focus();
        setActiveIndex(index - 1);
      }
    }
  };

  const handleFocus = (index: number) => {
    setActiveIndex(index);
  };

  const handlePress = (index: number) => {
    if (disabled) return;
    inputRefs.current[index]?.focus();
    setActiveIndex(index);
  };

  const getInputStyle = (index: number) => {
    const isActive = activeIndex === index;
    const hasValue = otp[index] !== '';
    const hasError = !!error;

    return [
      styles.input,
      {
        backgroundColor: theme.colors.card,
        color: theme.colors.textPrimary,
        borderColor: hasError
          ? theme.colors.error
          : isActive || hasValue
          ? theme.colors.primary
          : theme.colors.border,
        borderWidth: hasError ? 2 : isActive ? 2 : 1,
        opacity: disabled ? 0.5 : 1,
        shadowColor: isActive ? theme.colors.primary : 'transparent',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: isActive ? 0.1 : 0,
        shadowRadius: 4,
        elevation: isActive ? 2 : 0,
        fontSize: responsiveFontSize,
      },
    ];
  };

  return (
    <View style={styles.container}>
      <View style={styles.inputContainer}>
        {otp.map((digit, index) => (
          <Pressable key={index} onPress={() => handlePress(index)} style={styles.inputWrapper}>
            <TextInput
              testID={`otp-input-${index}`}
              ref={(ref) => {
                inputRefs.current[index] = ref;
              }}
              style={getInputStyle(index)}
              value={digit}
              onChangeText={(text) => handleChange(text, index)}
              onKeyPress={({ nativeEvent }) => handleKeyPress(nativeEvent.key, index)}
              onFocus={() => handleFocus(index)}
              keyboardType="numeric"
              maxLength={1}
              selectTextOnFocus
              editable={!disabled}
              textAlign="center"
              autoComplete="one-time-code"
            />
          </Pressable>
        ))}
      </View>
      {error && (
        <Text style={[styles.errorText, { color: theme.colors.error }]}>
          {error}
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  inputContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
    width: '100%',
  },
  inputWrapper: {
    flex: 1,
  },
  input: {
    flex: 1,
    aspectRatio: 1,
    borderRadius: 12,
    
    fontWeight: '600',
    textAlign: 'center',
    borderWidth: 1,
  },
  errorText: {
    fontSize: 14,
    fontWeight: '500',
    marginTop: 8,
    textAlign: 'center',
  },
});
