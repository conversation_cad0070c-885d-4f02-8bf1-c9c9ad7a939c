"use client";

import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Image as ImageIcon,
  MapPin,
  X,
  Send,
  Loader2
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { toast } from "sonner";
import { createClient } from "@/utils/supabase/client";
import { createCustomerPost, updateCustomerPost } from "@/lib/actions/customerPosts";
import { useRouter } from "next/navigation";
import Image from "next/image";
import LocationDisplay from "./forms/LocationDisplay";

interface SocialMediaPostCreatorProps {
  customerName?: string;
  onPostCreated?: () => void;
}

export default function SocialMediaPostCreator({ 
  customerName, 
  onPostCreated 
}: SocialMediaPostCreatorProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [content, setContent] = useState("");
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [customerAvatar, setCustomerAvatar] = useState<string | null>(null);
  const [customerDisplayName, setCustomerDisplayName] = useState(customerName || "");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [detectedImageUrl, setDetectedImageUrl] = useState<string | null>(null);
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const router = useRouter();

  // Character limit
  const MAX_CHARS = 2000;
  const charCount = content.length;
  const isOverLimit = charCount > MAX_CHARS;

  // Fetch customer profile
  useEffect(() => {
    const fetchCustomerProfile = async () => {
      const supabase = createClient();
      const { data: { user } } = await supabase.auth.getUser();
      
      if (user) {
        const { data: profile } = await supabase
          .from('customer_profiles')
          .select('name, avatar_url')
          .eq('id', user.id)
          .single();
        
        if (profile) {
          setCustomerDisplayName(profile.name || customerName || "User");
          setCustomerAvatar(profile.avatar_url);
        }
      }
    };

    fetchCustomerProfile();
  }, [customerName]);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [content]);

  // Focus textarea when expanded
  useEffect(() => {
    if (isExpanded && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [isExpanded]);

  const handleExpand = () => {
    setIsExpanded(true);
  };

  const handleCollapse = () => {
    if (!content.trim() && !imageUrl) {
      setIsExpanded(false);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }

    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('Image size must be less than 5MB');
      return;
    }

    // Store file and create preview URL
    setSelectedFile(file);
    const preview = URL.createObjectURL(file);
    setPreviewUrl(preview);
    setImageUrl(null); // Clear any existing URL
  };

  const handleImageRemove = () => {
    setSelectedFile(null);
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
    setPreviewUrl(null);
    setImageUrl(null);
    setDetectedImageUrl(null);
  };

  // Function to detect image URLs in content
  const detectImageUrl = (text: string) => {
    const imageUrlRegex = /(https?:\/\/[^\s]+\.(?:jpg|jpeg|png|gif|webp)(?:\?[^\s]*)?)/i;
    const match = text.match(imageUrlRegex);
    return match ? match[1] : null;
  };

  // Handle content change with URL detection
  const handleContentChange = (newContent: string) => {
    setContent(newContent);

    // Detect image URL in content
    const detectedUrl = detectImageUrl(newContent);
    if (detectedUrl && detectedUrl !== detectedImageUrl) {
      setDetectedImageUrl(detectedUrl);
      // Only set as imageUrl if no file is selected
      if (!selectedFile) {
        setImageUrl(detectedUrl);
      }
    } else if (!detectedUrl && detectedImageUrl) {
      setDetectedImageUrl(null);
      // Only clear imageUrl if it was from detected URL (not from file upload)
      if (!selectedFile) {
        setImageUrl(null);
      }
    }
  };

  const checkCustomerProfile = async () => {
    const supabase = createClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      toast.error('Please log in to continue');
      return false;
    }

    const { data: profile, error } = await supabase
      .from('customer_profiles')
      .select('name, pincode, city, state, locality')
      .eq('id', user.id)
      .single();

    if (error) {
      toast.error('Failed to check customer profile');
      return false;
    }

    if (!profile?.name || profile.name.trim() === '') {
      toast.error('Please complete your name in your profile before creating posts');
      router.push('/dashboard/customer/profile');
      return false;
    }

    if (!profile?.pincode || !profile?.city || !profile?.state || !profile?.locality) {
      toast.error('Please complete your address in your profile before creating posts');
      router.push('/dashboard/customer/profile');
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!content.trim() && !previewUrl && !detectedImageUrl) {
      toast.error('Please add some content or an image');
      return;
    }

    if (isOverLimit) {
      toast.error('Post content is too long');
      return;
    }

    setIsSubmitting(true);

    try {
      const hasValidProfile = await checkCustomerProfile();
      if (!hasValidProfile) {
        setIsSubmitting(false);
        return;
      }

      let result;

      if (selectedFile && !imageUrl) {
        // Handle file upload for new posts
        const createResult = await createCustomerPost({
          content: content.trim(),
          image_url: null, // Create post first without image
          mentioned_business_ids: [],
        });

        if (createResult.success && createResult.data) {
          const postData = createResult.data as { id: string; created_at: string };

          // Upload the image using the post ID
          const supabase = createClient();
          const { data: { user } } = await supabase.auth.getUser();

          if (user) {
            const fileExt = selectedFile.name.split('.').pop();
            const fileName = `${user.id}/${postData.id}_${Date.now()}.${fileExt}`;

            const { data, error } = await supabase.storage
              .from('customer-posts')
              .upload(fileName, selectedFile);

            if (!error && data) {
              const { data: { publicUrl } } = supabase.storage
                .from('customer-posts')
                .getPublicUrl(data.path);

              // Update post with image URL
              const updateResult = await updateCustomerPost(postData.id, {
                content: content.trim(),
                image_url: publicUrl,
                mentioned_business_ids: [],
              });
              result = updateResult;
            } else {
              result = createResult; // Post created but image upload failed
            }
          } else {
            result = createResult;
          }
        } else {
          result = createResult;
        }
      } else {
        // No file upload needed or URL provided
        result = await createCustomerPost({
          content: content.trim(),
          image_url: imageUrl,
          mentioned_business_ids: [],
        });
      }

      if (result.success) {
        toast.success('Post created successfully!');
        setContent("");
        setImageUrl(null);
        handleImageRemove(); // Clear preview
        setIsExpanded(false);
        onPostCreated?.();
      } else {
        toast.error(result.error || 'Failed to create post');
      }
    } catch (error) {
      console.error('Error creating post:', error);
      toast.error('Failed to create post');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div
      layout
      className="bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-xl shadow-sm overflow-hidden"
    >
      {/* Collapsed State */}
      <AnimatePresence mode="wait">
        {!isExpanded && (
          <motion.div
            key="collapsed"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="p-4"
          >
          <div 
            className="flex items-center gap-3 cursor-pointer"
            onClick={handleExpand}
          >
            <Avatar className="w-10 h-10 ring-2 ring-[#D4AF37]/30">
              <AvatarImage src={customerAvatar || undefined} alt={customerDisplayName} />
              <AvatarFallback className="bg-[#D4AF37] text-white text-sm font-medium">
                {customerDisplayName.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1 bg-gray-50 dark:bg-gray-800 rounded-full px-4 py-3 text-gray-500 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
              What&apos;s on your mind, {customerDisplayName}?
            </div>
          </div>
        </motion.div>
        )}
      </AnimatePresence>

      {/* Expanded State */}
      <AnimatePresence mode="wait">
        {isExpanded && (
          <motion.div
            key="expanded"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="overflow-hidden"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-3">
                <Avatar className="w-10 h-10 ring-2 ring-[#D4AF37]/30">
                  <AvatarImage src={customerAvatar || undefined} alt={customerDisplayName} />
                  <AvatarFallback className="bg-[#D4AF37] text-white text-sm font-medium">
                    {customerDisplayName.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-medium text-gray-900 dark:text-gray-100 text-sm">
                    {customerDisplayName}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Public post
                  </p>
                </div>
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCollapse}
                className="h-8 w-8 p-0 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Content Area */}
            <div className="p-4">
              {/* Posting from location */}
              <div className="mb-3">
                <LocationDisplay />
              </div>

              <textarea
                ref={textareaRef}
                value={content}
                onChange={(e) => handleContentChange(e.target.value)}
                placeholder={`What's on your mind, ${customerDisplayName}?`}
                className="w-full resize-none border-none outline-none text-lg placeholder-gray-500 dark:placeholder-gray-400 bg-transparent text-gray-900 dark:text-gray-100 min-h-[80px]"
                style={{ maxHeight: '300px' }}
              />

              {/* Character Count */}
              {charCount > 0 && (
                <div className={`text-xs mt-2 text-right ${
                  isOverLimit ? 'text-red-500' : 'text-gray-400'
                }`}>
                  {charCount}/{MAX_CHARS}
                </div>
              )}
            </div>

            {/* Image Preview */}
            <AnimatePresence>
              {(previewUrl || detectedImageUrl) && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  className="px-4 pb-4"
                >
                  <div className="relative rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700">
                    <Image
                      src={previewUrl || detectedImageUrl || ''}
                      alt="Post image preview"
                      width={500}
                      height={300}
                      className="w-full h-auto max-h-96 object-cover"
                      onError={handleImageRemove}
                    />
                    <button
                      onClick={handleImageRemove}
                      className="absolute top-2 right-2 bg-black bg-opacity-50 text-white rounded-full p-1.5 hover:bg-opacity-70 transition-all"
                    >
                      <X className="h-4 w-4" />
                    </button>
                    {detectedImageUrl && !previewUrl && (
                      <div className="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                        Auto-detected image
                      </div>
                    )}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>



            {/* Action Bar */}
            <div className="flex items-center justify-between p-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-1">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileSelect}
                  className="hidden"
                  id="image-upload"
                />
                <label htmlFor="image-upload">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-gray-500 hover:text-[#D4AF37] dark:text-gray-400 dark:hover:text-[#D4AF37] h-9 px-3"
                    asChild
                  >
                    <span>
                      <ImageIcon className="h-5 w-5 mr-1" />
                      <span className="text-sm">Photo</span>
                    </span>
                  </Button>
                </label>

                <Button
                  variant="ghost"
                  size="sm"
                  className="text-gray-500 hover:text-red-500 dark:text-gray-400 dark:hover:text-red-400 h-9 px-3"
                  disabled
                >
                  <MapPin className="h-5 w-5 mr-1" />
                  <span className="text-sm">Location</span>
                </Button>
              </div>

              <Button
                onClick={handleSubmit}
                disabled={isSubmitting || (!content.trim() && !previewUrl && !imageUrl && !detectedImageUrl) || isOverLimit}
                className="bg-[#D4AF37] hover:bg-[#B8941F] text-white px-6 py-2 rounded-full font-medium disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Posting...
                  </>
                ) : (
                  <>
                    <Send className="h-4 w-4 mr-2" />
                    Post
                  </>
                )}
              </Button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}
