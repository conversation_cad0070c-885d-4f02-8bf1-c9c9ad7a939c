import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { BusinessDetailsStep } from '@/app/(onboarding)/onboarding/components/steps/BusinessDetailsStep';

// Mock lucide-react icons
jest.mock('lucide-react', () => ({
  Building2: ({ className }: { className: string }) => <svg data-testid="building-icon" className={className} />,
  Mail: ({ className }: { className: string }) => <svg data-testid="mail-icon" className={className} />,
}));

// Explicitly mock react-hook-form
jest.mock('react-hook-form', () => ({
  useForm: () => ({
    control: {},
    handleSubmit: jest.fn(),
    register: jest.fn(),
    setValue: jest.fn(),
    formState: { errors: {} },
    trigger: jest.fn(),
    setError: jest.fn(),
    clearErrors: jest.fn(),
    getValues: jest.fn(),
  }),
  FormProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>, // Simple passthrough
}));

// Mock UI components
jest.mock('@/components/ui/form', () => ({
  FormControl: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  FormItem: ({ children }: { children: React.ReactNode }) => <div data-testid="form-item">{children}</div>,
  FormLabel: ({ children, htmlFor, formLabelHtmlFor }: { children: React.ReactNode, htmlFor?: string, formLabelHtmlFor?: string }) => <label data-testid="form-label" htmlFor={formLabelHtmlFor || htmlFor}>{children}</label>,
  FormMessage: ({ children }: { children: React.ReactNode }) => <div data-testid="form-message">{children}</div>,
  // Mock FormField to provide a controlled 'field' object to its render prop
  FormField: ({ render, name }: { render: ({ field }: any) => React.ReactNode, name: string }) => {
    const mockField = {
      name: name,
      value: '',
      onChange: jest.fn(),
      onBlur: jest.fn(),
    };
    return <div data-testid="form-field">{render({ field: mockField, formLabelHtmlFor: name })}</div>;
  },
}));

jest.mock('@/components/ui/input', () => ({
  Input: ({ className, placeholder, type, onChange, disabled, name, ...props }: any) => (
    <input
      className={className}
      placeholder={placeholder}
      type={type}
      onChange={onChange}
      disabled={disabled}
      name={name}
      id={name}
      {...props}
    />
  ),
}));

describe('BusinessDetailsStep', () => {
  const mockForm = {
    control: {},
    handleSubmit: jest.fn(),
    register: jest.fn(),
    setValue: jest.fn(),
    formState: { errors: {} },
    trigger: jest.fn(),
    setError: jest.fn(),
    clearErrors: jest.fn(),
    getValues: jest.fn(),
  };

  const defaultProps = {
    form: mockForm,
    isSubmitting: false,
    user: null,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders business name and email fields', () => {
    render(<BusinessDetailsStep {...defaultProps} />);

    expect(screen.getByLabelText(/Business Name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Contact Email/i)).toBeInTheDocument();
  });

  it('disables inputs when isSubmitting is true', () => {
    render(<BusinessDetailsStep {...defaultProps} isSubmitting={true} />);

    expect(screen.getByLabelText(/Business Name/i)).toBeDisabled();
    expect(screen.getByLabelText(/Contact Email/i)).toBeDisabled();
  });

  it('displays Google Account tag for Google user', () => {
    const googleUser = { app_metadata: { provider: 'google' } };
    render(<BusinessDetailsStep {...defaultProps} user={googleUser} />);

    expect(screen.getByText('Google Account')).toBeInTheDocument();
    expect(screen.queryByText('Email Account')).not.toBeInTheDocument();
    expect(screen.queryByText('Mobile Account')).not.toBeInTheDocument();
  });

  it('displays Email Account tag for email user', () => {
    const emailUser = { app_metadata: { provider: 'email' } };
    render(<BusinessDetailsStep {...defaultProps} user={emailUser} />);

    expect(screen.getByText('Email Account')).toBeInTheDocument();
    expect(screen.queryByText('Google Account')).not.toBeInTheDocument();
    expect(screen.queryByText('Mobile Account')).not.toBeInTheDocument();
  });

  it('displays Mobile Account tag for phone user', () => {
    const phoneUser = { app_metadata: { provider: 'phone' } };
    render(<BusinessDetailsStep {...defaultProps} user={phoneUser} />);

    expect(screen.getByText('Mobile Account')).toBeInTheDocument();
    expect(screen.queryByText('Google Account')).not.toBeInTheDocument();
    expect(screen.queryByText('Email Account')).not.toBeInTheDocument();
  });

  it('does not display any account tag for unknown user provider', () => {
    const unknownUser = { app_metadata: { provider: 'unknown' } };
    render(<BusinessDetailsStep {...defaultProps} user={unknownUser} />);

    expect(screen.queryByText('Google Account')).not.toBeInTheDocument();
    expect(screen.queryByText('Email Account')).not.toBeInTheDocument();
    expect(screen.queryByText('Mobile Account')).not.toBeInTheDocument();
  });
});