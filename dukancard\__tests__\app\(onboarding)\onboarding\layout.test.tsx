import { render, screen } from '@testing-library/react';
import OnboardingLayout, { generateMetadata } from '@/app/(onboarding)/onboarding/layout';

describe('OnboardingLayout', () => {
  it('renders children correctly', () => {
    render(<OnboardingLayout><div>Test Child</div></OnboardingLayout>);
    expect(screen.getByText('Test Child')).toBeInTheDocument();
  });
});

describe('generateMetadata', () => {
  it('returns the correct metadata', async () => {
    const metadata = await generateMetadata();
    expect(metadata.title).toBe('Onboarding');
    expect(metadata.description).toBe('Complete your Dukancard setup.');
    expect(metadata.robots).toBe('noindex, nofollow');
  });
});
