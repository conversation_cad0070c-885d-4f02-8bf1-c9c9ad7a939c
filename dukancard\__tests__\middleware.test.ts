// Set environment variables before importing middleware
process.env.UPSTASH_REDIS_REST_URL = 'http://mock-redis-url';
process.env.UPSTASH_REDIS_REST_TOKEN = 'mock-redis-token';
process.env.RATE_LIMIT_MAX_REQUESTS = '10';
process.env.RATE_LIMIT_WINDOW_SECONDS = '10';
process.env.PLAYWRIGHT_TESTING = 'false';

import { middleware } from '@/middleware';
import { NextRequest, NextResponse } from 'next/server';
import { updateSession } from '@/utils/supabase/middleware';
import { Ratelimit } from '@upstash/ratelimit';
import { Redis } from '@upstash/redis';

// Mock external dependencies
jest.mock('@/utils/supabase/middleware', () => ({
  updateSession: jest.fn((req) => NextResponse.next()),
}));

jest.mock('@upstash/redis', () => ({
  Redis: jest.fn(() => ({
    // Mock Redis methods used by Ratelimit
    get: jest.fn(),
    set: jest.fn(),
    incr: jest.fn(),
    expire: jest.fn(),
  })),
}));

// Create a mock ratelimit instance
const mockRatelimitInstance = {
  limit: jest.fn(() => Promise.resolve({ success: true, limit: 10, remaining: 9, reset: Date.now() / 1000 + 10 })),
};

jest.mock('@upstash/ratelimit', () => ({
  Ratelimit: jest.fn().mockImplementation(() => mockRatelimitInstance),
}));

// Mock console.error and console.warn to prevent them from cluttering test output
const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});

describe('middleware', () => {
  let mockRequest: NextRequest;

  const createNextUrlMock = (url: URL) => {
    const clonedUrl = new URL(url.toString());
    return {
      pathname: url.pathname,
      hostname: url.hostname,
      protocol: url.protocol,
      searchParams: url.searchParams,
      toString: () => url.toString(),
      clone: () => {
        const newUrl = new URL(url.toString());
        // Make the cloned URL mutable
        return Object.assign(newUrl, {
          hostname: newUrl.hostname,
          protocol: newUrl.protocol,
          pathname: newUrl.pathname
        });
      }
    };
  };

  const createMockRequest = (pathname: string, headers: Record<string, string> = {}, init?: RequestInit) => {
    const url = new URL(pathname, 'https://example.com');
    const headersObj = new Headers(headers);
    const request = new NextRequest(url, { headers: headersObj, ...init });

    // Create a proper nextUrl mock with clone method
    const nextUrlMock = createNextUrlMock(url);

    // Ensure nextUrl is properly set
    Object.defineProperty(request, 'nextUrl', {
      value: nextUrlMock,
      writable: true,
      configurable: true
    });

    // Ensure headers is properly set
    if (!request.headers) {
      Object.defineProperty(request, 'headers', {
        value: headersObj,
        writable: true,
        configurable: true
      });
    }

    return request;
  };

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
    mockRatelimitInstance.limit.mockResolvedValue({ success: true, limit: 10, remaining: 9, reset: Date.now() / 1000 + 10 });

    // Reset environment variables to default test values
    process.env.UPSTASH_REDIS_REST_URL = 'http://mock-redis-url';
    process.env.UPSTASH_REDIS_REST_TOKEN = 'mock-redis-token';
    process.env.PLAYWRIGHT_TESTING = 'false';
    process.env.NODE_ENV = 'development';
    process.env.RATE_LIMIT_MAX_REQUESTS = '10';
    process.env.RATE_LIMIT_WINDOW_SECONDS = '10';
  });

  afterAll(() => {
    consoleErrorSpy.mockRestore();
    consoleWarnSpy.mockRestore();
  });



  describe('Environment Detection', () => {
    it('should not be in test environment', async () => {
      // Override NODE_ENV for this test
      const originalNodeEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';
      process.env.PLAYWRIGHT_TESTING = 'false';

      mockRequest = createMockRequest('/some-path');

      // Check environment detection logic
      const isTestEnvironment = process.env.NODE_ENV === 'test' ||
                               process.env.PLAYWRIGHT_TESTING === 'true' ||
                               mockRequest.headers.get('x-playwright-testing') === 'true';

      expect(isTestEnvironment).toBe(false);
      expect(process.env.NODE_ENV).toBe('production');
      expect(process.env.PLAYWRIGHT_TESTING).toBe('false');
      expect(mockRequest.headers.get('x-playwright-testing')).toBeNull();

      // Restore original NODE_ENV
      process.env.NODE_ENV = originalNodeEnv;
    });
  });

  describe('Domain and HTTPS Redirect Logic', () => {
    it('should redirect www to non-www in production', async () => {
      // Override NODE_ENV for this test
      const originalNodeEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';
      process.env.PLAYWRIGHT_TESTING = 'false';

      mockRequest = createMockRequest('/some-path');
      // Override the URL to test www redirect
      const testUrl = new URL('https://www.example.com/some-path');
      Object.defineProperty(mockRequest, 'nextUrl', {
        value: createNextUrlMock(testUrl),
        writable: true
      });

      const response = await middleware(mockRequest);

      expect(response?.status).toBe(301);
      expect(response?.headers.get('location')).toBe('https://example.com/some-path');

      // Restore original NODE_ENV
      process.env.NODE_ENV = originalNodeEnv;
    });

    it('should redirect http to https in production', async () => {
      Object.defineProperty(process.env, 'NODE_ENV', {
        value: 'production',
        writable: true,
        configurable: true
      });
      process.env.PLAYWRIGHT_TESTING = 'false'; // Ensure we don't go through test environment
      mockRequest = createMockRequest('/some-path');
      // Override the URL to test http redirect
      const testUrl = new URL('http://example.com/some-path');
      Object.defineProperty(mockRequest, 'nextUrl', {
        value: createNextUrlMock(testUrl),
        writable: true
      });

      const response = await middleware(mockRequest);

      expect(response?.status).toBe(301);
      expect(response?.headers.get('location')).toBe('https://example.com/some-path');
    });

    it('should redirect both www and http to non-www https in production', async () => {
      Object.defineProperty(process.env, 'NODE_ENV', {
        value: 'production',
        writable: true,
        configurable: true
      });
      process.env.PLAYWRIGHT_TESTING = 'false'; // Ensure we don't go through test environment
      mockRequest = createMockRequest('/some-path');
      // Override the URL to test both www and http redirect
      const testUrl = new URL('http://www.example.com/some-path');
      Object.defineProperty(mockRequest, 'nextUrl', {
        value: createNextUrlMock(testUrl),
        writable: true
      });

      const response = await middleware(mockRequest);

      expect(response?.status).toBe(301);
      expect(response?.headers.get('location')).toBe('https://example.com/some-path');
    });

    it('should not redirect in development environment', async () => {
      Object.defineProperty(process.env, 'NODE_ENV', {
        value: 'development',
        writable: true,
        configurable: true
      });
      process.env.PLAYWRIGHT_TESTING = 'false'; // Ensure we don't go through test environment
      mockRequest = createMockRequest('/some-path');
      // Override the URL to test development environment
      const testUrl = new URL('http://www.localhost:3000/some-path');
      Object.defineProperty(mockRequest, 'nextUrl', {
        value: createNextUrlMock(testUrl),
        writable: true
      });

      const response = await middleware(mockRequest);

      expect(response?.status).not.toBe(301);
      expect(updateSession).toHaveBeenCalledWith(mockRequest);
    });

    it('should not redirect for development domains in production', async () => {
      Object.defineProperty(process.env, 'NODE_ENV', {
        value: 'production',
        writable: true,
        configurable: true
      });
      process.env.PLAYWRIGHT_TESTING = 'false'; // Ensure we don't go through test environment
      mockRequest = createMockRequest('/some-path');
      // Override the URL to test localhost in production
      const testUrl = new URL('https://localhost:3000/some-path');
      Object.defineProperty(mockRequest, 'nextUrl', {
        value: createNextUrlMock(testUrl),
        writable: true
      });

      const response = await middleware(mockRequest);

      expect(response?.status).not.toBe(301);
      expect(updateSession).toHaveBeenCalledWith(mockRequest);
    });
  });

  describe('Rate Limiting Logic', () => {
    it('should apply rate limiting to API routes', async () => {
      mockRequest = createMockRequest('/api/data', {
        'x-forwarded-for': '***********',
      });
      mockRatelimitInstance.limit.mockResolvedValue({ success: true, limit: 10, remaining: 9, reset: Date.now() / 1000 + 10 });

      await middleware(mockRequest);

      expect(mockRatelimitInstance.limit).toHaveBeenCalledWith('***********');
    });

    it('should return 429 if rate limit is exceeded', async () => {
      mockRequest = createMockRequest('/api/data', {
        'x-forwarded-for': '***********',
      });
      mockRatelimitInstance.limit.mockResolvedValue({ success: false, limit: 10, remaining: 0, reset: Date.now() / 1000 + 10 });

      const response = await middleware(mockRequest);

      expect(response?.status).toBe(429);
      expect(response?.headers.get('X-RateLimit-Limit')).toBe('10');
      expect(response?.headers.get('X-RateLimit-Remaining')).toBe('0');
    });

    it('should not apply rate limiting to non-API routes', async () => {
      mockRequest = createMockRequest('/some-page');

      await middleware(mockRequest);

      expect(mockRatelimitInstance.limit).not.toHaveBeenCalled();
    });

    it('should not apply rate limiting to API webhooks', async () => {
      mockRequest = createMockRequest('/api/webhooks/stripe');

      await middleware(mockRequest);

      expect(mockRatelimitInstance.limit).not.toHaveBeenCalled();
    });

    it('should warn and skip rate limiting if Redis is not configured', async () => {
      process.env.UPSTASH_REDIS_REST_URL = '';
      process.env.UPSTASH_REDIS_REST_TOKEN = '';

      mockRequest = createMockRequest('/api/data');

      await middleware(mockRequest);

      expect(consoleWarnSpy).toHaveBeenCalledWith('Rate limiting skipped: Redis not configured');
      expect(mockRatelimitInstance.limit).not.toHaveBeenCalled();
    });

    it('should allow request to proceed if rate limiting throws an error', async () => {
      mockRequest = createMockRequest('/api/data');
      mockRatelimitInstance.limit.mockRejectedValue(new Error('Redis connection error'));

      const response = await middleware(mockRequest);

      expect(consoleErrorSpy).toHaveBeenCalledWith('Rate limiting error:', expect.any(Error));
      expect(response?.status).not.toBe(429);
      expect(updateSession).toHaveBeenCalled(); // Should still proceed to updateSession
    });
  });

  describe('Supabase Session Update', () => {
    it('should call updateSession at the end of the middleware chain', async () => {
      const mockUpdateSessionResponse = NextResponse.json({ message: 'Session updated' });
      (updateSession as jest.Mock).mockResolvedValue(mockUpdateSessionResponse);
      mockRequest = createMockRequest('/some-page');

      const response = await middleware(mockRequest);

      expect(updateSession).toHaveBeenCalledWith(mockRequest);
      expect(response).toEqual(mockUpdateSessionResponse);
    });
  });


});