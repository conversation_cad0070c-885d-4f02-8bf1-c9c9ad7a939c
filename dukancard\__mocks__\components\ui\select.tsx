import React from 'react';

export const Select = ({ children, onValueChange, value, disabled }: any) => (
  <select onChange={(e) => onValueChange(e.target.value)} value={value} disabled={disabled} data-testid="select-mock">
    {children}
  </select>
);
export const SelectContent = ({ children }: any) => <div data-testid="select-content-mock">{children}</div>;
export const SelectItem = ({ children, value }: any) => <option value={value} data-testid="select-item-mock">{children}</option>;
export const SelectTrigger = ({ children }: any) => <button data-testid="select-trigger-mock">{children}</button>;
export const SelectValue = ({ placeholder }: any) => <span data-testid="select-value-mock">{placeholder}</span>;
