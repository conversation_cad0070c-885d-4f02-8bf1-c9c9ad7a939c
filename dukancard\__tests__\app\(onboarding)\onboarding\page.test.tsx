import React from 'react';
import { render, screen } from '@testing-library/react';
import OnboardingPage from '@/app/(onboarding)/onboarding/page';

// Mock the OnboardingClient component
jest.mock('@/app/(onboarding)/onboarding/OnboardingClient', () => {
  return jest.fn(({ redirectSlug, message }) => (
    <div data-testid="onboarding-client-mock">
      OnboardingClient Mock
      <span data-testid="redirect-slug">{redirectSlug}</span>
      <span data-testid="message">{message}</span>
    </div>
  ));
});

describe('OnboardingPage', () => {
  it('renders OnboardingClient with correct searchParams', async () => {
    const mockSearchParams = {
      redirect: 'test-redirect',
      message: 'test-message',
    };

    render(await OnboardingPage({ searchParams: Promise.resolve(mockSearchParams) }));

    expect(screen.getByTestId('onboarding-client-mock')).toBeInTheDocument();
    expect(screen.getByTestId('redirect-slug')).toHaveTextContent('test-redirect');
    expect(screen.getByTestId('message')).toHaveTextContent('test-message');
  });

  it('renders OnboardingClient with null searchParams if not provided', async () => {
    const mockSearchParams = {};

    render(await OnboardingPage({ searchParams: Promise.resolve(mockSearchParams) }));

    expect(screen.getByTestId('onboarding-client-mock')).toBeInTheDocument();
    expect(screen.getByTestId('redirect-slug')).toHaveTextContent('');
    expect(screen.getByTestId('message')).toHaveTextContent('');
  });

  it('renders the main div with correct classes', async () => {
    const mockSearchParams = {};

    render(await OnboardingPage({ searchParams: Promise.resolve(mockSearchParams) }));

    const mainDiv = screen.getByTestId('onboarding-client-mock').parentElement;
    expect(mainDiv).toHaveClass('w-full', 'min-h-[calc(100vh-80px)]', 'md:min-h-[calc(100vh-64px)]', 'flex', 'items-center', 'justify-center', 'pt-6', 'pb-20', 'md:pb-6');
  });
});
