import React from 'react';
import { View } from 'react-native';

const createMockIcon = (name) => {
  return React.forwardRef(({ size, color, ...props }, ref) => (
    <View testID={`${name}-icon`} {...props} ref={ref} />
  ));
};

module.exports = {
  Mail: createMockIcon('Mail'),
  Lock: createMockIcon('Lock'),
  Eye: createMockIcon('Eye'),
  EyeOff: createMockIcon('EyeOff'),
  Phone: createMockIcon('Phone'),
  X: createMockIcon('X'),
  // Add other icons as needed
};
