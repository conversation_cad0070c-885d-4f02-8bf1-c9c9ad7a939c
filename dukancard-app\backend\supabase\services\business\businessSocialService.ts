import { supabase } from "@/lib/supabase";
import { ReviewsRow } from "@/types/database/reviews";
import { BusinessProfilesRow } from "@/types/database/business-profiles";

// Define interfaces for the expected data structure
interface BusinessProfileDataForLike {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
  address_line: string | null;
}

interface CustomerProfileDataForLike {
  id: string;
  name: string | null;
  email: string | null;
  avatar_url: string | null;
}

export interface BusinessLikeReceived {
  id: string;
  user_id: string;
  customer_profiles?: CustomerProfileDataForLike | null;
  business_profiles?: BusinessProfileDataForLike | null;
  profile_type: "customer" | "business";
}

export interface BusinessMyLike {
  id: string;
  business_profiles: BusinessProfileDataForLike | null;
}

interface LikesReceivedResult {
  items: BusinessLikeReceived[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
}

interface MyLikesResult {
  items: BusinessMyLike[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
}

/**
 * Fetch likes received by a business (customers/businesses who liked this business)
 */
export async function fetchBusinessLikesReceived(
  businessId: string,
  page: number = 1,
  limit: number = 10
): Promise<LikesReceivedResult> {
  try {
    // Get total count first
    const { count: totalCount, error: countError } = await supabase
      .from("likes")
      .select("id", { count: "exact", head: true })
      .eq("business_profile_id", businessId);

    if (countError) {
      throw new Error("Failed to get total count");
    }

    if (!totalCount || totalCount === 0) {
      return {
        items: [],
        totalCount: 0,
        hasMore: false,
        currentPage: page,
      };
    }

    // Get likes with pagination (database-level pagination)
    const from = (page - 1) * limit;
    const { data: likes, error: likesError } = await supabase
      .from("likes")
      .select("id, user_id")
      .eq("business_profile_id", businessId)
      .range(from, from + limit - 1);

    if (likesError) {
      throw new Error("Failed to fetch likes");
    }

    if (!likes || likes.length === 0) {
      return {
        items: [],
        totalCount,
        hasMore: false,
        currentPage: page,
      };
    }

    // Get user IDs for the paginated results only
    const userIds = likes.map((like) => like.user_id);

    // Fetch customer and business profiles for paginated results only
    const [customerProfiles, businessProfiles] = await Promise.all([
      supabase
        .from("customer_profiles")
        .select("id, name, email, avatar_url")
        .in("id", userIds),
      supabase
        .from("business_profiles")
        .select(
          "id, business_name, business_slug, logo_url, city, state, pincode, address_line"
        )
        .in("id", userIds),
    ]);

    // Create maps for easy lookup
    const customerProfilesMap = new Map(
      customerProfiles.data?.map((profile) => [profile.id, profile]) || []
    );
    const businessProfilesMap = new Map(
      businessProfiles.data?.map((profile) => [profile.id, profile]) || []
    );

    // Combine likes with their corresponding profiles
    const processedLikes = likes
      .map((like) => {
        const customerProfile = customerProfilesMap.get(like.user_id);
        const businessProfile = businessProfilesMap.get(like.user_id);

        if (customerProfile) {
          return {
            id: like.id,
            user_id: like.user_id,
            customer_profiles: customerProfile,
            profile_type: "customer" as const,
          };
        } else if (businessProfile) {
          return {
            id: like.id,
            user_id: like.user_id,
            business_profiles: businessProfile,
            profile_type: "business" as const,
          };
        }
        return null;
      })
      .filter((item) => item !== null) as BusinessLikeReceived[];

    const hasMore = totalCount > from + limit;

    return {
      items: processedLikes,
      totalCount,
      hasMore,
      currentPage: page,
    };
  } catch (error) {
    throw error;
  }
}

/**
 * Fetch businesses that this business has liked
 */
export async function fetchBusinessMyLikes(
  businessId: string,
  page: number = 1,
  limit: number = 10,
  searchTerm: string = ""
): Promise<MyLikesResult> {
  try {
    // Build the query with proper joins and filtering
    let query = supabase
      .from("likes")
      .select(
        `
        id,
        business_profiles!inner (
          id,
          business_name,
          business_slug,
          logo_url,
          city,
          state,
          pincode,
          address_line
        )
      `
      )
      .eq("user_id", businessId);

    // Apply search filter if provided
    if (searchTerm) {
      query = query.ilike("business_profiles.business_name", `%${searchTerm}%`);
    }

    // Get total count for pagination with proper join for search
    let countQuery = supabase
      .from("likes")
      .select(
        `
        id,
        business_profiles!inner (
          id,
          business_name
        )
      `,
        { count: "exact", head: true }
      )
      .eq("user_id", businessId);

    // Apply search filter to count query if provided
    if (searchTerm) {
      countQuery = countQuery.ilike(
        "business_profiles.business_name",
        `%${searchTerm}%`
      );
    }

    const { count: totalCount, error: countError } = await countQuery;

    if (countError) {
      throw new Error("Failed to get total count");
    }

    // If no likes, return empty result
    if (!totalCount || totalCount === 0) {
      return {
        items: [],
        totalCount: 0,
        hasMore: false,
        currentPage: page,
      };
    }

    // Apply pagination to the query
    const from = (page - 1) * limit;
    query = query.range(from, from + limit - 1);

    const { data: likesWithProfiles, error: likesError } = await query;

    if (likesError) {
      throw new Error("Failed to fetch likes");
    }

    // Transform the data to match BusinessMyLike interface
    const transformedItems: BusinessMyLike[] = (likesWithProfiles || []).map(
      (item) => ({
        id: item.id,
        business_profiles: Array.isArray(item.business_profiles)
          ? item.business_profiles[0] || null
          : item.business_profiles,
      })
    );

    const hasMore = totalCount > from + limit;

    return {
      items: transformedItems,
      totalCount,
      hasMore,
      currentPage: page,
    };
  } catch (error) {
    console.error("Error in fetchBusinessMyLikes:", error);
    throw error;
  }
}

// Define types for follower data (can be customer or business)
export interface FollowerProfileData {
  id: string;
  name: string | null;
  slug: string | null;
  logo_url?: string | null;
  avatar_url?: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
  address_line: string | null;
  type: "business" | "customer";
}

export interface FollowerWithProfile {
  id: string;
  profile: FollowerProfileData | null;
}

export interface FollowersResult {
  items: FollowerWithProfile[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
}

// Define types for businesses that this business follows
export interface BusinessFollowingData {
  id: string;
  business_name: string | null;
  business_slug: string | null;
  logo_url: string | null;
  city: string | null;
  state: string | null;
  pincode: string | null;
  address_line: string | null;
}

export interface BusinessFollowingWithProfile {
  id: string;
  business_profiles: BusinessFollowingData | null;
}

export interface BusinessFollowingResult {
  items: BusinessFollowingWithProfile[];
  totalCount: number;
  hasMore: boolean;
  currentPage: number;
}

/**
 * Fetch followers to a business (both customers and other businesses)
 */
export async function fetchBusinessFollowers(
  businessId: string,
  page: number = 1,
  limit: number = 10
): Promise<FollowersResult> {
  try {
    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Get total count first
    const { count: totalCount, error: countError } = await supabase
      .from("subscriptions")
      .select("*", { count: "exact", head: true })
      .eq("business_profile_id", businessId);

    if (countError) {
      throw new Error("Failed to fetch subscription count");
    }

    if (!totalCount || totalCount === 0) {
      return {
        items: [],
        totalCount: 0,
        hasMore: false,
        currentPage: page,
      };
    }

    // Get paginated subscriptions (database-level pagination)
    const { data: subscriptions, error: subsError } = await supabase
      .from("subscriptions")
      .select("id, user_id, created_at")
      .eq("business_profile_id", businessId)
      .order("created_at", { ascending: false })
      .range(offset, offset + limit - 1);

    if (subsError) {
      throw new Error("Failed to fetch subscriptions");
    }

    if (!subscriptions || subscriptions.length === 0) {
      return {
        items: [],
        totalCount: totalCount || 0,
        hasMore: false,
        currentPage: page,
      };
    }

    // Get user IDs from the paginated subscriptions only
    const userIds = subscriptions.map((sub) => sub.user_id);

    // Fetch profiles for only the paginated user IDs (not all users)
    const [customerProfiles, businessProfiles] = await Promise.all([
      supabase
        .from("customer_profiles")
        .select("id, name, avatar_url")
        .in("id", userIds),
      supabase
        .from("business_profiles")
        .select(
          "id, business_name, business_slug, logo_url, city, state, pincode, address_line"
        )
        .in("id", userIds),
    ]);

    if (customerProfiles.error) {
      throw new Error("Failed to fetch customer profiles");
    }

    if (businessProfiles.error) {
      throw new Error("Failed to fetch business profiles");
    }

    // Create profile maps for efficient lookup
    const customerMap = new Map(
      customerProfiles.data?.map((p) => [p.id, p]) || []
    );
    const businessMap = new Map(
      businessProfiles.data?.map((p) => [p.id, p]) || []
    );

    // Transform subscriptions to include profile data
    const allFollowers: FollowerWithProfile[] = subscriptions
      .map((sub) => {
        const customerProfile = customerMap.get(sub.user_id);
        const businessProfile = businessMap.get(sub.user_id);

        if (customerProfile) {
          return {
            id: sub.id,
            profile: {
              id: customerProfile.id,
              name: customerProfile.name,
              slug: null,
              avatar_url: customerProfile.avatar_url,
              city: null,
              state: null,
              pincode: null,
              address_line: null,
              type: "customer" as const,
            },
          };
        } else if (businessProfile) {
          return {
            id: sub.id,
            profile: {
              id: businessProfile.id,
              name: businessProfile.business_name,
              slug: businessProfile.business_slug,
              logo_url: businessProfile.logo_url,
              city: businessProfile.city,
              state: businessProfile.state,
              pincode: businessProfile.pincode,
              address_line: businessProfile.address_line,
              type: "business" as const,
            },
          };
        }
        return null;
      })
      .filter((sub) => sub !== null) as FollowerWithProfile[];

    // Calculate hasMore based on database-level pagination
    const hasMore = totalCount > offset + limit;

    return {
      items: allFollowers,
      totalCount: totalCount || 0,
      hasMore,
      currentPage: page,
    };
  } catch (error) {
    throw error;
  }
}

/**
 * Fetch businesses that this business follows
 */
export async function fetchBusinessFollowing(
  businessId: string,
  page: number = 1,
  limit: number = 10,
  searchTerm: string = ""
): Promise<BusinessFollowingResult> {
  try {
    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Build a complex query that joins subscriptions with business_profiles
    // This ensures proper database-level pagination with search filtering
    let query = supabase
      .from("subscriptions")
      .select(
        `
        id,
        business_profile_id,
        business_profiles!inner (
          id,
          business_name,
          business_slug,
          logo_url,
          city,
          state,
          pincode,
          address_line
        )
      `,
        { count: "exact" }
      )
      .eq("user_id", businessId);

    // Apply search filter if provided
    if (searchTerm && searchTerm.trim()) {
      query = query.ilike(
        "business_profiles.business_name",
        `%${searchTerm.trim()}%`
      );
    }

    // Apply pagination at database level
    query = query.range(offset, offset + limit - 1);

    // Execute the query
    const {
      data: subscriptionsWithProfiles,
      count: totalCount,
      error: queryError,
    } = await query;

    if (queryError) {
      throw new Error("Failed to fetch business following data");
    }

    // Transform the data to match the expected interface
    let transformedSubscriptions: BusinessFollowingWithProfile[] = [];

    if (subscriptionsWithProfiles && subscriptionsWithProfiles.length > 0) {
      transformedSubscriptions = subscriptionsWithProfiles.map((sub) => ({
        id: sub.id,
        business_profiles: Array.isArray(sub.business_profiles)
          ? sub.business_profiles[0]
          : sub.business_profiles,
      }));
    }

    // Calculate hasMore based on database-level pagination
    const hasMore = totalCount ? totalCount > offset + limit : false;

    return {
      items: transformedSubscriptions,
      totalCount: totalCount || 0,
      hasMore,
      currentPage: page,
    };
  } catch (error) {
    throw error;
  }
}
