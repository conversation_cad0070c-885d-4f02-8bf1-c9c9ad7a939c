/**
 * Sort mappings utility functions
 * Based on dukancard/app/(main)/discover/utils/sortMappings.ts
 */

import { ProductSortOption, BusinessSortBy } from "@/src/types/discovery";

/**
 * Get display name for product sort option
 */
export function getProductSortDisplayName(sortOption: ProductSortOption): string {
  switch (sortOption) {
    case "newest":
      return "Newest First";
    case "name_asc":
      return "Name (A-Z)";
    case "name_desc":
      return "Name (Z-A)";
    case "price_low":
      return "Price (Low to High)";
    case "price_high":
      return "Price (High to Low)";
    default:
      return "Newest First";
  }
}

/**
 * Get display name for business sort option
 */
export function getBusinessSortDisplayName(sortOption: BusinessSortBy): string {
  switch (sortOption) {
    case "created_desc":
      return "Newest First";
    case "created_asc":
      return "Oldest First";
    case "name_asc":
      return "Name (A-Z)";
    case "name_desc":
      return "Name (Z-A)";
    case "likes_desc":
      return "Most Liked";
    case "likes_asc":
      return "Least Liked";
    case "subscriptions_desc":
      return "Most Subscribed";
    case "subscriptions_asc":
      return "Least Subscribed";
    case "rating_desc":
      return "Highest Rated";
    case "rating_asc":
      return "Lowest Rated";
    default:
      return "Newest First";
  }
}

/**
 * Get sort display name based on view type
 */
export function getSortDisplayName(
  viewType: "products" | "cards",
  productSortBy: ProductSortOption,
  businessSortBy: BusinessSortBy
): string {
  if (viewType === "products") {
    return getProductSortDisplayName(productSortBy);
  } else {
    return getBusinessSortDisplayName(businessSortBy);
  }
}
