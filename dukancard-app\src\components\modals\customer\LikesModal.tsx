import React, { useState } from "react";
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  TextInput,
} from "react-native";
import { X, Search } from "lucide-react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { createLikesModalStyles } from "@/styles/modals/customer/likes-modal";
import LikesList from "./components/LikesList";


interface LikesModalProps {
  visible: boolean;
  onClose: () => void;
}

export default function LikesModal({ visible, onClose }: LikesModalProps) {
  const theme = useTheme();
  const styles = createLikesModalStyles(theme);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeSearchTerm, setActiveSearchTerm] = useState("");

  const handleSearch = () => {
    setActiveSearchTerm(searchTerm);
  };

  const handleSearchSubmit = () => {
    handleSearch();
  };

  return (
    <Modal
      visible={visible}
      onRequestClose={onClose}
      presentationStyle="fullScreen"
      animationType="slide"
    >
      <SafeAreaView style={styles.safeArea}>
        <KeyboardAvoidingView
          style={{ flex: 1 }}
          behavior={Platform.OS === "ios" ? "padding" : "height"}
        >
          <View style={styles.header}>
            <View style={{ width: 40 }} />
            <Text style={styles.headerTitle}>My Likes</Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color={theme.isDark ? "#FFF" : "#000"} />
            </TouchableOpacity>
          </View>
          <View style={styles.contentContainer}>
            <View style={styles.searchContainer}>
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  backgroundColor: theme.isDark
                    ? theme.colors.input
                    : theme.colors.card,
                  borderRadius: theme.borderRadius.md,
                  paddingHorizontal: theme.spacing.md,
                  borderWidth: 1,
                  borderColor: theme.colors.border,
                }}
              >
                <Search
                  size={20}
                  color={theme.colors.textSecondary}
                  style={{ marginRight: theme.spacing.sm }}
                />
                <TextInput
                  style={{
                    flex: 1,
                    height: 48,
                    color: theme.colors.foreground,
                    fontSize: theme.typography.fontSize.base,
                  }}
                  placeholder="Search by business name..."
                  placeholderTextColor={theme.colors.textSecondary}
                  value={searchTerm}
                  onChangeText={setSearchTerm}
                  onSubmitEditing={handleSearchSubmit}
                  returnKeyType="search"
                />
                {searchTerm.length > 0 && (
                  <TouchableOpacity
                    onPress={handleSearch}
                    style={{
                      padding: theme.spacing.xs,
                      marginLeft: theme.spacing.xs,
                    }}
                  >
                    <Search size={20} color={theme.colors.primary} />
                  </TouchableOpacity>
                )}
              </View>
            </View>
            <LikesList searchTerm={activeSearchTerm} />
          </View>
        </KeyboardAvoidingView>
      </SafeAreaView>
    </Modal>
  );
}
