import { StyleSheet } from "react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { responsiveFontSize } from "@/lib/theme/colors";

export const createSettingsModalStyles = (
  theme: ReturnType<typeof useTheme>
) => {
  return StyleSheet.create({
    container: {
      flex: 1,
      padding: theme.spacing.md,
    },
    section: {
      marginBottom: theme.spacing.lg,
    },
    sectionTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: "600",
      marginBottom: theme.spacing.md,
      color: theme.colors.textPrimary,
    },
    settingItem: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingVertical: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    settingLeft: {
      flexDirection: "row",
      alignItems: "center",
      flex: 1,
    },
    settingIcon: {
      marginRight: theme.spacing.md,
    },
    settingContent: {
      flex: 1,
    },
    settingTitle: {
      fontSize: theme.typography.fontSize.base,
      fontWeight: "500",
      color: theme.colors.textPrimary,
    },
    settingSubtitle: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
      marginTop: theme.spacing.xs,
    },
    formContainer: {
      paddingHorizontal: theme.spacing.md,
    },
    input: {
      marginBottom: theme.spacing.md,
    },
    button: {
      marginTop: theme.spacing.md,
    },
    buttonText: {
      color: "#FFFFFF",
      textAlign: "center",
      fontWeight: "600",
    },
    deleteAccountButton: {
      borderColor: theme.colors.destructive,
      borderWidth: 1,
      backgroundColor: "transparent",
    },
    deleteAccountButtonText: {
      color: theme.colors.destructive,
    },
    centeredView: {
      flex: 1,
      justifyContent: "center",
      alignItems: "center",
      marginTop: 22,
    },
    modalView: {
      margin: 20,
      backgroundColor: "white",
      borderRadius: 20,
      padding: 35,
      alignItems: "center",
      shadowColor: "#000",
      shadowOffset: {
        width: 0,
        height: 2,
      },
      shadowOpacity: 0.25,
      shadowRadius: 4,
      elevation: 5,
    },
  });
};
