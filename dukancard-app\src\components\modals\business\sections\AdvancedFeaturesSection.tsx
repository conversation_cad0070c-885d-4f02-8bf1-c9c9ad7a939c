import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from "react-native";
import { Crown, Star, Zap, Shield, Palette, Image as ImageIcon } from "lucide-react-native";
import { useTheme } from "@/src/hooks/useTheme";
import { useToast } from "@/src/components/ui/Toast";
import { createAdvancedFeaturesSectionStyles } from "@/styles/modals/business/sections/advanced-features-section";

interface AdvancedFeaturesSectionProps {
  onBack: () => void;
}

// Premium features data
const PREMIUM_FEATURES = [
  {
    id: "custom-branding",
    title: "Custom Branding",
    description: "Upload custom header images and advanced branding options",
    icon: Palette,
    color: "#8B5CF6",
    isPremium: true,
  },
  {
    id: "analytics",
    title: "Advanced Analytics",
    description: "Track views, clicks, and customer engagement metrics",
    icon: Star,
    color: "#F59E0B",
    isPremium: true,
  },
  {
    id: "custom-domain",
    title: "Custom Domain",
    description: "Use your own domain for your business card URL",
    icon: Zap,
    color: "#10B981",
    isPremium: true,
  },
  {
    id: "priority-support",
    title: "Priority Support",
    description: "Get faster response times and dedicated support",
    icon: Shield,
    color: "#3B82F6",
    isPremium: true,
  },
  {
    id: "custom-themes",
    title: "Custom Themes",
    description: "Access to premium themes and layout options",
    icon: ImageIcon,
    color: "#EC4899",
    isPremium: true,
  },
];

export default function AdvancedFeaturesSection({ onBack }: AdvancedFeaturesSectionProps) {
  const theme = useTheme();
  const styles = createAdvancedFeaturesSectionStyles(theme);
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const handleUpgradePress = () => {
    toast.info("Coming Soon", "Premium features will be available soon!");
  };

  const handleFeaturePress = (featureId: string) => {
    toast.info("Premium Feature", "This feature requires a premium subscription.");
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {/* Premium Header */}
        <View style={styles.premiumHeader}>
          <View style={styles.premiumIconContainer}>
            <Crown size={32} color="#D4AF37" />
          </View>
          <Text style={styles.premiumTitle}>Premium Features</Text>
          <Text style={styles.premiumSubtitle}>
            Unlock advanced features to take your business card to the next level
          </Text>
        </View>

        {/* Current Plan Status */}
        <View style={styles.planCard}>
          <View style={styles.planHeader}>
            <Text style={styles.planTitle}>Current Plan: Free</Text>
            <View style={styles.planBadge}>
              <Text style={styles.planBadgeText}>Basic</Text>
            </View>
          </View>
          <Text style={styles.planDescription}>
            You&apos;re currently on the free plan with basic features.
          </Text>
          <TouchableOpacity
            style={styles.upgradeButton}
            onPress={handleUpgradePress}
          >
            <Crown size={16} color="white" />
            <Text style={styles.upgradeButtonText}>Upgrade to Premium</Text>
          </TouchableOpacity>
        </View>

        {/* Premium Features List */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Premium Features</Text>
          
          {PREMIUM_FEATURES.map((feature) => (
            <TouchableOpacity
              key={feature.id}
              style={styles.featureCard}
              onPress={() => handleFeaturePress(feature.id)}
            >
              <View style={styles.featureHeader}>
                <View style={[styles.featureIconContainer, { backgroundColor: `${feature.color}20` }]}>
                  <feature.icon size={24} color={feature.color} />
                </View>
                <View style={styles.featureInfo}>
                  <View style={styles.featureTitleRow}>
                    <Text style={styles.featureTitle}>{feature.title}</Text>
                    {feature.isPremium && (
                      <View style={styles.premiumBadge}>
                        <Crown size={12} color="#D4AF37" />
                        <Text style={styles.premiumBadgeText}>PRO</Text>
                      </View>
                    )}
                  </View>
                  <Text style={styles.featureDescription}>{feature.description}</Text>
                </View>
              </View>
            </TouchableOpacity>
          ))}
        </View>

        {/* Benefits Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Why Upgrade?</Text>
          
          <View style={styles.benefitsList}>
            <View style={styles.benefitItem}>
              <Text style={styles.benefitBullet}>✨</Text>
              <Text style={styles.benefitText}>
                Stand out with custom branding and professional themes
              </Text>
            </View>
            <View style={styles.benefitItem}>
              <Text style={styles.benefitBullet}>📊</Text>
              <Text style={styles.benefitText}>
                Track performance with detailed analytics and insights
              </Text>
            </View>
            <View style={styles.benefitItem}>
              <Text style={styles.benefitBullet}>🚀</Text>
              <Text style={styles.benefitText}>
                Get priority support and faster feature updates
              </Text>
            </View>
            <View style={styles.benefitItem}>
              <Text style={styles.benefitBullet}>🎯</Text>
              <Text style={styles.benefitText}>
                Use your own domain for a professional presence
              </Text>
            </View>
          </View>
        </View>

        {/* Pricing Preview */}
        <View style={styles.pricingCard}>
          <Text style={styles.pricingTitle}>Premium Plan</Text>
          <View style={styles.pricingRow}>
            <Text style={styles.pricingAmount}>₹299</Text>
            <Text style={styles.pricingPeriod}>/month</Text>
          </View>
          <Text style={styles.pricingDescription}>
            All premium features included. Cancel anytime.
          </Text>
          <TouchableOpacity
            style={styles.pricingButton}
            onPress={handleUpgradePress}
          >
            <Text style={styles.pricingButtonText}>Start Free Trial</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.infoBox}>
          <Text style={styles.infoText}>
            💡 Premium features are coming soon! We&apos;re working hard to bring you advanced customization and analytics tools.
          </Text>
        </View>
      </ScrollView>

      {/* Back Button */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[
            styles.backButton,
            { borderColor: theme.colors.border },
          ]}
          onPress={onBack}
        >
          <Text style={[styles.backButtonText, { color: theme.colors.foreground }]}>
            Back to Menu
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}
